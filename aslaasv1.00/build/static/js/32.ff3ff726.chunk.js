(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[32],{1019:function(e,t,r){"use strict";r.d(t,"b",(function(){return o}));var i=r(559),n=r(525);function o(e){return Object(n.a)("MuiListItemButton",e)}const s=Object(i.a)("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);t.a=s},1058:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=r(42),a=r(558),l=r(1209),c=r(566),u=r(49),h=r(69),d=r(1403),f=r(678),p=r(232),b=r(230),g=r(609),m=r(559),y=r(525);function v(e){return Object(y.a)("MuiListItem",e)}var w=Object(m.a)("MuiListItem",["root","container","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","padding","button","secondaryAction","selected"]),_=r(1019);function O(e){return Object(y.a)("MuiListItemSecondaryAction",e)}Object(m.a)("MuiListItemSecondaryAction",["root","disableGutters"]);var j=r(2);const S=["className"],E=Object(u.a)("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.disableGutters&&t.disableGutters]}})((e=>{let{ownerState:t}=e;return Object(n.a)({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)"},t.disableGutters&&{right:0})})),A=o.forwardRef((function(e,t){const r=Object(h.a)({props:e,name:"MuiListItemSecondaryAction"}),{className:l}=r,c=Object(i.a)(r,S),u=o.useContext(g.a),d=Object(n.a)({},r,{disableGutters:u.disableGutters}),f=(e=>{const{disableGutters:t,classes:r}=e,i={root:["root",t&&"disableGutters"]};return Object(a.a)(i,O,r)})(d);return Object(j.jsx)(E,Object(n.a)({className:Object(s.a)(f.root,l),ownerState:d,ref:t},c))}));A.muiName="ListItemSecondaryAction";var x=A;const k=["className"],I=["alignItems","autoFocus","button","children","className","component","components","componentsProps","ContainerComponent","ContainerProps","dense","disabled","disableGutters","disablePadding","divider","focusVisibleClassName","secondaryAction","selected","slotProps","slots"],T=Object(u.a)("div",{name:"MuiListItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.dense&&t.dense,"flex-start"===r.alignItems&&t.alignItemsFlexStart,r.divider&&t.divider,!r.disableGutters&&t.gutters,!r.disablePadding&&t.padding,r.button&&t.button,r.hasSecondaryAction&&t.secondaryAction]}})((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left"},!r.disablePadding&&Object(n.a)({paddingTop:8,paddingBottom:8},r.dense&&{paddingTop:4,paddingBottom:4},!r.disableGutters&&{paddingLeft:16,paddingRight:16},!!r.secondaryAction&&{paddingRight:48}),!!r.secondaryAction&&{["& > .".concat(_.a.root)]:{paddingRight:48}},{["&.".concat(w.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(w.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):Object(c.a)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(w.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):Object(c.a)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(w.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity}},"flex-start"===r.alignItems&&{alignItems:"flex-start"},r.divider&&{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"},r.button&&{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(w.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):Object(c.a)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):Object(c.a)(t.palette.primary.main,t.palette.action.selectedOpacity)}}},r.hasSecondaryAction&&{paddingRight:48})})),C=Object(u.a)("li",{name:"MuiListItem",slot:"Container",overridesResolver:(e,t)=>t.container})({position:"relative"}),R=o.forwardRef((function(e,t){const r=Object(h.a)({props:e,name:"MuiListItem"}),{alignItems:c="center",autoFocus:u=!1,button:m=!1,children:y,className:_,component:O,components:S={},componentsProps:E={},ContainerComponent:A="li",ContainerProps:{className:R}={},dense:P=!1,disabled:M=!1,disableGutters:B=!1,disablePadding:N=!1,divider:L=!1,focusVisibleClassName:U,secondaryAction:W,selected:D=!1,slotProps:F={},slots:q={}}=r,H=Object(i.a)(r.ContainerProps,k),V=Object(i.a)(r,I),z=o.useContext(g.a),G=o.useMemo((()=>({dense:P||z.dense||!1,alignItems:c,disableGutters:B})),[c,z.dense,P,B]),K=o.useRef(null);Object(p.a)((()=>{u&&K.current&&K.current.focus()}),[u]);const Q=o.Children.toArray(y),Y=Q.length&&Object(f.a)(Q[Q.length-1],["ListItemSecondaryAction"]),J=Object(n.a)({},r,{alignItems:c,autoFocus:u,button:m,dense:G.dense,disabled:M,disableGutters:B,disablePadding:N,divider:L,hasSecondaryAction:Y,selected:D}),X=(e=>{const{alignItems:t,button:r,classes:i,dense:n,disabled:o,disableGutters:s,disablePadding:l,divider:c,hasSecondaryAction:u,selected:h}=e,d={root:["root",n&&"dense",!s&&"gutters",!l&&"padding",c&&"divider",o&&"disabled",r&&"button","flex-start"===t&&"alignItemsFlexStart",u&&"secondaryAction",h&&"selected"],container:["container"]};return Object(a.a)(d,v,i)})(J),$=Object(b.a)(K,t),Z=q.root||S.Root||T,ee=F.root||E.root||{},te=Object(n.a)({className:Object(s.a)(X.root,ee.className,_),disabled:M},V);let re=O||"li";return m&&(te.component=O||"div",te.focusVisibleClassName=Object(s.a)(w.focusVisible,U),re=d.a),Y?(re=te.component||O?re:"div","li"===A&&("li"===re?re="div":"li"===te.component&&(te.component="div")),Object(j.jsx)(g.a.Provider,{value:G,children:Object(j.jsxs)(C,Object(n.a)({as:A,className:Object(s.a)(X.container,R),ref:$,ownerState:J},H,{children:[Object(j.jsx)(Z,Object(n.a)({},ee,!Object(l.a)(Z)&&{as:re,ownerState:Object(n.a)({},J,ee.ownerState)},te,{children:Q})),Q.pop()]}))})):Object(j.jsx)(g.a.Provider,{value:G,children:Object(j.jsxs)(Z,Object(n.a)({},ee,{as:re,ref:$},!Object(l.a)(Z)&&{ownerState:Object(n.a)({},J,ee.ownerState)},te,{children:[Q,W&&Object(j.jsx)(x,{children:W})]}))})}));t.a=R},1077:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=(r(793),r(42)),a=r(558),l=r(49),c=r(69),u=r(124),h=r(235);let d;function f(){if(d)return d;const e=document.createElement("div"),t=document.createElement("div");return t.style.width="10px",t.style.height="1px",e.appendChild(t),e.dir="rtl",e.style.fontSize="14px",e.style.width="4px",e.style.height="1px",e.style.position="absolute",e.style.top="-1000px",e.style.overflow="scroll",document.body.appendChild(e),d="reverse",e.scrollLeft>0?d="default":(e.scrollLeft=1,0===e.scrollLeft&&(d="negative")),document.body.removeChild(e),d}function p(e,t){const r=e.scrollLeft;if("rtl"!==t)return r;switch(f()){case"negative":return e.scrollWidth-e.clientWidth+r;case"reverse":return e.scrollWidth-e.clientWidth-r;default:return r}}function b(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}function g(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>{};const{ease:o=b,duration:s=300}=i;let a=null;const l=t[e];let c=!1;const u=()=>{c=!0},h=i=>{if(c)return void n(new Error("Animation cancelled"));null===a&&(a=i);const u=Math.min(1,(i-a)/s);t[e]=o(u)*(r-l)+l,u>=1?requestAnimationFrame((()=>{n(null)})):requestAnimationFrame(h)};return l===r?(n(new Error("Element already at target position")),u):(requestAnimationFrame(h),u)}var m=r(533),y=r(2);const v=["onChange"],w={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};var _=r(762),O=r(761),j=r(1403),S=r(559),E=r(525);function A(e){return Object(E.a)("MuiTabScrollButton",e)}var x,k,I=Object(S.a)("MuiTabScrollButton",["root","vertical","horizontal","disabled"]);const T=["className","direction","orientation","disabled"],C=Object(l.a)(j.a,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.orientation&&t[r.orientation]]}})((e=>{let{ownerState:t}=e;return Object(n.a)({width:40,flexShrink:0,opacity:.8,["&.".concat(I.disabled)]:{opacity:0}},"vertical"===t.orientation&&{width:"100%",height:40,"& svg":{transform:"rotate(".concat(t.isRtl?-90:90,"deg)")}})}));var R=o.forwardRef((function(e,t){const r=Object(c.a)({props:e,name:"MuiTabScrollButton"}),{className:o,direction:l}=r,h=Object(i.a)(r,T),d="rtl"===Object(u.a)().direction,f=Object(n.a)({isRtl:d},r),p=(e=>{const{classes:t,orientation:r,disabled:i}=e,n={root:["root",r,i&&"disabled"]};return Object(a.a)(n,A,t)})(f);return Object(y.jsx)(C,Object(n.a)({component:"div",className:Object(s.a)(p.root,o),ref:t,role:null,ownerState:f,tabIndex:null},h,{children:"left"===l?x||(x=Object(y.jsx)(_.a,{fontSize:"small"})):k||(k=Object(y.jsx)(O.a,{fontSize:"small"}))}))})),P=r(617),M=r(895),B=r(676);const N=["aria-label","aria-labelledby","action","centered","children","className","component","allowScrollButtonsMobile","indicatorColor","onChange","orientation","ScrollButtonComponent","scrollButtons","selectionFollowsFocus","TabIndicatorProps","TabScrollButtonProps","textColor","value","variant","visibleScrollbar"],L=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,U=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,W=(e,t,r)=>{let i=!1,n=r(e,t);for(;n;){if(n===e.firstChild){if(i)return;i=!0}const t=n.disabled||"true"===n.getAttribute("aria-disabled");if(n.hasAttribute("tabindex")&&!t)return void n.focus();n=r(e,n)}},D=Object(l.a)("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{["& .".concat(M.a.scrollButtons)]:t.scrollButtons},{["& .".concat(M.a.scrollButtons)]:r.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,r.vertical&&t.vertical]}})((e=>{let{ownerState:t,theme:r}=e;return Object(n.a)({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex"},t.vertical&&{flexDirection:"column"},t.scrollButtonsHideMobile&&{["& .".concat(M.a.scrollButtons)]:{[r.breakpoints.down("sm")]:{display:"none"}}})})),F=Object(l.a)("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.scroller,r.fixed&&t.fixed,r.hideScrollbar&&t.hideScrollbar,r.scrollableX&&t.scrollableX,r.scrollableY&&t.scrollableY]}})((e=>{let{ownerState:t}=e;return Object(n.a)({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap"},t.fixed&&{overflowX:"hidden",width:"100%"},t.hideScrollbar&&{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},t.scrollableX&&{overflowX:"auto",overflowY:"hidden"},t.scrollableY&&{overflowY:"auto",overflowX:"hidden"})})),q=Object(l.a)("div",{name:"MuiTabs",slot:"FlexContainer",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.flexContainer,r.vertical&&t.flexContainerVertical,r.centered&&t.centered]}})((e=>{let{ownerState:t}=e;return Object(n.a)({display:"flex"},t.vertical&&{flexDirection:"column"},t.centered&&{justifyContent:"center"})})),H=Object(l.a)("span",{name:"MuiTabs",slot:"Indicator",overridesResolver:(e,t)=>t.indicator})((e=>{let{ownerState:t,theme:r}=e;return Object(n.a)({position:"absolute",height:2,bottom:0,width:"100%",transition:r.transitions.create()},"primary"===t.indicatorColor&&{backgroundColor:(r.vars||r).palette.primary.main},"secondary"===t.indicatorColor&&{backgroundColor:(r.vars||r).palette.secondary.main},t.vertical&&{height:"100%",width:2,right:0})})),V=Object(l.a)((function(e){const{onChange:t}=e,r=Object(i.a)(e,v),s=o.useRef(),a=o.useRef(null),l=()=>{s.current=a.current.offsetHeight-a.current.clientHeight};return o.useEffect((()=>{const e=Object(h.a)((()=>{const e=s.current;l(),e!==s.current&&t(s.current)})),r=Object(m.a)(a.current);return r.addEventListener("resize",e),()=>{e.clear(),r.removeEventListener("resize",e)}}),[t]),o.useEffect((()=>{l(),t(s.current)}),[t]),Object(y.jsx)("div",Object(n.a)({style:w,ref:a},r))}),{name:"MuiTabs",slot:"ScrollbarSize"})({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),z={};const G=o.forwardRef((function(e,t){const r=Object(c.a)({props:e,name:"MuiTabs"}),l=Object(u.a)(),d="rtl"===l.direction,{"aria-label":b,"aria-labelledby":v,action:w,centered:_=!1,children:O,className:j,component:S="div",allowScrollButtonsMobile:E=!1,indicatorColor:A="primary",onChange:x,orientation:k="horizontal",ScrollButtonComponent:I=R,scrollButtons:T="auto",selectionFollowsFocus:C,TabIndicatorProps:G={},TabScrollButtonProps:K={},textColor:Q="primary",value:Y,variant:J="standard",visibleScrollbar:X=!1}=r,$=Object(i.a)(r,N),Z="scrollable"===J,ee="vertical"===k,te=ee?"scrollTop":"scrollLeft",re=ee?"top":"left",ie=ee?"bottom":"right",ne=ee?"clientHeight":"clientWidth",oe=ee?"height":"width",se=Object(n.a)({},r,{component:S,allowScrollButtonsMobile:E,indicatorColor:A,orientation:k,vertical:ee,scrollButtons:T,textColor:Q,variant:J,visibleScrollbar:X,fixed:!Z,hideScrollbar:Z&&!X,scrollableX:Z&&!ee,scrollableY:Z&&ee,centered:_&&!Z,scrollButtonsHideMobile:!E}),ae=(e=>{const{vertical:t,fixed:r,hideScrollbar:i,scrollableX:n,scrollableY:o,centered:s,scrollButtonsHideMobile:l,classes:c}=e,u={root:["root",t&&"vertical"],scroller:["scroller",r&&"fixed",i&&"hideScrollbar",n&&"scrollableX",o&&"scrollableY"],flexContainer:["flexContainer",t&&"flexContainerVertical",s&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",l&&"scrollButtonsHideMobile"],scrollableX:[n&&"scrollableX"],hideScrollbar:[i&&"hideScrollbar"]};return Object(a.a)(u,M.b,c)})(se);const[le,ce]=o.useState(!1),[ue,he]=o.useState(z),[de,fe]=o.useState({start:!1,end:!1}),[pe,be]=o.useState({overflow:"hidden",scrollbarWidth:0}),ge=new Map,me=o.useRef(null),ye=o.useRef(null),ve=()=>{const e=me.current;let t,r;if(e){const r=e.getBoundingClientRect();t={clientWidth:e.clientWidth,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop,scrollLeftNormalized:p(e,l.direction),scrollWidth:e.scrollWidth,top:r.top,bottom:r.bottom,left:r.left,right:r.right}}if(e&&!1!==Y){const e=ye.current.children;if(e.length>0){const t=e[ge.get(Y)];0,r=t?t.getBoundingClientRect():null}}return{tabsMeta:t,tabMeta:r}},we=Object(P.a)((()=>{const{tabsMeta:e,tabMeta:t}=ve();let r,i=0;if(ee)r="top",t&&e&&(i=t.top-e.top+e.scrollTop);else if(r=d?"right":"left",t&&e){const n=d?e.scrollLeftNormalized+e.clientWidth-e.scrollWidth:e.scrollLeft;i=(d?-1:1)*(t[r]-e[r]+n)}const n={[r]:i,[oe]:t?t[oe]:0};if(isNaN(ue[r])||isNaN(ue[oe]))he(n);else{const e=Math.abs(ue[r]-n[r]),t=Math.abs(ue[oe]-n[oe]);(e>=1||t>=1)&&he(n)}})),_e=function(e){let{animation:t=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t?g(te,me.current,e,{duration:l.transitions.duration.standard}):me.current[te]=e},Oe=e=>{let t=me.current[te];ee?t+=e:(t+=e*(d?-1:1),t*=d&&"reverse"===f()?-1:1),_e(t)},je=()=>{const e=me.current[ne];let t=0;const r=Array.from(ye.current.children);for(let i=0;i<r.length;i+=1){const n=r[i];if(t+n[ne]>e){0===i&&(t=e);break}t+=n[ne]}return t},Se=()=>{Oe(-1*je())},Ee=()=>{Oe(je())},Ae=o.useCallback((e=>{be({overflow:null,scrollbarWidth:e})}),[]),xe=Object(P.a)((e=>{const{tabsMeta:t,tabMeta:r}=ve();if(r&&t)if(r[re]<t[re]){const i=t[te]+(r[re]-t[re]);_e(i,{animation:e})}else if(r[ie]>t[ie]){const i=t[te]+(r[ie]-t[ie]);_e(i,{animation:e})}})),ke=Object(P.a)((()=>{if(Z&&!1!==T){const{scrollTop:e,scrollHeight:t,clientHeight:r,scrollWidth:i,clientWidth:n}=me.current;let o,s;if(ee)o=e>1,s=e<t-r-1;else{const e=p(me.current,l.direction);o=d?e<i-n-1:e>1,s=d?e>1:e<i-n-1}o===de.start&&s===de.end||fe({start:o,end:s})}}));o.useEffect((()=>{const e=Object(h.a)((()=>{me.current&&(we(),ke())})),t=Object(m.a)(me.current);let r;return t.addEventListener("resize",e),"undefined"!==typeof ResizeObserver&&(r=new ResizeObserver(e),Array.from(ye.current.children).forEach((e=>{r.observe(e)}))),()=>{e.clear(),t.removeEventListener("resize",e),r&&r.disconnect()}}),[we,ke]);const Ie=o.useMemo((()=>Object(h.a)((()=>{ke()}))),[ke]);o.useEffect((()=>()=>{Ie.clear()}),[Ie]),o.useEffect((()=>{ce(!0)}),[]),o.useEffect((()=>{we(),ke()})),o.useEffect((()=>{xe(z!==ue)}),[xe,ue]),o.useImperativeHandle(w,(()=>({updateIndicator:we,updateScrollButtons:ke})),[we,ke]);const Te=Object(y.jsx)(H,Object(n.a)({},G,{className:Object(s.a)(ae.indicator,G.className),ownerState:se,style:Object(n.a)({},ue,G.style)}));let Ce=0;const Re=o.Children.map(O,(e=>{if(!o.isValidElement(e))return null;const t=void 0===e.props.value?Ce:e.props.value;ge.set(t,Ce);const r=t===Y;return Ce+=1,o.cloneElement(e,Object(n.a)({fullWidth:"fullWidth"===J,indicator:r&&!le&&Te,selected:r,selectionFollowsFocus:C,onChange:x,textColor:Q,value:t},1!==Ce||!1!==Y||e.props.tabIndex?{}:{tabIndex:0}))})),Pe=(()=>{const e={};e.scrollbarSizeListener=Z?Object(y.jsx)(V,{onChange:Ae,className:Object(s.a)(ae.scrollableX,ae.hideScrollbar)}):null;const t=de.start||de.end,r=Z&&("auto"===T&&t||!0===T);return e.scrollButtonStart=r?Object(y.jsx)(I,Object(n.a)({orientation:k,direction:d?"right":"left",onClick:Se,disabled:!de.start},K,{className:Object(s.a)(ae.scrollButtons,K.className)})):null,e.scrollButtonEnd=r?Object(y.jsx)(I,Object(n.a)({orientation:k,direction:d?"left":"right",onClick:Ee,disabled:!de.end},K,{className:Object(s.a)(ae.scrollButtons,K.className)})):null,e})();return Object(y.jsxs)(D,Object(n.a)({className:Object(s.a)(ae.root,j),ownerState:se,ref:t,as:S},$,{children:[Pe.scrollButtonStart,Pe.scrollbarSizeListener,Object(y.jsxs)(F,{className:ae.scroller,ownerState:se,style:{overflow:pe.overflow,[ee?"margin".concat(d?"Left":"Right"):"marginBottom"]:X?void 0:-pe.scrollbarWidth},ref:me,onScroll:Ie,children:[Object(y.jsx)(q,{"aria-label":b,"aria-labelledby":v,"aria-orientation":"vertical"===k?"vertical":null,className:ae.flexContainer,ownerState:se,onKeyDown:e=>{const t=ye.current,r=Object(B.a)(t).activeElement;if("tab"!==r.getAttribute("role"))return;let i="horizontal"===k?"ArrowLeft":"ArrowUp",n="horizontal"===k?"ArrowRight":"ArrowDown";switch("horizontal"===k&&d&&(i="ArrowRight",n="ArrowLeft"),e.key){case i:e.preventDefault(),W(t,r,U);break;case n:e.preventDefault(),W(t,r,L);break;case"Home":e.preventDefault(),W(t,null,L);break;case"End":e.preventDefault(),W(t,null,U)}},ref:ye,role:"tablist",children:Re}),le&&Te]}),Pe.scrollButtonEnd]}))}));t.a=G},1079:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=r(42),a=r(558),l=r(1403),c=r(55),u=r(69),h=r(49),d=r(559),f=r(525);function p(e){return Object(f.a)("MuiTab",e)}var b=Object(d.a)("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper"]),g=r(2);const m=["className","disabled","disableFocusRipple","fullWidth","icon","iconPosition","indicator","label","onChange","onClick","onFocus","selected","selectionFollowsFocus","textColor","value","wrapped"],y=Object(h.a)(l.a,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.label&&r.icon&&t.labelIcon,t["textColor".concat(Object(c.a)(r.textColor))],r.fullWidth&&t.fullWidth,r.wrapped&&t.wrapped]}})((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({},t.typography.button,{maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center"},r.label&&{flexDirection:"top"===r.iconPosition||"bottom"===r.iconPosition?"column":"row"},{lineHeight:1.25},r.icon&&r.label&&{minHeight:72,paddingTop:9,paddingBottom:9,["& > .".concat(b.iconWrapper)]:Object(n.a)({},"top"===r.iconPosition&&{marginBottom:6},"bottom"===r.iconPosition&&{marginTop:6},"start"===r.iconPosition&&{marginRight:t.spacing(1)},"end"===r.iconPosition&&{marginLeft:t.spacing(1)})},"inherit"===r.textColor&&{color:"inherit",opacity:.6,["&.".concat(b.selected)]:{opacity:1},["&.".concat(b.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity}},"primary"===r.textColor&&{color:(t.vars||t).palette.text.secondary,["&.".concat(b.selected)]:{color:(t.vars||t).palette.primary.main},["&.".concat(b.disabled)]:{color:(t.vars||t).palette.text.disabled}},"secondary"===r.textColor&&{color:(t.vars||t).palette.text.secondary,["&.".concat(b.selected)]:{color:(t.vars||t).palette.secondary.main},["&.".concat(b.disabled)]:{color:(t.vars||t).palette.text.disabled}},r.fullWidth&&{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"},r.wrapped&&{fontSize:t.typography.pxToRem(12)})})),v=o.forwardRef((function(e,t){const r=Object(u.a)({props:e,name:"MuiTab"}),{className:l,disabled:h=!1,disableFocusRipple:d=!1,fullWidth:f,icon:b,iconPosition:v="top",indicator:w,label:_,onChange:O,onClick:j,onFocus:S,selected:E,selectionFollowsFocus:A,textColor:x="inherit",value:k,wrapped:I=!1}=r,T=Object(i.a)(r,m),C=Object(n.a)({},r,{disabled:h,disableFocusRipple:d,selected:E,icon:!!b,iconPosition:v,label:!!_,fullWidth:f,textColor:x,wrapped:I}),R=(e=>{const{classes:t,textColor:r,fullWidth:i,wrapped:n,icon:o,label:s,selected:l,disabled:u}=e,h={root:["root",o&&s&&"labelIcon","textColor".concat(Object(c.a)(r)),i&&"fullWidth",n&&"wrapped",l&&"selected",u&&"disabled"],iconWrapper:["iconWrapper"]};return Object(a.a)(h,p,t)})(C),P=b&&_&&o.isValidElement(b)?o.cloneElement(b,{className:Object(s.a)(R.iconWrapper,b.props.className)}):b;return Object(g.jsxs)(y,Object(n.a)({focusRipple:!d,className:Object(s.a)(R.root,l),ref:t,role:"tab","aria-selected":E,disabled:h,onClick:e=>{!E&&O&&O(e,k),j&&j(e)},onFocus:e=>{A&&!E&&O&&O(e,k),S&&S(e)},ownerState:C,tabIndex:E?0:-1},T,{children:["top"===v||"start"===v?Object(g.jsxs)(o.Fragment,{children:[P,_]}):Object(g.jsxs)(o.Fragment,{children:[_,P]}),w]}))}));t.a=v},1396:function(e,t,r){"use strict";r.r(t);var i=r(0),n=r.n(i),o=r(671),s=r(529),a=r(672),l=r(1395),c=r(1077),u=r(1079),h=r(603),d=r(1410),f=r(1415),p=r(1190),b=r(1421),g=r(684),m=r(1058),y=r(794);const v={clientId:"aslaacv_".concat(Math.random().toString(16).substring(2,10)),clean:!0,connectTimeout:4e3,reconnectPeriod:1e3,keepalive:60,protocolId:"MQTT",protocolVersion:4,rejectUnauthorized:!1},w="ws://*************:8083/mqtt",_=["ws://*************:8084/mqtt","wss://api.elec.mn:8084/mqtt"];let O=0,j=w,S=null;const E={connect:[],message:[],error:[],reconnect:[],offline:[],disconnect:[]},A=()=>(O=(O+1)%(_.length+1),j=0===O?w:_[O-1],console.log("Switching to broker: ".concat(j)),j),x=()=>{if(S&&S.connected)return S;S&&(S.end(!0),S=null);try{S=y.a.connect(j,v);let e=0;S.on("reconnect",(()=>{e++,e>=3&&(e=0,j=A(),S.options.href=j)}))}catch(e){j=A()}return S.on("connect",(()=>{E.connect.forEach((e=>e()))})),S.on("message",((e,t)=>{E.message.forEach((r=>r(e,t)))})),S.on("error",(e=>{E.error.forEach((t=>t(e)))})),S.on("reconnect",(()=>{E.reconnect.forEach((e=>e()))})),S.on("offline",(()=>{E.offline.forEach((e=>e()))})),S.on("disconnect",(()=>{E.disconnect.forEach((e=>e()))})),S},k=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{qos:0};return!(!S||!S.connected)&&(S.subscribe(e,t,(e=>{if(e)return!1})),!0)},I=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{qos:0,retain:!1};return!(!S||!S.connected)&&(S.publish(e,t,r,(e=>{if(e)return!1})),!0)},T=(e,t)=>{E[e]&&E[e].push(t)},C=()=>{S&&S.connected&&(S.end(),S=null)};var R=r(2);var P=()=>{const[e,t]=Object(i.useState)(!1),[r,o]=Object(i.useState)([]),[l,c]=Object(i.useState)("aslaa/test"),[u,h]=Object(i.useState)("Hello MQTT"),[y,v]=Object(i.useState)("aslaa/test");Object(i.useEffect)((()=>(x(),T("connect",(()=>{t(!0),w("System","Connected to MQTT broker")})),T("message",((e,t)=>{w(e,t.toString())})),T("error",(e=>{w("Error",e.message)})),T("disconnect",(()=>{t(!1),w("System","Disconnected from MQTT broker")})),()=>{C()})),[]);const w=(e,t)=>{o((r=>[...r,{id:Date.now(),topic:e,message:t,time:(new Date).toLocaleTimeString()}]))};return Object(R.jsxs)(s.a,{sx:{p:3,maxWidth:600,mx:"auto"},children:[Object(R.jsxs)(d.a,{sx:{p:3,mb:3},children:[Object(R.jsx)(a.a,{variant:"h5",gutterBottom:!0,children:"MQTT Connection Test"}),Object(R.jsxs)(a.a,{variant:"body1",gutterBottom:!0,children:["Status: ",e?Object(R.jsx)("span",{style:{color:"green"},children:"Connected"}):Object(R.jsx)("span",{style:{color:"red"},children:"Disconnected"})]}),Object(R.jsxs)(s.a,{sx:{mt:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Subscribe"}),Object(R.jsxs)(s.a,{sx:{display:"flex",gap:1,mb:2},children:[Object(R.jsx)(f.a,{label:"Topic",variant:"outlined",size:"small",fullWidth:!0,value:y,onChange:e=>v(e.target.value)}),Object(R.jsx)(p.a,{variant:"contained",onClick:()=>{y&&(k(y),w("System","Subscribed to ".concat(y)))},disabled:!e||!y,children:"Subscribe"})]})]}),Object(R.jsxs)(s.a,{sx:{mt:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Publish"}),Object(R.jsxs)(s.a,{sx:{display:"flex",flexDirection:"column",gap:1},children:[Object(R.jsx)(f.a,{label:"Topic",variant:"outlined",size:"small",value:l,onChange:e=>c(e.target.value)}),Object(R.jsx)(f.a,{label:"Message",variant:"outlined",size:"small",value:u,onChange:e=>h(e.target.value)}),Object(R.jsx)(p.a,{variant:"contained",onClick:()=>{l&&u&&(I(l,u),w("System","Published to ".concat(l,": ").concat(u)))},disabled:!e||!l||!u,children:"Publish"})]})]})]}),Object(R.jsxs)(d.a,{sx:{p:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Messages"}),Object(R.jsx)(b.a,{children:0===r.length?Object(R.jsx)(a.a,{variant:"body2",color:"text.secondary",children:"No messages yet"}):r.map(((e,t)=>Object(R.jsxs)(n.a.Fragment,{children:[t>0&&Object(R.jsx)(g.a,{}),Object(R.jsx)(m.a,{children:Object(R.jsxs)(s.a,{sx:{width:"100%"},children:[Object(R.jsxs)(s.a,{sx:{display:"flex",justifyContent:"space-between"},children:[Object(R.jsx)(a.a,{variant:"subtitle2",color:"primary",children:e.topic}),Object(R.jsx)(a.a,{variant:"caption",color:"text.secondary",children:e.time})]}),Object(R.jsx)(a.a,{variant:"body2",children:e.message})]})})]},e.id)))})]})]})},M=r(565);var B=()=>{const[e,t]=Object(i.useState)("Disconnected"),[r,n]=Object(i.useState)(null),[o,l]=Object(i.useState)(!1),[c,u]=Object(i.useState)(null),[h,f]=Object(i.useState)([]),b=e=>{const t=(new Date).toLocaleTimeString();f((r=>[...r,"[".concat(t,"] ").concat(e)]))};return Object(i.useEffect)((()=>()=>{r&&r.end()}),[r]),Object(R.jsx)(s.a,{sx:{p:3,maxWidth:600,mx:"auto"},children:Object(R.jsxs)(d.a,{sx:{p:3,mb:3},children:[Object(R.jsx)(a.a,{variant:"h5",gutterBottom:!0,children:"Simple MQTT Connection Test (*************:8083)"}),Object(R.jsxs)(s.a,{sx:{display:"flex",alignItems:"center",mb:2},children:[Object(R.jsx)(a.a,{variant:"body1",sx:{mr:1},children:"Status:"}),Object(R.jsx)(a.a,{variant:"body1",sx:{color:"Connected"===e?"green":"Connecting..."===e||"Reconnecting"===e?"orange":"error.main",fontWeight:"bold"},children:e}),o&&Object(R.jsx)(M.a,{size:20,sx:{ml:1}})]}),c&&Object(R.jsxs)(a.a,{color:"error",sx:{mb:2},children:["Error: ",c]}),Object(R.jsxs)(s.a,{sx:{display:"flex",gap:2,mb:3},children:[Object(R.jsx)(p.a,{variant:"contained",onClick:()=>{l(!0),t("Connecting..."),u(null);try{const e={clientId:"aslaacv_".concat(Math.random().toString(16).substring(2,10)),clean:!0,connectTimeout:4e3,reconnectPeriod:1e3};b("Connecting to MQTT broker...");const r=y.a.connect("ws://*************:8083/mqtt",e);r.on("connect",(()=>{t("Connected"),n(r),l(!1),b("Connected to MQTT broker successfully!"),r.subscribe("aslaa/test",(e=>{e?b("Error subscribing: ".concat(e.message)):(b("Subscribed to aslaa/test topic"),r.publish("aslaa/test","Hello from SimpleMqttTest",{qos:0},(e=>{b(e?"Error publishing: ".concat(e.message):"Published test message to aslaa/test")})))}))})),r.on("message",((e,t)=>{const r=t.toString();b("Received message on ".concat(e,": ").concat(r))})),r.on("error",(e=>{t("Error"),u(e.message),l(!1),b("Connection error: ".concat(e.message)),console.error("MQTT Error:",e)})),r.on("close",(()=>{t("Disconnected"),l(!1),b("Connection closed")})),r.on("offline",(()=>{t("Offline"),l(!1),b("Client is offline")})),r.on("reconnect",(()=>{t("Reconnecting"),b("Attempting to reconnect...")}))}catch(e){t("Error"),u(e.message),l(!1),b("Exception: ".concat(e.message))}},disabled:"Connected"===e||"Connecting..."===e||o,children:"Connect"}),Object(R.jsx)(p.a,{variant:"outlined",onClick:()=>{r&&(r.end(),n(null),t("Disconnected"),b("Disconnected from MQTT broker"))},disabled:!r||"Disconnected"===e,children:"Disconnect"}),Object(R.jsx)(p.a,{variant:"contained",color:"secondary",onClick:()=>{r&&r.connected?r.publish("aslaa/test","Test message at ".concat((new Date).toLocaleTimeString()),{qos:0},(e=>{b(e?"Error publishing: ".concat(e.message):"Published test message")})):b("Cannot publish: client not connected")},disabled:!r||"Connected"!==e,children:"Publish Test Message"})]}),Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Logs"}),Object(R.jsx)(d.a,{variant:"outlined",sx:{p:2,maxHeight:300,overflow:"auto",bgcolor:"grey.900",fontFamily:"monospace",fontSize:"0.875rem"},children:0===h.length?Object(R.jsx)(a.a,{variant:"body2",color:"text.secondary",children:'No logs yet. Click "Connect" to start.'}):h.map(((e,t)=>Object(R.jsx)(a.a,{variant:"body2",color:"grey.300",sx:{mb:.5},children:e},t)))})]})})};var N=()=>{const[e,t]=Object(i.useState)("aslaa/test"),[r,o]=Object(i.useState)("Hello from HTTP MQTT Test"),[l,c]=Object(i.useState)([]),[u,h]=Object(i.useState)(!1),y="https://broker.emqx.io:8081/api/v4",v=(e,t)=>{c((r=>[...r,{id:Date.now(),type:e,content:t,time:(new Date).toLocaleTimeString()}]))};return Object(R.jsxs)(s.a,{sx:{p:3,maxWidth:600,mx:"auto"},children:[Object(R.jsxs)(d.a,{sx:{p:3,mb:3},children:[Object(R.jsx)(a.a,{variant:"h5",gutterBottom:!0,children:"HTTP MQTT Test"}),Object(R.jsx)(a.a,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"This test uses HTTP API instead of WebSocket connection"}),Object(R.jsxs)(s.a,{sx:{mt:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Publish Message"}),Object(R.jsxs)(s.a,{sx:{display:"flex",flexDirection:"column",gap:2},children:[Object(R.jsx)(f.a,{label:"Topic",variant:"outlined",size:"small",value:e,onChange:e=>t(e.target.value)}),Object(R.jsx)(f.a,{label:"Message",variant:"outlined",size:"small",value:r,onChange:e=>o(e.target.value)}),Object(R.jsxs)(s.a,{sx:{display:"flex",gap:2},children:[Object(R.jsx)(p.a,{variant:"contained",onClick:async()=>{if(e&&r){h(!0);try{v("System","Publishing to ".concat(e,": ").concat(r));const t=await fetch("".concat(y,"/mqtt/publish"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({topic:e,payload:r,qos:1,retain:!1})}),i=await t.json();0===i.code?v("Success","Message published successfully to ".concat(e)):v("Error","Failed to publish: ".concat(i.message))}catch(t){v("Error","Error publishing message: ".concat(t.message)),console.error("Error publishing message:",t)}finally{h(!1)}}},disabled:u||!e||!r,children:u?Object(R.jsx)(M.a,{size:24}):"Publish"}),Object(R.jsx)(p.a,{variant:"outlined",onClick:async()=>{h(!0);try{v("System","Checking broker status...");const t=await fetch("".concat(y,"/brokers"),{method:"GET",headers:{"Content-Type":"application/json"}}),r=await t.json();var e;if(0===r.code)v("Success","Broker is online. Node: ".concat(null===(e=r.data[0])||void 0===e?void 0:e.node));else v("Error","Failed to check broker: ".concat(r.message))}catch(t){v("Error","Error checking broker status: ".concat(t.message)),console.error("Error checking broker status:",t)}finally{h(!1)}},disabled:u,children:"Check Broker Status"}),Object(R.jsx)(p.a,{variant:"text",onClick:()=>{c([])},disabled:u||0===l.length,children:"Clear Logs"})]})]})]})]}),Object(R.jsxs)(d.a,{sx:{p:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Logs"}),Object(R.jsx)(b.a,{children:0===l.length?Object(R.jsx)(a.a,{variant:"body2",color:"text.secondary",children:"No logs yet"}):l.map(((e,t)=>Object(R.jsxs)(n.a.Fragment,{children:[t>0&&Object(R.jsx)(g.a,{}),Object(R.jsx)(m.a,{children:Object(R.jsxs)(s.a,{sx:{width:"100%"},children:[Object(R.jsxs)(s.a,{sx:{display:"flex",justifyContent:"space-between"},children:[Object(R.jsx)(a.a,{variant:"subtitle2",color:"Success"===e.type?"success.main":"Error"===e.type?"error.main":"primary.main",children:e.type}),Object(R.jsx)(a.a,{variant:"caption",color:"text.secondary",children:e.time})]}),Object(R.jsx)(a.a,{variant:"body2",children:e.content})]})})]},e.id)))})]})]})};var L=()=>{const[e,t]=Object(i.useState)("Disconnected"),[r,n]=Object(i.useState)(null),[o,l]=Object(i.useState)(!1),[c,u]=Object(i.useState)(null),[h,b]=Object(i.useState)([]),[g,m]=Object(i.useState)("wss://broker.emqx.io:8084/mqtt"),[v,w]=Object(i.useState)("Hello from DirectMqttTest"),[_,O]=Object(i.useState)("aslaa/test"),j=e=>{const t=(new Date).toLocaleTimeString();b((r=>[...r,"[".concat(t,"] ").concat(e)]))};return Object(i.useEffect)((()=>()=>{r&&r.end()}),[r]),Object(R.jsx)(s.a,{sx:{p:3,maxWidth:600,mx:"auto"},children:Object(R.jsxs)(d.a,{sx:{p:3,mb:3},children:[Object(R.jsx)(a.a,{variant:"h5",gutterBottom:!0,children:"Direct MQTT Connection Test"}),Object(R.jsxs)(s.a,{sx:{display:"flex",alignItems:"center",mb:2},children:[Object(R.jsx)(a.a,{variant:"body1",sx:{mr:1},children:"Status:"}),Object(R.jsx)(a.a,{variant:"body1",sx:{color:"Connected"===e?"green":"Connecting..."===e||"Reconnecting"===e?"orange":"error.main",fontWeight:"bold"},children:e}),o&&Object(R.jsx)(M.a,{size:20,sx:{ml:1}})]}),c&&Object(R.jsxs)(a.a,{color:"error",sx:{mb:2},children:["Error: ",c]}),Object(R.jsxs)(s.a,{sx:{mb:3},children:[Object(R.jsx)(f.a,{label:"Broker URL",variant:"outlined",size:"small",fullWidth:!0,value:g,onChange:e=>m(e.target.value),sx:{mb:2},disabled:"Connected"===e||"Connecting..."===e}),Object(R.jsxs)(s.a,{sx:{display:"flex",gap:2},children:[Object(R.jsx)(p.a,{variant:"contained",onClick:()=>{l(!0),t("Connecting..."),u(null);try{j("Connecting to MQTT broker: ".concat(g));const e=y.a.connect(g);e.on("connect",(()=>{t("Connected"),n(e),l(!1),j("Connected to MQTT broker successfully!")})),e.on("message",((e,t)=>{const r=t.toString();j("Received message on ".concat(e,": ").concat(r))})),e.on("error",(e=>{t("Error"),u(e.message),l(!1),j("Connection error: ".concat(e.message)),console.error("MQTT Error:",e)})),e.on("close",(()=>{t("Disconnected"),l(!1),j("Connection closed")})),e.on("offline",(()=>{t("Offline"),l(!1),j("Client is offline")})),e.on("reconnect",(()=>{t("Reconnecting"),j("Attempting to reconnect...")}))}catch(e){t("Error"),u(e.message),l(!1),j("Exception: ".concat(e.message)),console.error("MQTT Connection Exception:",e)}},disabled:"Connected"===e||"Connecting..."===e||o||!g,children:"Connect"}),Object(R.jsx)(p.a,{variant:"outlined",onClick:()=>{r&&(r.end(),n(null),t("Disconnected"),j("Disconnected from MQTT broker"))},disabled:!r||"Disconnected"===e,children:"Disconnect"})]})]}),Object(R.jsxs)(s.a,{sx:{mb:3},children:[Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Publish & Subscribe"}),Object(R.jsx)(f.a,{label:"Topic",variant:"outlined",size:"small",fullWidth:!0,value:_,onChange:e=>O(e.target.value),sx:{mb:2}}),Object(R.jsx)(f.a,{label:"Message",variant:"outlined",size:"small",fullWidth:!0,value:v,onChange:e=>w(e.target.value),sx:{mb:2}}),Object(R.jsxs)(s.a,{sx:{display:"flex",gap:2},children:[Object(R.jsx)(p.a,{variant:"contained",onClick:()=>{r&&r.connected&&_?r.subscribe(_,(e=>{j(e?"Error subscribing to ".concat(_,": ").concat(e.message):"Subscribed to ".concat(_))})):j("Cannot subscribe: client not connected or topic empty")},disabled:!r||"Connected"!==e||!_,children:"Subscribe"}),Object(R.jsx)(p.a,{variant:"contained",color:"secondary",onClick:()=>{r&&r.connected&&_&&v?r.publish(_,v,{qos:0},(e=>{j(e?"Error publishing: ".concat(e.message):"Published to ".concat(_,": ").concat(v))})):j("Cannot publish: client not connected or topic/message empty")},disabled:!r||"Connected"!==e||!_||!v,children:"Publish"})]})]}),Object(R.jsx)(a.a,{variant:"h6",gutterBottom:!0,children:"Logs"}),Object(R.jsx)(d.a,{variant:"outlined",sx:{p:2,maxHeight:300,overflow:"auto",bgcolor:"grey.900",fontFamily:"monospace",fontSize:"0.875rem"},children:0===h.length?Object(R.jsx)(a.a,{variant:"body2",color:"text.secondary",children:'No logs yet. Click "Connect" to start.'}):h.map(((e,t)=>Object(R.jsx)(a.a,{variant:"body2",color:"grey.300",sx:{mb:.5},children:e},t)))})]})})};t.default=()=>{const[e,t]=n.a.useState(0);return Object(R.jsx)(h.a,{title:"MQTT Test",children:Object(R.jsxs)(o.a,{maxWidth:"lg",children:[Object(R.jsxs)(s.a,{sx:{mb:5},children:[Object(R.jsx)(a.a,{variant:"h4",gutterBottom:!0,children:"MQTT Connection Test"}),Object(R.jsx)(a.a,{variant:"body1",color:"text.secondary",children:"Testing connection with the default EMQX broker (broker.emqx.io)"})]}),Object(R.jsx)(l.a,{severity:"info",sx:{mb:3},children:"If WebSocket connections don't work, try the HTTP API test which uses a different approach."}),Object(R.jsx)(s.a,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:Object(R.jsxs)(c.a,{value:e,onChange:(e,r)=>{t(r)},children:[Object(R.jsx)(u.a,{label:"Simple WebSocket Test"}),Object(R.jsx)(u.a,{label:"Direct URL Test"}),Object(R.jsx)(u.a,{label:"Advanced WebSocket Test"}),Object(R.jsx)(u.a,{label:"HTTP API Test"})]})}),0===e&&Object(R.jsx)(B,{}),1===e&&Object(R.jsx)(L,{}),2===e&&Object(R.jsx)(P,{}),3===e&&Object(R.jsx)(N,{})]})})}},571:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(11);function n(e,t){if(null==e)return{};var r,n,o=Object(i.a)(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}},603:function(e,t,r){"use strict";var i=r(8),n=r(571),o=r(6),s=r.n(o),a=r(234),l=r(0),c=r(529),u=r(671),h=r(2);const d=["children","title","meta"],f=Object(l.forwardRef)(((e,t)=>{let{children:r,title:o="",meta:s}=e,l=Object(n.a)(e,d);return Object(h.jsxs)(h.Fragment,{children:[Object(h.jsxs)(a.a,{children:[Object(h.jsx)("title",{children:o}),s]}),Object(h.jsx)(c.a,Object(i.a)(Object(i.a)({ref:t},l),{},{children:Object(h.jsx)(u.a,{children:r})}))]})}));f.propTypes={children:s.a.node.isRequired,title:s.a.string,meta:s.a.node},t.a=f},604:function(e,t,r){"use strict";var i=r(183);const n=Object(i.a)();t.a=n},613:function(e,t,r){"use strict";r.d(t,"b",(function(){return o}));var i=r(559),n=r(525);function o(e){return Object(n.a)("MuiDivider",e)}const s=Object(i.a)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);t.a=s},663:function(e,t,r){"use strict";function i(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw new TypeError("Private element is not present on this object")}r.d(t,"a",(function(){return i}))},671:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=r(236),a=r(525),l=r(558),c=r(227),u=r(520),h=r(604),d=r(343),f=r(2);const p=["className","component","disableGutters","fixed","maxWidth","classes"],b=Object(d.a)(),g=Object(h.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t["maxWidth".concat(Object(c.a)(String(r.maxWidth)))],r.fixed&&t.fixed,r.disableGutters&&t.disableGutters]}}),m=e=>Object(u.a)({props:e,name:"MuiContainer",defaultTheme:b}),y=(e,t)=>{const{classes:r,fixed:i,disableGutters:n,maxWidth:o}=e,s={root:["root",o&&"maxWidth".concat(Object(c.a)(String(o))),i&&"fixed",n&&"disableGutters"]};return Object(l.a)(s,(e=>Object(a.a)(t,e)),r)};var v=r(55),w=r(49),_=r(69);const O=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:t=g,useThemeProps:r=m,componentName:a="MuiContainer"}=e,l=t((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",display:"block"},!r.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}})}),(e=>{let{theme:t,ownerState:r}=e;return r.fixed&&Object.keys(t.breakpoints.values).reduce(((e,r)=>{const i=r,n=t.breakpoints.values[i];return 0!==n&&(e[t.breakpoints.up(i)]={maxWidth:"".concat(n).concat(t.breakpoints.unit)}),e}),{})}),(e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({},"xs"===r.maxWidth&&{[t.breakpoints.up("xs")]:{maxWidth:Math.max(t.breakpoints.values.xs,444)}},r.maxWidth&&"xs"!==r.maxWidth&&{[t.breakpoints.up(r.maxWidth)]:{maxWidth:"".concat(t.breakpoints.values[r.maxWidth]).concat(t.breakpoints.unit)}})})),c=o.forwardRef((function(e,t){const o=r(e),{className:c,component:u="div",disableGutters:h=!1,fixed:d=!1,maxWidth:b="lg"}=o,g=Object(i.a)(o,p),m=Object(n.a)({},o,{component:u,disableGutters:h,fixed:d,maxWidth:b}),v=y(m,a);return Object(f.jsx)(l,Object(n.a)({as:u,ownerState:m,className:Object(s.a)(v.root,c),ref:t},g))}));return c}({createStyledComponent:Object(w.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t["maxWidth".concat(Object(v.a)(String(r.maxWidth)))],r.fixed&&t.fixed,r.disableGutters&&t.disableGutters]}}),useThemeProps:e=>Object(_.a)({props:e,name:"MuiContainer"})});t.a=O},672:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=r(42),a=r(562),l=r(558),c=r(49),u=r(69),h=r(55),d=r(559),f=r(525);function p(e){return Object(f.a)("MuiTypography",e)}Object(d.a)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);var b=r(2);const g=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],m=Object(c.a)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.variant&&t[r.variant],"inherit"!==r.align&&t["align".concat(Object(h.a)(r.align))],r.noWrap&&t.noWrap,r.gutterBottom&&t.gutterBottom,r.paragraph&&t.paragraph]}})((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({margin:0},r.variant&&t.typography[r.variant],"inherit"!==r.align&&{textAlign:r.align},r.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},r.gutterBottom&&{marginBottom:"0.35em"},r.paragraph&&{marginBottom:16})})),y={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},v={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},w=o.forwardRef((function(e,t){const r=Object(u.a)({props:e,name:"MuiTypography"}),o=(e=>v[e]||e)(r.color),c=Object(a.a)(Object(n.a)({},r,{color:o})),{align:d="inherit",className:f,component:w,gutterBottom:_=!1,noWrap:O=!1,paragraph:j=!1,variant:S="body1",variantMapping:E=y}=c,A=Object(i.a)(c,g),x=Object(n.a)({},c,{align:d,color:o,className:f,component:w,gutterBottom:_,noWrap:O,paragraph:j,variant:S,variantMapping:E}),k=w||(j?"p":E[S]||y[S])||"span",I=(e=>{const{align:t,gutterBottom:r,noWrap:i,paragraph:n,variant:o,classes:s}=e,a={root:["root",o,"inherit"!==e.align&&"align".concat(Object(h.a)(t)),r&&"gutterBottom",i&&"noWrap",n&&"paragraph"]};return Object(l.a)(a,p,s)})(x);return Object(b.jsx)(m,Object(n.a)({as:k,ref:t,ownerState:x,className:Object(s.a)(I.root,f)},A))}));t.a=w},684:function(e,t,r){"use strict";var i=r(11),n=r(3),o=r(0),s=r(42),a=r(558),l=r(566),c=r(49),u=r(69),h=r(613),d=r(2);const f=["absolute","children","className","component","flexItem","light","orientation","role","textAlign","variant"],p=Object(c.a)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin"},r.absolute&&{position:"absolute",bottom:0,left:0,width:"100%"},r.light&&{borderColor:t.vars?"rgba(".concat(t.vars.palette.dividerChannel," / 0.08)"):Object(l.a)(t.palette.divider,.08)},"inset"===r.variant&&{marginLeft:72},"middle"===r.variant&&"horizontal"===r.orientation&&{marginLeft:t.spacing(2),marginRight:t.spacing(2)},"middle"===r.variant&&"vertical"===r.orientation&&{marginTop:t.spacing(1),marginBottom:t.spacing(1)},"vertical"===r.orientation&&{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"},r.flexItem&&{alignSelf:"stretch",height:"auto"})}),(e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({},r.children&&{display:"flex",whiteSpace:"nowrap",textAlign:"center",border:0,"&::before, &::after":{position:"relative",width:"100%",borderTop:"thin solid ".concat((t.vars||t).palette.divider),top:"50%",content:'""',transform:"translateY(50%)"}})}),(e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({},r.children&&"vertical"===r.orientation&&{flexDirection:"column","&::before, &::after":{height:"100%",top:"0%",left:"50%",borderTop:0,borderLeft:"thin solid ".concat((t.vars||t).palette.divider),transform:"translateX(0%)"}})}),(e=>{let{ownerState:t}=e;return Object(n.a)({},"right"===t.textAlign&&"vertical"!==t.orientation&&{"&::before":{width:"90%"},"&::after":{width:"10%"}},"left"===t.textAlign&&"vertical"!==t.orientation&&{"&::before":{width:"10%"},"&::after":{width:"90%"}})})),b=Object(c.a)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((e=>{let{theme:t,ownerState:r}=e;return Object(n.a)({display:"inline-block",paddingLeft:"calc(".concat(t.spacing(1)," * 1.2)"),paddingRight:"calc(".concat(t.spacing(1)," * 1.2)")},"vertical"===r.orientation&&{paddingTop:"calc(".concat(t.spacing(1)," * 1.2)"),paddingBottom:"calc(".concat(t.spacing(1)," * 1.2)")})})),g=o.forwardRef((function(e,t){const r=Object(u.a)({props:e,name:"MuiDivider"}),{absolute:o=!1,children:l,className:c,component:g=(l?"div":"hr"),flexItem:m=!1,light:y=!1,orientation:v="horizontal",role:w=("hr"!==g?"separator":void 0),textAlign:_="center",variant:O="fullWidth"}=r,j=Object(i.a)(r,f),S=Object(n.a)({},r,{absolute:o,component:g,flexItem:m,light:y,orientation:v,role:w,textAlign:_,variant:O}),E=(e=>{const{absolute:t,children:r,classes:i,flexItem:n,light:o,orientation:s,textAlign:l,variant:c}=e,u={root:["root",t&&"absolute",c,o&&"light","vertical"===s&&"vertical",n&&"flexItem",r&&"withChildren",r&&"vertical"===s&&"withChildrenVertical","right"===l&&"vertical"!==s&&"textAlignRight","left"===l&&"vertical"!==s&&"textAlignLeft"],wrapper:["wrapper","vertical"===s&&"wrapperVertical"]};return Object(a.a)(u,h.b,i)})(S);return Object(d.jsx)(p,Object(n.a)({as:g,className:Object(s.a)(E.root,c),role:w,ref:t,ownerState:S},j,{children:l?Object(d.jsx)(b,{className:E.wrapper,ownerState:S,children:l}):null}))}));t.a=g},703:function(e,t,r){"use strict";function i(e,t){this.v=e,this.k=t}r.d(t,"a",(function(){return i}))},704:function(e,t){(function(t){e.exports=t}).call(this,{})},761:function(e,t,r){"use strict";r(0);var i=r(573),n=r(2);t.a=Object(i.a)(Object(n.jsx)("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight")},762:function(e,t,r){"use strict";r(0);var i=r(573),n=r(2);t.a=Object(i.a)(Object(n.jsx)("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft")},765:function(e,t,r){"use strict";function i(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}r.d(t,"a",(function(){return i}))},766:function(e,t,r){"use strict";function i(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=r}}r.d(t,"a",(function(){return i}))},767:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(663);function n(e,t){return e.get(Object(i.a)(e,t))}},768:function(e,t,r){"use strict";function i(e,t){return t.get?t.get.call(e):t.value}r.d(t,"a",(function(){return i}))},769:function(e,t,r){"use strict";function i(e,t){if(void 0===e)throw new TypeError("attempted to "+t+" private static field before its declaration")}r.d(t,"a",(function(){return i}))},794:function(e,t,r){"use strict";(function(e,i){var n=r(571),o=r(829),s=r(830),a=r(831),l=r(832),c=r(833),u=r(834),h=r(835),d=r(50),f=r(8),p=r(836),b=r(837),g=r(838),m=r(839);const y=["context","forceRefresh"];var v=Object.defineProperty,w=Object.getOwnPropertyDescriptor,_=Object.getOwnPropertyNames,O=Object.prototype.hasOwnProperty,j=(e,t)=>()=>(e&&(t=e(e=0)),t),S=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),E=(e,t)=>{for(var r in t)v(e,r,{get:t[r],enumerable:!0})},A=e=>((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of _(t))!O.call(e,n)&&n!==r&&v(e,n,{get:()=>t[n],enumerable:!(i=w(t,n))||i.enumerable});return e})(v({},"__esModule",{value:!0}),e),x=j((()=>{})),k={};function I(e){throw new Error("Node.js process "+e+" is not supported by JSPM core outside of Node.js")}function T(){!G||!K||(G=!1,K.length?z=K.concat(z):Q=-1,z.length&&C())}function C(){if(!G){var e=setTimeout(T,0);G=!0;for(var t=z.length;t;){for(K=z,z=[];++Q<t;)K&&K[Q].run();Q=-1,t=z.length}K=null,G=!1,clearTimeout(e)}}function R(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];z.push(new P(e,t)),1===z.length&&!G&&setTimeout(C,0)}function P(e,t){this.fun=e,this.array=t}function M(){}function B(e){I("_linkedBinding")}function N(e){I("dlopen")}function L(){return[]}function U(){return[]}function W(e,t){if(!e)throw new Error(t||"assertion error")}function D(){return!1}function F(){return qe.now()/1e3}function q(e){var t=Math.floor(.001*(Date.now()-qe.now())),r=.001*qe.now(),i=Math.floor(r)+t,n=Math.floor(r%1*1e9);return e&&(i-=e[0],(n-=e[1])<0&&(i--,n+=Ve)),[i,n]}function H(){return rt}function V(e){return[]}E(k,{_debugEnd:()=>ke,_debugProcess:()=>xe,_events:()=>Ge,_eventsCount:()=>Ke,_exiting:()=>de,_fatalExceptions:()=>Se,_getActiveHandles:()=>U,_getActiveRequests:()=>L,_kill:()=>be,_linkedBinding:()=>B,_maxListeners:()=>ze,_preload_modules:()=>De,_rawDebug:()=>ce,_startProfilerIdleNotifier:()=>Ie,_stopProfilerIdleNotifier:()=>Te,_tickCallback:()=>Ae,abort:()=>Me,addListener:()=>Qe,allowedNodeEnvironmentFlags:()=>Oe,arch:()=>J,argv:()=>Z,argv0:()=>We,assert:()=>W,binding:()=>ne,chdir:()=>ae,config:()=>fe,cpuUsage:()=>ge,cwd:()=>se,debugPort:()=>Ue,default:()=>rt,dlopen:()=>N,domain:()=>he,emit:()=>Ze,emitWarning:()=>ie,env:()=>$,execArgv:()=>ee,execPath:()=>Le,exit:()=>we,features:()=>je,hasUncaughtExceptionCaptureCallback:()=>D,hrtime:()=>q,kill:()=>ve,listeners:()=>V,memoryUsage:()=>ye,moduleLoadList:()=>ue,nextTick:()=>R,off:()=>Je,on:()=>H,once:()=>Ye,openStdin:()=>_e,pid:()=>Be,platform:()=>X,ppid:()=>Ne,prependListener:()=>et,prependOnceListener:()=>tt,reallyExit:()=>pe,release:()=>le,removeAllListeners:()=>$e,removeListener:()=>Xe,resourceUsage:()=>me,setSourceMapsEnabled:()=>Fe,setUncaughtExceptionCaptureCallback:()=>Ee,stderr:()=>Re,stdin:()=>Pe,stdout:()=>Ce,title:()=>Y,umask:()=>oe,uptime:()=>F,version:()=>te,versions:()=>re});var z,G,K,Q,Y,J,X,$,Z,ee,te,re,ie,ne,oe,se,ae,le,ce,ue,he,de,fe,pe,be,ge,me,ye,ve,we,_e,Oe,je,Se,Ee,Ae,xe,ke,Ie,Te,Ce,Re,Pe,Me,Be,Ne,Le,Ue,We,De,Fe,qe,He,Ve,ze,Ge,Ke,Qe,Ye,Je,Xe,$e,Ze,et,tt,rt,it=j((()=>{x(),yt(),nt(),z=[],G=!1,Q=-1,P.prototype.run=function(){this.fun.apply(null,this.array)},Y="browser",J="x64",X="browser",$={PATH:"/usr/bin",LANG:navigator.language+".UTF-8",PWD:"/",HOME:"/home",TMP:"/tmp"},Z=["/usr/bin/node"],ee=[],te="v16.8.0",re={},ie=function(e,t){console.warn((t?t+": ":"")+e)},ne=function(e){I("binding")},oe=function(e){return 0},se=function(){return"/"},ae=function(e){},le={name:"node",sourceUrl:"",headersUrl:"",libUrl:""},ce=M,ue=[],he={},de=!1,fe={},pe=M,be=M,me=ge=function(){return{}},ye=ge,ve=M,we=M,_e=M,Oe={},je={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},Se=M,Ee=M,Ae=M,xe=M,ke=M,Ie=M,Te=M,Ce=void 0,Re=void 0,Pe=void 0,Me=M,Be=2,Ne=1,Le="/bin/usr/node",Ue=9229,We="node",De=[],Fe=M,void 0===(qe={now:typeof performance<"u"?performance.now.bind(performance):void 0,timing:typeof performance<"u"?performance.timing:void 0}).now&&(He=Date.now(),qe.timing&&qe.timing.navigationStart&&(He=qe.timing.navigationStart),qe.now=()=>Date.now()-He),Ve=1e9,q.bigint=function(e){var t=q(e);return typeof BigInt>"u"?t[0]*Ve+t[1]:BigInt(t[0]*Ve)+BigInt(t[1])},rt={version:te,versions:re,arch:J,platform:X,release:le,_rawDebug:ce,moduleLoadList:ue,binding:ne,_linkedBinding:B,_events:Ge={},_eventsCount:Ke=0,_maxListeners:ze=10,on:H,addListener:Qe=H,once:Ye=H,off:Je=H,removeListener:Xe=H,removeAllListeners:$e=H,emit:Ze=M,prependListener:et=H,prependOnceListener:tt=H,listeners:V,domain:he,_exiting:de,config:fe,dlopen:N,uptime:F,_getActiveRequests:L,_getActiveHandles:U,reallyExit:pe,_kill:be,cpuUsage:ge,resourceUsage:me,memoryUsage:ye,kill:ve,exit:we,openStdin:_e,allowedNodeEnvironmentFlags:Oe,assert:W,features:je,_fatalExceptions:Se,setUncaughtExceptionCaptureCallback:Ee,hasUncaughtExceptionCaptureCallback:D,emitWarning:ie,nextTick:R,_tickCallback:Ae,_debugProcess:xe,_debugEnd:ke,_startProfilerIdleNotifier:Ie,_stopProfilerIdleNotifier:Te,stdout:Ce,stdin:Pe,stderr:Re,abort:Me,umask:oe,chdir:ae,cwd:se,env:$,title:Y,argv:Z,execArgv:ee,pid:Be,ppid:Ne,execPath:Le,debugPort:Ue,hrtime:q,argv0:We,_preload_modules:De,setSourceMapsEnabled:Fe}})),nt=j((()=>{it()})),ot={};function st(){if(dt)return ht;dt=!0;let e=function(){if(lt)return at;lt=!0,at.byteLength=function(e){var t=s(e),r=t[0],i=t[1];return 3*(r+i)/4-i},at.toByteArray=function(e){var i,n,o=s(e),a=o[0],l=o[1],c=new r(function(e,t,r){return 3*(t+r)/4-r}(0,a,l)),u=0,h=l>0?a-4:a;for(n=0;n<h;n+=4)i=t[e.charCodeAt(n)]<<18|t[e.charCodeAt(n+1)]<<12|t[e.charCodeAt(n+2)]<<6|t[e.charCodeAt(n+3)],c[u++]=i>>16&255,c[u++]=i>>8&255,c[u++]=255&i;return 2===l&&(i=t[e.charCodeAt(n)]<<2|t[e.charCodeAt(n+1)]>>4,c[u++]=255&i),1===l&&(i=t[e.charCodeAt(n)]<<10|t[e.charCodeAt(n+1)]<<4|t[e.charCodeAt(n+2)]>>2,c[u++]=i>>8&255,c[u++]=255&i),c},at.fromByteArray=function(t){for(var r,i=t.length,n=i%3,o=[],s=16383,a=0,c=i-n;a<c;a+=s)o.push(l(t,a,a+s>c?c:a+s));return 1===n?(r=t[i-1],o.push(e[r>>2]+e[r<<4&63]+"==")):2===n&&(r=(t[i-2]<<8)+t[i-1],o.push(e[r>>10]+e[r>>4&63]+e[r<<2&63]+"=")),o.join("")};for(var e=[],t=[],r=typeof Uint8Array<"u"?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=0,o=i.length;n<o;++n)e[n]=i[n],t[i.charCodeAt(n)]=n;function s(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function a(t){return e[t>>18&63]+e[t>>12&63]+e[t>>6&63]+e[63&t]}function l(e,t,r){for(var i,n=[],o=t;o<r;o+=3)i=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]),n.push(a(i));return n.join("")}return t[45]=62,t[95]=63,at}(),t=(ut||(ut=!0,ct.read=function(e,t,r,i,n){var o,s,a=8*n-i-1,l=(1<<a)-1,c=l>>1,u=-7,h=r?n-1:0,d=r?-1:1,f=e[t+h];for(h+=d,o=f&(1<<-u)-1,f>>=-u,u+=a;u>0;o=256*o+e[t+h],h+=d,u-=8);for(s=o&(1<<-u)-1,o>>=-u,u+=i;u>0;s=256*s+e[t+h],h+=d,u-=8);if(0===o)o=1-c;else{if(o===l)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,i),o-=c}return(f?-1:1)*s*Math.pow(2,o-i)},ct.write=function(e,t,r,i,n,o){var s,a,l,c=8*o-n-1,u=(1<<c)-1,h=u>>1,d=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,f=i?0:o-1,p=i?1:-1,b=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=u):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),(t+=s+h>=1?d/l:d*Math.pow(2,1-h))*l>=2&&(s++,l/=2),s+h>=u?(a=0,s=u):s+h>=1?(a=(t*l-1)*Math.pow(2,n),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,n),s=0));n>=8;e[r+f]=255&a,f+=p,a/=256,n-=8);for(s=s<<n|a,c+=n;c>0;e[r+f]=255&s,f+=p,s/=256,c-=8);e[r+f-p]|=128*b}),ct),r="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;ht.Buffer=o,ht.SlowBuffer=function(e){return+e!=e&&(e=0),o.alloc(+e)},ht.INSPECT_MAX_BYTES=50;let i=2147483647;function n(e){if(e>i)throw new RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,o.prototype),t}function o(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return l(e)}return s(e,t,r)}function s(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!o.isEncoding(t))throw new TypeError("Unknown encoding: "+t);let r=0|d(e,t),i=n(r),s=i.write(e,t);return s!==r&&(i=i.slice(0,s)),i}(e,t);if(ArrayBuffer.isView(e))return function(e){if(G(e,Uint8Array)){let t=new Uint8Array(e);return u(t.buffer,t.byteOffset,t.byteLength)}return c(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(G(e,ArrayBuffer)||e&&G(e.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(G(e,SharedArrayBuffer)||e&&G(e.buffer,SharedArrayBuffer)))return u(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');let i=e.valueOf&&e.valueOf();if(null!=i&&i!==e)return o.from(i,t,r);let s=function(e){if(o.isBuffer(e)){let t=0|h(e.length),r=n(t);return 0===r.length||e.copy(r,0,0,t),r}if(void 0!==e.length)return"number"!=typeof e.length||K(e.length)?n(0):c(e);if("Buffer"===e.type&&Array.isArray(e.data))return c(e.data)}(e);if(s)return s;if(typeof Symbol<"u"&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return o.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function a(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return a(e),n(e<0?0:0|h(e))}function c(e){let t=e.length<0?0:0|h(e.length),r=n(t);for(let i=0;i<t;i+=1)r[i]=255&e[i];return r}function u(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');let i;return i=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),Object.setPrototypeOf(i,o.prototype),i}function h(e){if(e>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|e}function d(e,t){if(o.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||G(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let r=e.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===r)return 0;let n=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return H(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(e).length;default:if(n)return i?-1:H(e).length;t=(""+t).toLowerCase(),n=!0}}function f(e,t,r){let i=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0)||(r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return x(this,t,r);case"utf8":case"utf-8":return j(this,t,r);case"ascii":return E(this,t,r);case"latin1":case"binary":return A(this,t,r);case"base64":return O(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function p(e,t,r){let i=e[t];e[t]=e[r],e[r]=i}function b(e,t,r,i,n){if(0===e.length)return-1;if("string"==typeof r?(i=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),K(r=+r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=o.from(t,i)),o.isBuffer(t))return 0===t.length?-1:g(e,t,r,i,n);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,i,n);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,i,n){let o,s=1,a=e.length,l=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;s=2,a/=2,l/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(n){let i=-1;for(o=r;o<a;o++)if(c(e,o)===c(t,-1===i?0:o-i)){if(-1===i&&(i=o),o-i+1===l)return i*s}else-1!==i&&(o-=o-i),i=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){let r=!0;for(let i=0;i<l;i++)if(c(e,o+i)!==c(t,i)){r=!1;break}if(r)return o}return-1}function m(e,t,r,i){r=Number(r)||0;let n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;let o,s=t.length;for(i>s/2&&(i=s/2),o=0;o<i;++o){let i=parseInt(t.substr(2*o,2),16);if(K(i))return o;e[r+o]=i}return o}function y(e,t,r,i){return z(H(t,e.length-r),e,r,i)}function v(e,t,r,i){return z(function(e){let t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,i)}function w(e,t,r,i){return z(V(t),e,r,i)}function _(e,t,r,i){return z(function(e,t){let r,i,n,o=[];for(let s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),i=r>>8,n=r%256,o.push(n),o.push(i);return o}(t,e.length-r),e,r,i)}function O(t,r,i){return 0===r&&i===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(r,i))}function j(e,t,r){r=Math.min(e.length,r);let i=[],n=t;for(;n<r;){let t=e[n],o=null,s=t>239?4:t>223?3:t>191?2:1;if(n+s<=r){let r,i,a,l;switch(s){case 1:t<128&&(o=t);break;case 2:r=e[n+1],128===(192&r)&&(l=(31&t)<<6|63&r,l>127&&(o=l));break;case 3:r=e[n+1],i=e[n+2],128===(192&r)&&128===(192&i)&&(l=(15&t)<<12|(63&r)<<6|63&i,l>2047&&(l<55296||l>57343)&&(o=l));break;case 4:r=e[n+1],i=e[n+2],a=e[n+3],128===(192&r)&&128===(192&i)&&128===(192&a)&&(l=(15&t)<<18|(63&r)<<12|(63&i)<<6|63&a,l>65535&&l<1114112&&(o=l))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,i.push(o>>>10&1023|55296),o=56320|1023&o),i.push(o),n+=s}return function(e){let t=e.length;if(t<=S)return String.fromCharCode.apply(String,e);let r="",i=0;for(;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=S));return r}(i)}ht.kMaxLength=i,o.TYPED_ARRAY_SUPPORT=function(){try{let e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),!o.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&"function"==typeof console.error&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.buffer}}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.byteOffset}}),o.poolSize=8192,o.from=function(e,t,r){return s(e,t,r)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array),o.alloc=function(e,t,r){return function(e,t,r){return a(e),e<=0?n(e):void 0!==t?"string"==typeof r?n(e).fill(t,r):n(e).fill(t):n(e)}(e,t,r)},o.allocUnsafe=function(e){return l(e)},o.allocUnsafeSlow=function(e){return l(e)},o.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==o.prototype},o.compare=function(e,t){if(G(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),G(t,Uint8Array)&&(t=o.from(t,t.offset,t.byteLength)),!o.isBuffer(e)||!o.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,i=t.length;for(let n=0,o=Math.min(r,i);n<o;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:i<r?1:0},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);let r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;let i=o.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){let t=e[r];if(G(t,Uint8Array))n+t.length>i.length?(o.isBuffer(t)||(t=o.from(t)),t.copy(i,n)):Uint8Array.prototype.set.call(i,t,n);else{if(!o.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(i,n)}n+=t.length}return i},o.byteLength=d,o.prototype._isBuffer=!0,o.prototype.swap16=function(){let e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)p(this,t,t+1);return this},o.prototype.swap32=function(){let e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)p(this,t,t+3),p(this,t+1,t+2);return this},o.prototype.swap64=function(){let e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)p(this,t,t+7),p(this,t+1,t+6),p(this,t+2,t+5),p(this,t+3,t+4);return this},o.prototype.toString=function(){let e=this.length;return 0===e?"":0===arguments.length?j(this,0,e):f.apply(this,arguments)},o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},o.prototype.inspect=function(){let e="",t=ht.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},r&&(o.prototype[r]=o.prototype.inspect),o.prototype.compare=function(e,t,r,i,n){if(G(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),!o.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return-1;if(t>=r)return 1;if(this===e)return 0;let s=(n>>>=0)-(i>>>=0),a=(r>>>=0)-(t>>>=0),l=Math.min(s,a),c=this.slice(i,n),u=e.slice(t,r);for(let o=0;o<l;++o)if(c[o]!==u[o]){s=c[o],a=u[o];break}return s<a?-1:a<s?1:0},o.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},o.prototype.indexOf=function(e,t,r){return b(this,e,t,r,!0)},o.prototype.lastIndexOf=function(e,t,r){return b(this,e,t,r,!1)},o.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}let n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");let o=!1;for(;;)switch(i){case"hex":return m(this,e,t,r);case"utf8":case"utf-8":return y(this,e,t,r);case"ascii":case"latin1":case"binary":return v(this,e,t,r);case"base64":return w(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};let S=4096;function E(e,t,r){let i="";r=Math.min(e.length,r);for(let n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}function A(e,t,r){let i="";r=Math.min(e.length,r);for(let n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}function x(e,t,r){let i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);let n="";for(let o=t;o<r;++o)n+=Q[e[o]];return n}function k(e,t,r){let i=e.slice(t,r),n="";for(let o=0;o<i.length-1;o+=2)n+=String.fromCharCode(i[o]+256*i[o+1]);return n}function I(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function T(e,t,r,i,n,s){if(!o.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<s)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}function C(e,t,r,i,n){W(t,i,n,e,r,7);let o=Number(t&BigInt(4294967295));e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s,r}function R(e,t,r,i,n){W(t,i,n,e,r,7);let o=Number(t&BigInt(4294967295));e[r+7]=o,o>>=8,e[r+6]=o,o>>=8,e[r+5]=o,o>>=8,e[r+4]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[r+3]=s,s>>=8,e[r+2]=s,s>>=8,e[r+1]=s,s>>=8,e[r]=s,r+8}function P(e,t,r,i,n,o){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function M(e,r,i,n,o){return r=+r,i>>>=0,o||P(e,0,i,4),t.write(e,r,i,n,23,4),i+4}function B(e,r,i,n,o){return r=+r,i>>>=0,o||P(e,0,i,8),t.write(e,r,i,n,52,8),i+8}o.prototype.slice=function(e,t){let r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);let i=this.subarray(e,t);return Object.setPrototypeOf(i,o.prototype),i},o.prototype.readUintLE=o.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||I(e,t,this.length);let i=this[e],n=1,o=0;for(;++o<t&&(n*=256);)i+=this[e+o]*n;return i},o.prototype.readUintBE=o.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||I(e,t,this.length);let i=this[e+--t],n=1;for(;t>0&&(n*=256);)i+=this[e+--t]*n;return i},o.prototype.readUint8=o.prototype.readUInt8=function(e,t){return e>>>=0,t||I(e,1,this.length),this[e]},o.prototype.readUint16LE=o.prototype.readUInt16LE=function(e,t){return e>>>=0,t||I(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUint16BE=o.prototype.readUInt16BE=function(e,t){return e>>>=0,t||I(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUint32LE=o.prototype.readUInt32LE=function(e,t){return e>>>=0,t||I(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},o.prototype.readUint32BE=o.prototype.readUInt32BE=function(e,t){return e>>>=0,t||I(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readBigUInt64LE=Y((function(e){D(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&F(e,this.length-8);let i=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,n=this[++e]+256*this[++e]+65536*this[++e]+r*2**24;return BigInt(i)+(BigInt(n)<<BigInt(32))})),o.prototype.readBigUInt64BE=Y((function(e){D(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&F(e,this.length-8);let i=t*2**24+65536*this[++e]+256*this[++e]+this[++e],n=this[++e]*2**24+65536*this[++e]+256*this[++e]+r;return(BigInt(i)<<BigInt(32))+BigInt(n)})),o.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||I(e,t,this.length);let i=this[e],n=1,o=0;for(;++o<t&&(n*=256);)i+=this[e+o]*n;return n*=128,i>=n&&(i-=Math.pow(2,8*t)),i},o.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||I(e,t,this.length);let i=t,n=1,o=this[e+--i];for(;i>0&&(n*=256);)o+=this[e+--i]*n;return n*=128,o>=n&&(o-=Math.pow(2,8*t)),o},o.prototype.readInt8=function(e,t){return e>>>=0,t||I(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},o.prototype.readInt16LE=function(e,t){e>>>=0,t||I(e,2,this.length);let r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt16BE=function(e,t){e>>>=0,t||I(e,2,this.length);let r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt32LE=function(e,t){return e>>>=0,t||I(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,t){return e>>>=0,t||I(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readBigInt64LE=Y((function(e){D(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&F(e,this.length-8);let i=this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24);return(BigInt(i)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),o.prototype.readBigInt64BE=Y((function(e){D(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&F(e,this.length-8);let i=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+r)})),o.prototype.readFloatLE=function(e,r){return e>>>=0,r||I(e,4,this.length),t.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,r){return e>>>=0,r||I(e,4,this.length),t.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,r){return e>>>=0,r||I(e,8,this.length),t.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,r){return e>>>=0,r||I(e,8,this.length),t.read(this,e,!1,52,8)},o.prototype.writeUintLE=o.prototype.writeUIntLE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){T(this,e,t,r,Math.pow(2,8*r)-1,0)}let n=1,o=0;for(this[t]=255&e;++o<r&&(n*=256);)this[t+o]=e/n&255;return t+r},o.prototype.writeUintBE=o.prototype.writeUIntBE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){T(this,e,t,r,Math.pow(2,8*r)-1,0)}let n=r-1,o=1;for(this[t+n]=255&e;--n>=0&&(o*=256);)this[t+n]=e/o&255;return t+r},o.prototype.writeUint8=o.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,1,255,0),this[t]=255&e,t+1},o.prototype.writeUint16LE=o.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},o.prototype.writeUint16BE=o.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},o.prototype.writeUint32LE=o.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},o.prototype.writeUint32BE=o.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},o.prototype.writeBigUInt64LE=Y((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return C(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),o.prototype.writeBigUInt64BE=Y((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return R(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),o.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t>>>=0,!i){let i=Math.pow(2,8*r-1);T(this,e,t,r,i-1,-i)}let n=0,o=1,s=0;for(this[t]=255&e;++n<r&&(o*=256);)e<0&&0===s&&0!==this[t+n-1]&&(s=1),this[t+n]=(e/o>>0)-s&255;return t+r},o.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t>>>=0,!i){let i=Math.pow(2,8*r-1);T(this,e,t,r,i-1,-i)}let n=r-1,o=1,s=0;for(this[t+n]=255&e;--n>=0&&(o*=256);)e<0&&0===s&&0!==this[t+n+1]&&(s=1),this[t+n]=(e/o>>0)-s&255;return t+r},o.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},o.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},o.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},o.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},o.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||T(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},o.prototype.writeBigInt64LE=Y((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return C(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),o.prototype.writeBigInt64BE=Y((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return R(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),o.prototype.writeFloatLE=function(e,t,r){return M(this,e,t,!0,r)},o.prototype.writeFloatBE=function(e,t,r){return M(this,e,t,!1,r)},o.prototype.writeDoubleLE=function(e,t,r){return B(this,e,t,!0,r)},o.prototype.writeDoubleBE=function(e,t,r){return B(this,e,t,!1,r)},o.prototype.copy=function(e,t,r,i){if(!o.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),!i&&0!==i&&(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r||0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);let n=i-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,i):Uint8Array.prototype.set.call(e,this.subarray(r,i),t),n},o.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!o.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(1===e.length){let t=e.charCodeAt(0);("utf8"===i&&t<128||"latin1"===i)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;let n;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(n=t;n<r;++n)this[n]=e;else{let s=o.isBuffer(e)?e:o.from(e,i),a=s.length;if(0===a)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(n=0;n<r-t;++n)this[n+t]=s[n%a]}return this};let N={};function L(e,t,r){N[e]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name="".concat(this.name," [").concat(e,"]"),this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return"".concat(this.name," [").concat(e,"]: ").concat(this.message)}}}function U(e){let t="",r=e.length,i="-"===e[0]?1:0;for(;r>=i+4;r-=3)t="_".concat(e.slice(r-3,r)).concat(t);return"".concat(e.slice(0,r)).concat(t)}function W(e,t,r,i,n,o){if(e>r||e<t){let i,n="bigint"==typeof t?"n":"";throw i=o>3?0===t||t===BigInt(0)?">= 0".concat(n," and < 2").concat(n," ** ").concat(8*(o+1)).concat(n):">= -(2".concat(n," ** ").concat(8*(o+1)-1).concat(n,") and < 2 ** ").concat(8*(o+1)-1).concat(n):">= ".concat(t).concat(n," and <= ").concat(r).concat(n),new N.ERR_OUT_OF_RANGE("value",i,e)}!function(e,t,r){D(t,"offset"),(void 0===e[t]||void 0===e[t+r])&&F(t,e.length-(r+1))}(i,n,o)}function D(e,t){if("number"!=typeof e)throw new N.ERR_INVALID_ARG_TYPE(t,"number",e)}function F(e,t,r){throw Math.floor(e)!==e?(D(e,r),new N.ERR_OUT_OF_RANGE(r||"offset","an integer",e)):t<0?new N.ERR_BUFFER_OUT_OF_BOUNDS:new N.ERR_OUT_OF_RANGE(r||"offset",">= ".concat(r?1:0," and <= ").concat(t),e)}L("ERR_BUFFER_OUT_OF_BOUNDS",(function(e){return e?"".concat(e," is outside of buffer bounds"):"Attempt to access memory outside buffer bounds"}),RangeError),L("ERR_INVALID_ARG_TYPE",(function(e,t){return'The "'.concat(e,'" argument must be of type number. Received type ').concat(typeof t)}),TypeError),L("ERR_OUT_OF_RANGE",(function(e,t,r){let i='The value of "'.concat(e,'" is out of range.'),n=r;return Number.isInteger(r)&&Math.abs(r)>2**32?n=U(String(r)):"bigint"==typeof r&&(n=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(n=U(n)),n+="n"),i+=" It must be ".concat(t,". Received ").concat(n),i}),RangeError);let q=/[^+/0-9A-Za-z-_]/g;function H(e,t){t=t||1/0;let r,i=e.length,n=null,o=[];for(let s=0;s<i;++s){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===i){(t-=3)>-1&&o.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&o.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function V(t){return e.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(q,"")).length<2)return"";for(;e.length%4!==0;)e+="=";return e}(t))}function z(e,t,r,i){let n;for(n=0;n<i&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}function G(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function K(e){return e!==e}let Q=function(){let e="0123456789abcdef",t=new Array(256);for(let r=0;r<16;++r){let i=16*r;for(let n=0;n<16;++n)t[i+n]=e[r]+e[n]}return t}();function Y(e){return typeof BigInt>"u"?J:e}function J(){throw new Error("BigInt not supported")}return ht}E(ot,{Buffer:()=>pt,INSPECT_MAX_BYTES:()=>bt,default:()=>ft,kMaxLength:()=>gt});var at,lt,ct,ut,ht,dt,ft,pt,bt,gt,mt=j((()=>{x(),yt(),nt(),at={},lt=!1,ct={},ut=!1,ht={},dt=!1,(ft=st()).Buffer,ft.SlowBuffer,ft.INSPECT_MAX_BYTES,ft.kMaxLength,pt=ft.Buffer,bt=ft.INSPECT_MAX_BYTES,gt=ft.kMaxLength})),yt=j((()=>{mt()})),vt=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(e){this.aliasToTopic={},this.max=e}put(e,t){return!(0===t||t>this.max)&&(this.aliasToTopic[t]=e,this.length=Object.keys(this.aliasToTopic).length,!0)}getTopicByAlias(e){return this.aliasToTopic[e]}clear(){this.aliasToTopic={}}}})),wt=S(((e,t)=>{x(),yt(),nt();var r=class extends Error{constructor(e){if(!Array.isArray(e))throw new TypeError("Expected input to be an Array, got ".concat(typeof e));let t="";for(let r=0;r<e.length;r++)t+="    ".concat(e[r].stack,"\n");super(t),this.name="AggregateError",this.errors=e}};t.exports={AggregateError:r,ArrayIsArray:e=>Array.isArray(e),ArrayPrototypeIncludes:(e,t)=>e.includes(t),ArrayPrototypeIndexOf:(e,t)=>e.indexOf(t),ArrayPrototypeJoin:(e,t)=>e.join(t),ArrayPrototypeMap:(e,t)=>e.map(t),ArrayPrototypePop:(e,t)=>e.pop(t),ArrayPrototypePush:(e,t)=>e.push(t),ArrayPrototypeSlice:(e,t,r)=>e.slice(t,r),Error:Error,FunctionPrototypeCall(e,t){for(var r=arguments.length,i=new Array(r>2?r-2:0),n=2;n<r;n++)i[n-2]=arguments[n];return e.call(t,...i)},FunctionPrototypeSymbolHasInstance:(e,t)=>Function.prototype[Symbol.hasInstance].call(e,t),MathFloor:Math.floor,Number:Number,NumberIsInteger:Number.isInteger,NumberIsNaN:Number.isNaN,NumberMAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER,NumberMIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER,NumberParseInt:Number.parseInt,ObjectDefineProperties:(e,t)=>Object.defineProperties(e,t),ObjectDefineProperty:(e,t,r)=>Object.defineProperty(e,t,r),ObjectGetOwnPropertyDescriptor:(e,t)=>Object.getOwnPropertyDescriptor(e,t),ObjectKeys:e=>Object.keys(e),ObjectSetPrototypeOf:(e,t)=>Object.setPrototypeOf(e,t),Promise:Promise,PromisePrototypeCatch:(e,t)=>e.catch(t),PromisePrototypeThen:(e,t,r)=>e.then(t,r),PromiseReject:e=>Promise.reject(e),PromiseResolve:e=>Promise.resolve(e),ReflectApply:Reflect.apply,RegExpPrototypeTest:(e,t)=>e.test(t),SafeSet:Set,String:String,StringPrototypeSlice:(e,t,r)=>e.slice(t,r),StringPrototypeToLowerCase:e=>e.toLowerCase(),StringPrototypeToUpperCase:e=>e.toUpperCase(),StringPrototypeTrim:e=>e.trim(),Symbol:Symbol,SymbolFor:Symbol.for,SymbolAsyncIterator:Symbol.asyncIterator,SymbolHasInstance:Symbol.hasInstance,SymbolIterator:Symbol.iterator,SymbolDispose:Symbol.dispose||Symbol("Symbol.dispose"),SymbolAsyncDispose:Symbol.asyncDispose||Symbol("Symbol.asyncDispose"),TypedArrayPrototypeSet:(e,t,r)=>e.set(t,r),Boolean:Boolean,Uint8Array:Uint8Array}})),_t=S(((e,t)=>{x(),yt(),nt(),t.exports={format(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return e.replace(/%([sdifj])/g,(function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];let[n,o]=t,s=r.shift();return"f"===o?s.toFixed(6):"j"===o?JSON.stringify(s):"s"===o&&"object"==typeof s?"".concat(s.constructor!==Object?s.constructor.name:""," {}").trim():s.toString()}))},inspect(e){switch(typeof e){case"string":if(e.includes("'")){if(!e.includes('"'))return'"'.concat(e,'"');if(!e.includes("`")&&!e.includes("${"))return"`".concat(e,"`")}return"'".concat(e,"'");case"number":return isNaN(e)?"NaN":Object.is(e,-0)?String(e):e;case"bigint":return"".concat(String(e),"n");case"boolean":case"undefined":return String(e);case"object":return"{}"}}}})),Ot=S(((e,t)=>{x(),yt(),nt();var{format:r,inspect:i}=_t(),{AggregateError:n}=wt(),o=globalThis.AggregateError||n,s=Symbol("kIsNodeError"),a=["string","function","number","object","Function","Object","boolean","bigint","symbol"],l=/^([A-Z][a-z0-9]*)+$/,c={};function u(e,t){if(!e)throw new c.ERR_INTERNAL_ASSERTION(t)}function h(e){let t="",r=e.length,i="-"===e[0]?1:0;for(;r>=i+4;r-=3)t="_".concat(e.slice(r-3,r)).concat(t);return"".concat(e.slice(0,r)).concat(t)}function d(e,t,i){i||(i=Error);class n extends i{constructor(){for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];super(function(e,t,i){if("function"==typeof t)return u(t.length<=i.length,"Code: ".concat(e,"; The provided arguments length (").concat(i.length,") does not match the required ones (").concat(t.length,").")),t(...i);let n=(t.match(/%[dfijoOs]/g)||[]).length;return u(n===i.length,"Code: ".concat(e,"; The provided arguments length (").concat(i.length,") does not match the required ones (").concat(n,").")),0===i.length?t:r(t,...i)}(e,t,n))}toString(){return"".concat(this.name," [").concat(e,"]: ").concat(this.message)}}Object.defineProperties(n.prototype,{name:{value:i.name,writable:!0,enumerable:!1,configurable:!0},toString:{value(){return"".concat(this.name," [").concat(e,"]: ").concat(this.message)},writable:!0,enumerable:!1,configurable:!0}}),n.prototype.code=e,n.prototype[s]=!0,c[e]=n}function f(e){let t="__node_internal_"+e.name;return Object.defineProperty(e,"name",{value:t}),e}var p=class extends Error{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"The operation was aborted",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0!==t&&"object"!=typeof t)throw new c.ERR_INVALID_ARG_TYPE("options","Object",t);super(e,t),this.code="ABORT_ERR",this.name="AbortError"}};d("ERR_ASSERTION","%s",Error),d("ERR_INVALID_ARG_TYPE",((e,t,r)=>{u("string"==typeof e,"'name' must be a string"),Array.isArray(t)||(t=[t]);let n="The ";e.endsWith(" argument")?n+="".concat(e," "):n+='"'.concat(e,'" ').concat(e.includes(".")?"property":"argument"," "),n+="must be ";let o=[],s=[],c=[];for(let i of t)u("string"==typeof i,"All expected entries have to be of type string"),a.includes(i)?o.push(i.toLowerCase()):l.test(i)?s.push(i):(u("object"!==i,'The value "object" should be written as "Object"'),c.push(i));if(s.length>0){let e=o.indexOf("object");-1!==e&&(o.splice(o,e,1),s.push("Object"))}if(o.length>0){switch(o.length){case 1:n+="of type ".concat(o[0]);break;case 2:n+="one of type ".concat(o[0]," or ").concat(o[1]);break;default:{let e=o.pop();n+="one of type ".concat(o.join(", "),", or ").concat(e)}}(s.length>0||c.length>0)&&(n+=" or ")}if(s.length>0){switch(s.length){case 1:n+="an instance of ".concat(s[0]);break;case 2:n+="an instance of ".concat(s[0]," or ").concat(s[1]);break;default:{let e=s.pop();n+="an instance of ".concat(s.join(", "),", or ").concat(e)}}c.length>0&&(n+=" or ")}switch(c.length){case 0:break;case 1:c[0].toLowerCase()!==c[0]&&(n+="an "),n+="".concat(c[0]);break;case 2:n+="one of ".concat(c[0]," or ").concat(c[1]);break;default:{let e=c.pop();n+="one of ".concat(c.join(", "),", or ").concat(e)}}if(null==r)n+=". Received ".concat(r);else if("function"==typeof r&&r.name)n+=". Received function ".concat(r.name);else if("object"==typeof r){var h;if(null!==(h=r.constructor)&&void 0!==h&&h.name)n+=". Received an instance of ".concat(r.constructor.name);else{let e=i(r,{depth:-1});n+=". Received ".concat(e)}}else{let e=i(r,{colors:!1});e.length>25&&(e="".concat(e.slice(0,25),"...")),n+=". Received type ".concat(typeof r," (").concat(e,")")}return n}),TypeError),d("ERR_INVALID_ARG_VALUE",(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"is invalid",n=i(t);return n.length>128&&(n=n.slice(0,128)+"..."),"The ".concat(e.includes(".")?"property":"argument"," '").concat(e,"' ").concat(r,". Received ").concat(n)}),TypeError),d("ERR_INVALID_RETURN_VALUE",((e,t,r)=>{var i;let n=null!=r&&null!==(i=r.constructor)&&void 0!==i&&i.name?"instance of ".concat(r.constructor.name):"type ".concat(typeof r);return"Expected ".concat(e,' to be returned from the "').concat(t,'" function but got ').concat(n,".")}),TypeError),d("ERR_MISSING_ARGS",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];u(t.length>0,"At least one arg needs to be specified");let i,n=t.length;switch(t=(Array.isArray(t)?t:[t]).map((e=>'"'.concat(e,'"'))).join(" or "),n){case 1:i+="The ".concat(t[0]," argument");break;case 2:i+="The ".concat(t[0]," and ").concat(t[1]," arguments");break;default:{let e=t.pop();i+="The ".concat(t.join(", "),", and ").concat(e," arguments")}}return"".concat(i," must be specified")}),TypeError),d("ERR_OUT_OF_RANGE",((e,t,r)=>{let n;if(u(t,'Missing "range" argument'),Number.isInteger(r)&&Math.abs(r)>2**32)n=h(String(r));else if("bigint"==typeof r){n=String(r);let e=BigInt(2)**BigInt(32);(r>e||r<-e)&&(n=h(n)),n+="n"}else n=i(r);return'The value of "'.concat(e,'" is out of range. It must be ').concat(t,". Received ").concat(n)}),RangeError),d("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error),d("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error),d("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error),d("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error),d("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error),d("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),d("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error),d("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error),d("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error),d("ERR_STREAM_WRITE_AFTER_END","write after end",Error),d("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError),t.exports={AbortError:p,aggregateTwoErrors:f((function(e,t){if(e&&t&&e!==t){if(Array.isArray(t.errors))return t.errors.push(e),t;let r=new o([t,e],t.message);return r.code=t.code,r}return e||t})),hideStackFrames:f,codes:c}})),jt=S(((e,t)=>{x(),yt(),nt();var{AbortController:r,AbortSignal:i}=typeof self<"u"?self:typeof window<"u"?window:void 0;t.exports=r,t.exports.AbortSignal=i,t.exports.default=r}));function St(){St.init.call(this)}function Et(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function At(e){return void 0===e._maxListeners?St.defaultMaxListeners:e._maxListeners}function xt(e,t,r,i){var n,o,s,a;if(Et(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),s=o[t]),void 0===s)s=o[t]=r,++e._eventsCount;else if("function"==typeof s?s=o[t]=i?[r,s]:[s,r]:i?s.unshift(r):s.push(r),(n=At(e))>0&&s.length>n&&!s.warned){s.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=s.length,a=l,console&&console.warn&&console.warn(a)}return e}function kt(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function It(e,t,r){var i={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},n=kt.bind(i);return n.listener=r,i.wrapFn=n,n}function Tt(e,t,r){var i=e._events;if(void 0===i)return[];var n=i[t];return void 0===n?[]:"function"==typeof n?r?[n.listener||n]:[n]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(n):Rt(n,n.length)}function Ct(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function Rt(e,t){for(var r=new Array(t),i=0;i<t;++i)r[i]=e[i];return r}var Pt,Mt,Bt,Nt,Lt,Ut,Wt,Dt=j((()=>{x(),yt(),nt(),Bt="object"==typeof Reflect?Reflect:null,Nt=Bt&&"function"==typeof Bt.apply?Bt.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)},Mt=Bt&&"function"==typeof Bt.ownKeys?Bt.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)},Lt=Number.isNaN||function(e){return e!=e},Pt=St,St.EventEmitter=St,St.prototype._events=void 0,St.prototype._eventsCount=0,St.prototype._maxListeners=void 0,Ut=10,Object.defineProperty(St,"defaultMaxListeners",{enumerable:!0,get:function(){return Ut},set:function(e){if("number"!=typeof e||e<0||Lt(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");Ut=e}}),St.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},St.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||Lt(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},St.prototype.getMaxListeners=function(){return At(this)},St.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,n=this._events;if(void 0!==n)i=i&&void 0===n.error;else if(!i)return!1;if(i){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var a=n[e];if(void 0===a)return!1;if("function"==typeof a)Nt(a,this,t);else{var l=a.length,c=Rt(a,l);for(r=0;r<l;++r)Nt(c[r],this,t)}return!0},St.prototype.addListener=function(e,t){return xt(this,e,t,!1)},St.prototype.on=St.prototype.addListener,St.prototype.prependListener=function(e,t){return xt(this,e,t,!0)},St.prototype.once=function(e,t){return Et(t),this.on(e,It(this,e,t)),this},St.prototype.prependOnceListener=function(e,t){return Et(t),this.prependListener(e,It(this,e,t)),this},St.prototype.removeListener=function(e,t){var r,i,n,o,s;if(Et(t),void 0===(i=this._events))return this;if(void 0===(r=i[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(n=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){s=r[o].listener,n=o;break}if(n<0)return this;0===n?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,n),1===r.length&&(i[e]=r[0]),void 0!==i.removeListener&&this.emit("removeListener",e,s||t)}return this},St.prototype.off=St.prototype.removeListener,St.prototype.removeAllListeners=function(e){var t,r,i;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var n,o=Object.keys(r);for(i=0;i<o.length;++i)"removeListener"!==(n=o[i])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this},St.prototype.listeners=function(e){return Tt(this,e,!0)},St.prototype.rawListeners=function(e){return Tt(this,e,!1)},St.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):Ct.call(e,t)},St.prototype.listenerCount=Ct,St.prototype.eventNames=function(){return this._eventsCount>0?Mt(this._events):[]},(Wt=Pt).EventEmitter,Wt.defaultMaxListeners,Wt.init,Wt.listenerCount,Wt.EventEmitter,Wt.defaultMaxListeners,Wt.init,Wt.listenerCount})),Ft={};E(Ft,{EventEmitter:()=>qt,default:()=>Wt,defaultMaxListeners:()=>Ht,init:()=>Vt,listenerCount:()=>zt,on:()=>Gt,once:()=>Kt});var qt,Ht,Vt,zt,Gt,Kt,Qt=j((()=>{x(),yt(),nt(),Dt(),Dt(),Wt.once=function(e,t){return new Promise(((r,i)=>{function n(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];void 0!==o&&e.removeListener("error",o),r(i)}let o;"error"!==t&&(o=t=>{e.removeListener(name,n),i(t)},e.once("error",o)),e.once(t,n)}))},Wt.on=function(e,t){let r=[],i=[],n=null,o=!1,s={async next(){let e=r.shift();if(e)return createIterResult(e,!1);if(n){let e=Promise.reject(n);return n=null,e}return o?createIterResult(void 0,!0):new Promise(((e,t)=>i.push({resolve:e,reject:t})))},async return(){e.removeListener(t,a),e.removeListener("error",l),o=!0;for(let e of i)e.resolve(createIterResult(void 0,!0));return createIterResult(void 0,!0)},throw(r){n=r,e.removeListener(t,a),e.removeListener("error",l)},[Symbol.asyncIterator](){return this}};return e.on(t,a),e.on("error",l),s;function a(){let e=i.shift();for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];e?e.resolve(createIterResult(n,!1)):r.push(n)}function l(e){o=!0;let t=i.shift();t?t.reject(e):n=e,s.return()}},({EventEmitter:qt,defaultMaxListeners:Ht,init:Vt,listenerCount:zt,on:Gt,once:Kt}=Wt)})),Yt=S(((e,t)=>{x(),yt(),nt();var r=(mt(),A(ot)),{format:i,inspect:n}=_t(),{codes:{ERR_INVALID_ARG_TYPE:o}}=Ot(),{kResistStopPropagation:s,AggregateError:a,SymbolDispose:l}=wt(),c=globalThis.AbortSignal||jt().AbortSignal,u=globalThis.AbortController||jt().AbortController,h=Object.getPrototypeOf((async function(){})).constructor,d=globalThis.Blob||r.Blob,f=typeof d<"u"?function(e){return e instanceof d}:function(e){return!1},p=(e,t)=>{if(void 0!==e&&(null===e||"object"!=typeof e||!("aborted"in e)))throw new o(t,"AbortSignal",e)};t.exports={AggregateError:a,kEmptyObject:Object.freeze({}),once(e){let t=!1;return function(){for(var r=arguments.length,i=new Array(r),n=0;n<r;n++)i[n]=arguments[n];t||(t=!0,e.apply(this,i))}},createDeferredPromise:function(){let e,t;return{promise:new Promise(((r,i)=>{e=r,t=i})),resolve:e,reject:t}},promisify:e=>new Promise(((t,r)=>{e((function(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return e?r(e):t(...n)}))})),debuglog:()=>function(){},format:i,inspect:n,types:{isAsyncFunction:e=>e instanceof h,isArrayBufferView:e=>ArrayBuffer.isView(e)},isBlob:f,deprecate:(e,t)=>e,addAbortListener:(Qt(),A(Ft)).addAbortListener||function(e,t){if(void 0===e)throw new o("signal","AbortSignal",e);let r;return p(e,"signal"),((e,t)=>{if("function"!=typeof e)throw new o(t,"Function",e)})(t,"listener"),e.aborted?queueMicrotask((()=>t())):(e.addEventListener("abort",t,{__proto__:null,once:!0,[s]:!0}),r=()=>{e.removeEventListener("abort",t)}),{__proto__:null,[l](){var e;null===(e=r)||void 0===e||e()}}},AbortSignalAny:c.any||function(e){if(1===e.length)return e[0];let t=new u,r=()=>t.abort();return e.forEach((e=>{p(e,"signals"),e.addEventListener("abort",r,{once:!0})})),t.signal.addEventListener("abort",(()=>{e.forEach((e=>e.removeEventListener("abort",r)))}),{once:!0}),t.signal}},t.exports.promisify.custom=Symbol.for("nodejs.util.promisify.custom")})),Jt=S(((e,t)=>{x(),yt(),nt();var{ArrayIsArray:r,ArrayPrototypeIncludes:i,ArrayPrototypeJoin:n,ArrayPrototypeMap:o,NumberIsInteger:s,NumberIsNaN:a,NumberMAX_SAFE_INTEGER:l,NumberMIN_SAFE_INTEGER:c,NumberParseInt:u,ObjectPrototypeHasOwnProperty:h,RegExpPrototypeExec:d,String:f,StringPrototypeToUpperCase:p,StringPrototypeTrim:b}=wt(),{hideStackFrames:g,codes:{ERR_SOCKET_BAD_PORT:m,ERR_INVALID_ARG_TYPE:y,ERR_INVALID_ARG_VALUE:v,ERR_OUT_OF_RANGE:w,ERR_UNKNOWN_SIGNAL:_}}=Ot(),{normalizeEncoding:O}=Yt(),{isAsyncFunction:j,isArrayBufferView:S}=Yt().types,E={};var A=/^[0-7]+$/;var k=g((function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:l;if("number"!=typeof e)throw new y(t,"number",e);if(!s(e))throw new w(t,"an integer",e);if(e<r||e>i)throw new w(t,">= ".concat(r," && <= ").concat(i),e)})),I=g((function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-2147483648,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2147483647;if("number"!=typeof e)throw new y(t,"number",e);if(!s(e))throw new w(t,"an integer",e);if(e<r||e>i)throw new w(t,">= ".concat(r," && <= ").concat(i),e)})),T=g((function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("number"!=typeof e)throw new y(t,"number",e);if(!s(e))throw new w(t,"an integer",e);let i=r?1:0,n=4294967295;if(e<i||e>n)throw new w(t,">= ".concat(i," && <= ").concat(n),e)}));function C(e,t){if("string"!=typeof e)throw new y(t,"string",e)}var R=g(((e,t,r)=>{if(!i(r,e)){let i="must be one of: "+n(o(r,(e=>"string"==typeof e?"'".concat(e,"'"):f(e))),", ");throw new v(t,e,i)}}));function P(e,t){if("boolean"!=typeof e)throw new y(t,"boolean",e)}function M(e,t,r){return null!=e&&h(e,t)?e[t]:r}var B=g((function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=M(i,"allowArray",!1),o=M(i,"allowFunction",!1);if(!M(i,"nullable",!1)&&null===e||!n&&r(e)||"object"!=typeof e&&(!o||"function"!=typeof e))throw new y(t,"Object",e)})),N=g(((e,t)=>{if(null!=e&&"object"!=typeof e&&"function"!=typeof e)throw new y(t,"a dictionary",e)})),L=g((function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!r(e))throw new y(t,"Array",e);if(e.length<i){let r="must be longer than ".concat(i);throw new v(t,e,r)}}));var U=g((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"buffer";if(!S(e))throw new y(t,["Buffer","TypedArray","DataView"],e)}));var W=g(((e,t)=>{if(void 0!==e&&(null===e||"object"!=typeof e||!("aborted"in e)))throw new y(t,"AbortSignal",e)})),D=g(((e,t)=>{if("function"!=typeof e)throw new y(t,"Function",e)})),F=g(((e,t)=>{if("function"!=typeof e||j(e))throw new y(t,"Function",e)})),q=g(((e,t)=>{if(void 0!==e)throw new y(t,"undefined",e)}));var H=/^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;function V(e,t){if(typeof e>"u"||!d(H,e))throw new v(t,e,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}t.exports={isInt32:function(e){return e===(0|e)},isUint32:function(e){return e===e>>>0},parseFileMode:function(e,t,r){if(typeof e>"u"&&(e=r),"string"==typeof e){if(null===d(A,e))throw new v(t,e,"must be a 32-bit unsigned integer or an octal string");e=u(e,8)}return T(e,t),e},validateArray:L,validateStringArray:function(e,t){L(e,t);for(let r=0;r<e.length;r++)C(e[r],"".concat(t,"[").concat(r,"]"))},validateBooleanArray:function(e,t){L(e,t);for(let r=0;r<e.length;r++)P(e[r],"".concat(t,"[").concat(r,"]"))},validateAbortSignalArray:function(e,t){L(e,t);for(let r=0;r<e.length;r++){let i=e[r],n="".concat(t,"[").concat(r,"]");if(null==i)throw new y(n,"AbortSignal",i);W(i,n)}},validateBoolean:P,validateBuffer:U,validateDictionary:N,validateEncoding:function(e,t){let r=O(t),i=e.length;if("hex"===r&&i%2!==0)throw new v("encoding",t,"is invalid for data of length ".concat(i))},validateFunction:D,validateInt32:I,validateInteger:k,validateNumber:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;if("number"!=typeof e)throw new y(t,"number",e);if(null!=r&&e<r||null!=i&&e>i||(null!=r||null!=i)&&a(e))throw new w(t,"".concat(null!=r?">= ".concat(r):"").concat(null!=r&&null!=i?" && ":"").concat(null!=i?"<= ".concat(i):""),e)},validateObject:B,validateOneOf:R,validatePlainFunction:F,validatePort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Port",r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("number"!=typeof e&&"string"!=typeof e||"string"==typeof e&&0===b(e).length||+e!==+e>>>0||e>65535||0===e&&!r)throw new m(t,e,r);return 0|e},validateSignalName:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"signal";if(C(e,t),void 0===E[e])throw void 0!==E[p(e)]?new _(e+" (signals must use all capital letters)"):new _(e)},validateString:C,validateUint32:T,validateUndefined:q,validateUnion:function(e,t,r){if(!i(r,e))throw new y(t,"('".concat(n(r,"|"),"')"),e)},validateAbortSignal:W,validateLinkHeaderValue:function(e){if("string"==typeof e)return V(e,"hints"),e;if(r(e)){let t=e.length,r="";if(0===t)return r;for(let i=0;i<t;i++){let n=e[i];V(n,"hints"),r+=n,i!==t-1&&(r+=", ")}return r}throw new v("hints",e,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}}})),Xt=S(((e,t)=>{x(),yt(),nt();var r,i,n=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(i){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{i="function"==typeof clearTimeout?clearTimeout:s}catch(t){i=s}}();var l,c=[],u=!1,h=-1;function d(){!u||!l||(u=!1,l.length?c=l.concat(c):h=-1,c.length&&f())}function f(){if(!u){var e=a(d);u=!0;for(var t=c.length;t;){for(l=c,c=[];++h<t;)l&&l[h].run();h=-1,t=c.length}l=null,u=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===s||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(r){return i.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function b(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new p(e,t)),1===c.length&&!u&&a(f)},p.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=b,n.addListener=b,n.once=b,n.off=b,n.removeListener=b,n.removeAllListeners=b,n.emit=b,n.prependListener=b,n.prependOnceListener=b,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}})),$t=S(((e,t)=>{x(),yt(),nt();var{SymbolAsyncIterator:r,SymbolIterator:i,SymbolFor:n}=wt(),o=n("nodejs.stream.destroyed"),s=n("nodejs.stream.errored"),a=n("nodejs.stream.readable"),l=n("nodejs.stream.writable"),c=n("nodejs.stream.disturbed"),u=n("nodejs.webstream.isClosedPromise"),h=n("nodejs.webstream.controllerErrorFunction");function d(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var r;return!(!e||"function"!=typeof e.pipe||"function"!=typeof e.on||t&&("function"!=typeof e.pause||"function"!=typeof e.resume)||e._writableState&&!1===(null===(r=e._readableState)||void 0===r?void 0:r.readable)||e._writableState&&!e._readableState)}function f(e){var t;return!(!e||"function"!=typeof e.write||"function"!=typeof e.on||e._readableState&&!1===(null===(t=e._writableState)||void 0===t?void 0:t.writable))}function p(e){return e&&(e._readableState||e._writableState||"function"==typeof e.write&&"function"==typeof e.on||"function"==typeof e.pipe&&"function"==typeof e.on)}function b(e){return!(!e||p(e)||"function"!=typeof e.pipeThrough||"function"!=typeof e.getReader||"function"!=typeof e.cancel)}function g(e){return!(!e||p(e)||"function"!=typeof e.getWriter||"function"!=typeof e.abort)}function m(e){return!(!e||p(e)||"object"!=typeof e.readable||"object"!=typeof e.writable)}function y(e){if(!p(e))return null;let t=e._writableState,r=e._readableState,i=t||r;return!!(e.destroyed||e[o]||null!=i&&i.destroyed)}function v(e){if(!f(e))return null;if(!0===e.writableEnded)return!0;let t=e._writableState;return(null==t||!t.errored)&&("boolean"!=typeof(null===t||void 0===t?void 0:t.ended)?null:t.ended)}function w(e,t){if(!d(e))return null;let r=e._readableState;return(null==r||!r.errored)&&("boolean"!=typeof(null===r||void 0===r?void 0:r.endEmitted)?null:!!(r.endEmitted||!1===t&&!0===r.ended&&0===r.length))}function _(e){return e&&null!=e[a]?e[a]:"boolean"!=typeof(null===e||void 0===e?void 0:e.readable)?null:!y(e)&&(d(e)&&e.readable&&!w(e))}function O(e){return e&&null!=e[l]?e[l]:"boolean"!=typeof(null===e||void 0===e?void 0:e.writable)?null:!y(e)&&(f(e)&&e.writable&&!v(e))}function j(e){return"boolean"==typeof e._closed&&"boolean"==typeof e._defaultKeepAlive&&"boolean"==typeof e._removedConnection&&"boolean"==typeof e._removedContLen}function S(e){return"boolean"==typeof e._sent100&&j(e)}t.exports={isDestroyed:y,kIsDestroyed:o,isDisturbed:function(e){var t;return!(!e||!(null!==(t=e[c])&&void 0!==t?t:e.readableDidRead||e.readableAborted))},kIsDisturbed:c,isErrored:function(e){var t,r,i,n,o,a,l,c,u,h;return!(!e||!(null!==(t=null!==(r=null!==(i=null!==(n=null!==(o=null!==(a=e[s])&&void 0!==a?a:e.readableErrored)&&void 0!==o?o:e.writableErrored)&&void 0!==n?n:null===(l=e._readableState)||void 0===l?void 0:l.errorEmitted)&&void 0!==i?i:null===(c=e._writableState)||void 0===c?void 0:c.errorEmitted)&&void 0!==r?r:null===(u=e._readableState)||void 0===u?void 0:u.errored)&&void 0!==t?t:null!==(h=e._writableState)&&void 0!==h&&h.errored))},kIsErrored:s,isReadable:_,kIsReadable:a,kIsClosedPromise:u,kControllerErrorFunction:h,kIsWritable:l,isClosed:function(e){if(!p(e))return null;if("boolean"==typeof e.closed)return e.closed;let t=e._writableState,r=e._readableState;return"boolean"==typeof(null===t||void 0===t?void 0:t.closed)||"boolean"==typeof(null===r||void 0===r?void 0:r.closed)?(null===t||void 0===t?void 0:t.closed)||(null===r||void 0===r?void 0:r.closed):"boolean"==typeof e._closed&&j(e)?e._closed:null},isDuplexNodeStream:function(e){return!(!e||"function"!=typeof e.pipe||!e._readableState||"function"!=typeof e.on||"function"!=typeof e.write)},isFinished:function(e,t){return p(e)?!!y(e)||!(!1!==(null===t||void 0===t?void 0:t.readable)&&_(e)||!1!==(null===t||void 0===t?void 0:t.writable)&&O(e)):null},isIterable:function(e,t){return null!=e&&(!0===t?"function"==typeof e[r]:!1===t?"function"==typeof e[i]:"function"==typeof e[r]||"function"==typeof e[i])},isReadableNodeStream:d,isReadableStream:b,isReadableEnded:function(e){if(!d(e))return null;if(!0===e.readableEnded)return!0;let t=e._readableState;return!(!t||t.errored)&&("boolean"!=typeof(null===t||void 0===t?void 0:t.ended)?null:t.ended)},isReadableFinished:w,isReadableErrored:function(e){var t,r;return p(e)?e.readableErrored?e.readableErrored:null!==(t=null===(r=e._readableState)||void 0===r?void 0:r.errored)&&void 0!==t?t:null:null},isNodeStream:p,isWebStream:function(e){return b(e)||g(e)||m(e)},isWritable:O,isWritableNodeStream:f,isWritableStream:g,isWritableEnded:v,isWritableFinished:function(e,t){if(!f(e))return null;if(!0===e.writableFinished)return!0;let r=e._writableState;return(null==r||!r.errored)&&("boolean"!=typeof(null===r||void 0===r?void 0:r.finished)?null:!!(r.finished||!1===t&&!0===r.ended&&0===r.length))},isWritableErrored:function(e){var t,r;return p(e)?e.writableErrored?e.writableErrored:null!==(t=null===(r=e._writableState)||void 0===r?void 0:r.errored)&&void 0!==t?t:null:null},isServerRequest:function(e){var t;return"boolean"==typeof e._consuming&&"boolean"==typeof e._dumped&&void 0===(null===(t=e.req)||void 0===t?void 0:t.upgradeOrConnect)},isServerResponse:S,willEmitClose:function(e){if(!p(e))return null;let t=e._writableState,r=e._readableState,i=t||r;return!i&&S(e)||!!(i&&i.autoDestroy&&i.emitClose&&!1===i.closed)},isTransformStream:m}})),Zt=S(((e,t)=>{x(),yt(),nt();var r,i=Xt(),{AbortError:n,codes:o}=Ot(),{ERR_INVALID_ARG_TYPE:s,ERR_STREAM_PREMATURE_CLOSE:a}=o,{kEmptyObject:l,once:c}=Yt(),{validateAbortSignal:u,validateFunction:h,validateObject:d,validateBoolean:f}=Jt(),{Promise:p,PromisePrototypeThen:b,SymbolDispose:g}=wt(),{isClosed:m,isReadable:y,isReadableNodeStream:v,isReadableStream:w,isReadableFinished:_,isReadableErrored:O,isWritable:j,isWritableNodeStream:S,isWritableStream:E,isWritableFinished:A,isWritableErrored:k,isNodeStream:I,willEmitClose:T,kIsClosedPromise:C}=$t();function R(e){return e.setHeader&&"function"==typeof e.abort}var P=()=>{};function M(e,t,o){var f,p;if(2===arguments.length?(o=t,t=l):null==t?t=l:d(t,"options"),h(o,"callback"),u(t.signal,"options.signal"),o=c(o),w(e)||E(e))return B(e,t,o);if(!I(e))throw new s("stream",["ReadableStream","WritableStream","Stream"],e);let b=null!==(f=t.readable)&&void 0!==f?f:v(e),x=null!==(p=t.writable)&&void 0!==p?p:S(e),C=e._writableState,M=e._readableState,N=()=>{e.writable||W()},L=T(e)&&v(e)===b&&S(e)===x,U=A(e,!1),W=()=>{U=!0,e.destroyed&&(L=!1),(!L||e.readable&&!b)&&(!b||D)&&o.call(e)},D=_(e,!1),F=()=>{D=!0,e.destroyed&&(L=!1),(!L||e.writable&&!x)&&(!x||U)&&o.call(e)},q=t=>{o.call(e,t)},H=m(e),V=()=>{H=!0;let t=k(e)||O(e);return t&&"boolean"!=typeof t?o.call(e,t):b&&!D&&v(e,!0)&&!_(e,!1)?o.call(e,new a):!x||U||A(e,!1)?void o.call(e):o.call(e,new a)},z=()=>{H=!0;let t=k(e)||O(e);if(t&&"boolean"!=typeof t)return o.call(e,t);o.call(e)},G=()=>{e.req.on("finish",W)};R(e)?(e.on("complete",W),L||e.on("abort",V),e.req?G():e.on("request",G)):x&&!C&&(e.on("end",N),e.on("close",N)),!L&&"boolean"==typeof e.aborted&&e.on("aborted",V),e.on("end",F),e.on("finish",W),!1!==t.error&&e.on("error",q),e.on("close",V),H?i.nextTick(V):null!=C&&C.errorEmitted||null!=M&&M.errorEmitted?L||i.nextTick(z):(!b&&(!L||y(e))&&(U||!1===j(e))||!x&&(!L||j(e))&&(D||!1===y(e))||M&&e.req&&e.aborted)&&i.nextTick(z);let K=()=>{o=P,e.removeListener("aborted",V),e.removeListener("complete",W),e.removeListener("abort",V),e.removeListener("request",G),e.req&&e.req.removeListener("finish",W),e.removeListener("end",N),e.removeListener("close",N),e.removeListener("finish",W),e.removeListener("end",F),e.removeListener("error",q),e.removeListener("close",V)};if(t.signal&&!H){let s=()=>{let r=o;K(),r.call(e,new n(void 0,{cause:t.signal.reason}))};if(t.signal.aborted)i.nextTick(s);else{let i=(r=r||Yt().addAbortListener)(t.signal,s),n=o;o=c((function(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];i[g](),n.apply(e,r)}))}}return K}function B(e,t,o){let s=!1,a=P;if(t.signal)if(a=()=>{s=!0,o.call(e,new n(void 0,{cause:t.signal.reason}))},t.signal.aborted)i.nextTick(a);else{let i=(r=r||Yt().addAbortListener)(t.signal,a),n=o;o=c((function(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];i[g](),n.apply(e,r)}))}let l=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];s||i.nextTick((()=>o.apply(e,r)))};return b(e[C].promise,l,l),P}t.exports=M,t.exports.finished=function(e,t){var r;let i=!1;return null===t&&(t=l),null!==(r=t)&&void 0!==r&&r.cleanup&&(f(t.cleanup,"cleanup"),i=t.cleanup),new p(((r,n)=>{let o=M(e,t,(e=>{i&&o(),e?n(e):r()}))}))}})),er=S(((e,t)=>{x(),yt(),nt();var r=Xt(),{aggregateTwoErrors:i,codes:{ERR_MULTIPLE_CALLBACK:n},AbortError:o}=Ot(),{Symbol:s}=wt(),{kIsDestroyed:a,isDestroyed:l,isFinished:c,isServerRequest:u}=$t(),h=s("kDestroy"),d=s("kConstruct");function f(e,t,r){e&&(e.stack,t&&!t.errored&&(t.errored=e),r&&!r.errored&&(r.errored=e))}function p(e,t,i){let n=!1;function o(t){if(n)return;n=!0;let o=e._readableState,s=e._writableState;f(t,s,o),s&&(s.closed=!0),o&&(o.closed=!0),"function"==typeof i&&i(t),t?r.nextTick(b,e,t):r.nextTick(g,e)}try{e._destroy(t||null,o)}catch(s){o(s)}}function b(e,t){m(e,t),g(e)}function g(e){let t=e._readableState,r=e._writableState;r&&(r.closeEmitted=!0),t&&(t.closeEmitted=!0),(null!=r&&r.emitClose||null!=t&&t.emitClose)&&e.emit("close")}function m(e,t){let r=e._readableState,i=e._writableState;null!=i&&i.errorEmitted||null!=r&&r.errorEmitted||(i&&(i.errorEmitted=!0),r&&(r.errorEmitted=!0),e.emit("error",t))}function y(e,t,i){let n=e._readableState,o=e._writableState;if(null!=o&&o.destroyed||null!=n&&n.destroyed)return this;null!=n&&n.autoDestroy||null!=o&&o.autoDestroy?e.destroy(t):t&&(t.stack,o&&!o.errored&&(o.errored=t),n&&!n.errored&&(n.errored=t),i?r.nextTick(m,e,t):m(e,t))}function v(e){let t=!1;function i(i){if(t)return void y(e,null!==i&&void 0!==i?i:new n);t=!0;let o=e._readableState,s=e._writableState,a=s||o;o&&(o.constructed=!0),s&&(s.constructed=!0),a.destroyed?e.emit(h,i):i?y(e,i,!0):r.nextTick(w,e)}try{e._construct((e=>{r.nextTick(i,e)}))}catch(o){r.nextTick(i,o)}}function w(e){e.emit(d)}function _(e){return(null===e||void 0===e?void 0:e.setHeader)&&"function"==typeof e.abort}function O(e){e.emit("close")}function j(e,t){e.emit("error",t),r.nextTick(O,e)}t.exports={construct:function(e,t){if("function"!=typeof e._construct)return;let i=e._readableState,n=e._writableState;i&&(i.constructed=!1),n&&(n.constructed=!1),e.once(d,t),!(e.listenerCount(d)>1)&&r.nextTick(v,e)},destroyer:function(e,t){!e||l(e)||(!t&&!c(e)&&(t=new o),u(e)?(e.socket=null,e.destroy(t)):_(e)?e.abort():_(e.req)?e.req.abort():"function"==typeof e.destroy?e.destroy(t):"function"==typeof e.close?e.close():t?r.nextTick(j,e,t):r.nextTick(O,e),e.destroyed||(e[a]=!0))},destroy:function(e,t){let r=this._readableState,n=this._writableState,o=n||r;return null!=n&&n.destroyed||null!=r&&r.destroyed?("function"==typeof t&&t(),this):(f(e,n,r),n&&(n.destroyed=!0),r&&(r.destroyed=!0),o.constructed?p(this,e,t):this.once(h,(function(r){p(this,i(r,e),t)})),this)},undestroy:function(){let e=this._readableState,t=this._writableState;e&&(e.constructed=!0,e.closed=!1,e.closeEmitted=!1,e.destroyed=!1,e.errored=null,e.errorEmitted=!1,e.reading=!1,e.ended=!1===e.readable,e.endEmitted=!1===e.readable),t&&(t.constructed=!0,t.destroyed=!1,t.closed=!1,t.closeEmitted=!1,t.errored=null,t.errorEmitted=!1,t.finalCalled=!1,t.prefinished=!1,t.ended=!1===t.writable,t.ending=!1===t.writable,t.finished=!1===t.writable)},errorOrDestroy:y}})),tr=S(((e,t)=>{x(),yt(),nt();var{ArrayIsArray:r,ObjectSetPrototypeOf:i}=wt(),{EventEmitter:n}=(Qt(),A(Ft));function o(e){n.call(this,e)}function s(e,t,i){if("function"==typeof e.prependListener)return e.prependListener(t,i);e._events&&e._events[t]?r(e._events[t])?e._events[t].unshift(i):e._events[t]=[i,e._events[t]]:e.on(t,i)}i(o.prototype,n.prototype),i(o,n),o.prototype.pipe=function(e,t){let r=this;function i(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function o(){r.readable&&r.resume&&r.resume()}r.on("data",i),e.on("drain",o),!e._isStdio&&(!t||!1!==t.end)&&(r.on("end",l),r.on("close",c));let a=!1;function l(){a||(a=!0,e.end())}function c(){a||(a=!0,"function"==typeof e.destroy&&e.destroy())}function u(e){h(),0===n.listenerCount(this,"error")&&this.emit("error",e)}function h(){r.removeListener("data",i),e.removeListener("drain",o),r.removeListener("end",l),r.removeListener("close",c),r.removeListener("error",u),e.removeListener("error",u),r.removeListener("end",h),r.removeListener("close",h),e.removeListener("close",h)}return s(r,"error",u),s(e,"error",u),r.on("end",h),r.on("close",h),e.on("close",h),e.emit("pipe",r),e},t.exports={Stream:o,prependListener:s}})),rr=S(((e,t)=>{x(),yt(),nt();var r,{SymbolDispose:i}=wt(),{AbortError:n,codes:o}=Ot(),{isNodeStream:s,isWebStream:a,kControllerErrorFunction:l}=$t(),c=Zt(),{ERR_INVALID_ARG_TYPE:u}=o;t.exports.addAbortSignal=function(e,r){if(((e,t)=>{if("object"!=typeof e||!("aborted"in e))throw new u(t,"AbortSignal",e)})(e,"signal"),!s(r)&&!a(r))throw new u("stream",["ReadableStream","WritableStream","Stream"],r);return t.exports.addAbortSignalNoValidate(e,r)},t.exports.addAbortSignalNoValidate=function(e,t){if("object"!=typeof e||!("aborted"in e))return t;let o=s(t)?()=>{t.destroy(new n(void 0,{cause:e.reason}))}:()=>{t[l](new n(void 0,{cause:e.reason}))};if(e.aborted)o();else{let n=(r=r||Yt().addAbortListener)(e,o);c(t,n[i])}return t}})),ir=S(((e,t)=>{x(),yt(),nt();var{StringPrototypeSlice:r,SymbolIterator:i,TypedArrayPrototypeSet:n,Uint8Array:o}=wt(),{Buffer:s}=(mt(),A(ot)),{inspect:a}=Yt();t.exports=class{constructor(){this.head=null,this.tail=null,this.length=0}push(e){let t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}unshift(e){let t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}shift(){if(0===this.length)return;let e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}clear(){this.head=this.tail=null,this.length=0}join(e){if(0===this.length)return"";let t=this.head,r=""+t.data;for(;null!==(t=t.next);)r+=e+t.data;return r}concat(e){if(0===this.length)return s.alloc(0);let t=s.allocUnsafe(e>>>0),r=this.head,i=0;for(;r;)n(t,r.data,i),i+=r.data.length,r=r.next;return t}consume(e,t){let r=this.head.data;if(e<r.length){let t=r.slice(0,e);return this.head.data=r.slice(e),t}return e===r.length?this.shift():t?this._getString(e):this._getBuffer(e)}first(){return this.head.data}*[i](){for(let e=this.head;e;e=e.next)yield e.data}_getString(e){let t="",i=this.head,n=0;do{let o=i.data;if(!(e>o.length)){e===o.length?(t+=o,++n,i.next?this.head=i.next:this.head=this.tail=null):(t+=r(o,0,e),this.head=i,i.data=r(o,e));break}t+=o,e-=o.length,++n}while(null!==(i=i.next));return this.length-=n,t}_getBuffer(e){let t=s.allocUnsafe(e),r=e,i=this.head,a=0;do{let s=i.data;if(!(e>s.length)){e===s.length?(n(t,s,r-e),++a,i.next?this.head=i.next:this.head=this.tail=null):(n(t,new o(s.buffer,s.byteOffset,e),r-e),this.head=i,i.data=s.slice(e));break}n(t,s,r-e),e-=s.length,++a}while(null!==(i=i.next));return this.length-=a,t}[Symbol.for("nodejs.util.inspect.custom")](e,t){return a(this,Object(f.a)(Object(f.a)({},t),{},{depth:0,customInspect:!1}))}}})),nr=S(((e,t)=>{x(),yt(),nt();var{MathFloor:r,NumberIsInteger:i}=wt(),{validateInteger:n}=Jt(),{ERR_INVALID_ARG_VALUE:o}=Ot().codes,s=16384,a=16;function l(e){return e?a:s}t.exports={getHighWaterMark:function(e,t,n,s){let a=function(e,t,r){return null!=e.highWaterMark?e.highWaterMark:t?e[r]:null}(t,s,n);if(null!=a){if(!i(a)||a<0){let e=s?"options.".concat(n):"options.highWaterMark";throw new o(e,a)}return r(a)}return l(e.objectMode)},getDefaultHighWaterMark:l,setDefaultHighWaterMark:function(e,t){n(t,"value",0),e?a=t:s=t}}})),or=S(((e,t)=>{x(),yt(),nt();var r=(mt(),A(ot)),i=r.Buffer;function n(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:(n(r,e),e.Buffer=o),o.prototype=Object.create(i.prototype),n(i,o),o.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}})),sr=S((e=>{x(),yt(),nt();var t=or().Buffer,r=t.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function i(e){var i;switch(this.encoding=function(e){var i=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof i&&(t.isEncoding===r||!r(e)))throw new Error("Unknown encoding: "+e);return i||e}(e),this.encoding){case"utf16le":this.text=s,this.end=a,i=4;break;case"utf8":this.fillLast=o,i=4;break;case"base64":this.text=l,this.end=c,i=3;break;default:return this.write=u,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=t.allocUnsafe(i)}function n(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function o(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!==(192&t[0]))return e.lastNeed=0,"\ufffd";if(e.lastNeed>1&&t.length>1){if(128!==(192&t[1]))return e.lastNeed=1,"\ufffd";if(e.lastNeed>2&&t.length>2&&128!==(192&t[2]))return e.lastNeed=2,"\ufffd"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function s(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function a(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function l(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function c(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function u(e){return e.toString(this.encoding)}function h(e){return e&&e.length?this.write(e):""}e.StringDecoder=i,i.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},i.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"\ufffd":t},i.prototype.text=function(e,t){var r=function(e,t,r){var i=t.length-1;if(i<r)return 0;var o=n(t[i]);return o>=0?(o>0&&(e.lastNeed=o-1),o):--i<r||-2===o?0:(o=n(t[i]),o>=0?(o>0&&(e.lastNeed=o-2),o):--i<r||-2===o?0:(o=n(t[i]),o>=0?(o>0&&(2===o?o=0:e.lastNeed=o-3),o):0))}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var i=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,i),e.toString("utf8",t,i)},i.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}})),ar=S(((e,t)=>{x(),yt(),nt();var r=Xt(),{PromisePrototypeThen:i,SymbolAsyncIterator:n,SymbolIterator:o}=wt(),{Buffer:s}=(mt(),A(ot)),{ERR_INVALID_ARG_TYPE:a,ERR_STREAM_NULL_VALUES:l}=Ot().codes;t.exports=function(e,t,c){let u,h;if("string"==typeof t||t instanceof s)return new e(Object(f.a)(Object(f.a)({objectMode:!0},c),{},{read(){this.push(t),this.push(null)}}));if(t&&t[n])h=!0,u=t[n]();else{if(!t||!t[o])throw new a("iterable",["Iterable"],t);h=!1,u=t[o]()}let d=new e(Object(f.a)({objectMode:!0,highWaterMark:1},c)),p=!1;return d._read=function(){p||(p=!0,async function(){for(;;){try{let{value:e,done:t}=h?await u.next():u.next();if(t)d.push(null);else{let t=e&&"function"==typeof e.then?await e:e;if(null===t)throw p=!1,new l;if(d.push(t))continue;p=!1}}catch(e){d.destroy(e)}break}}())},d._destroy=function(e,t){i(async function(e){let t=null!=e,r="function"==typeof u.throw;if(t&&r){let{value:t,done:r}=await u.throw(e);if(await t,r)return}if("function"==typeof u.return){let{value:e}=await u.return();await e}}(e),(()=>r.nextTick(t,e)),(i=>r.nextTick(t,i||e)))},d}})),lr=S(((e,t)=>{x(),yt(),nt();var r=Xt(),{ArrayPrototypeIndexOf:i,NumberIsInteger:n,NumberIsNaN:o,NumberParseInt:s,ObjectDefineProperties:a,ObjectKeys:l,ObjectSetPrototypeOf:c,Promise:u,SafeSet:h,SymbolAsyncDispose:d,SymbolAsyncIterator:p,Symbol:b}=wt();t.exports=Q,Q.ReadableState=K;var{EventEmitter:y}=(Qt(),A(Ft)),{Stream:v,prependListener:w}=tr(),{Buffer:_}=(mt(),A(ot)),{addAbortSignal:O}=rr(),j=Zt(),S=Yt().debuglog("stream",(e=>{S=e})),E=ir(),k=er(),{getHighWaterMark:I,getDefaultHighWaterMark:T}=nr(),{aggregateTwoErrors:C,codes:{ERR_INVALID_ARG_TYPE:R,ERR_METHOD_NOT_IMPLEMENTED:P,ERR_OUT_OF_RANGE:M,ERR_STREAM_PUSH_AFTER_EOF:B,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:N},AbortError:L}=Ot(),{validateObject:U}=Jt(),W=b("kPaused"),{StringDecoder:D}=sr(),F=ar();c(Q.prototype,v.prototype),c(Q,v);var q=()=>{},{errorOrDestroy:H}=k,V=2048,z=4096;function G(e){return{enumerable:!1,get(){return 0!==(this.state&e)},set(t){t?this.state|=e:this.state&=~e}}}function K(e,t,r){"boolean"!=typeof r&&(r=t instanceof hr()),this.state=6192,e&&e.objectMode&&(this.state|=1),r&&e&&e.readableObjectMode&&(this.state|=1),this.highWaterMark=e?I(this,e,"readableHighWaterMark",r):T(!1),this.buffer=new E,this.length=0,this.pipes=[],this.flowing=null,this[W]=null,e&&!1===e.emitClose&&(this.state&=~V),e&&!1===e.autoDestroy&&(this.state&=~z),this.errored=null,this.defaultEncoding=e&&e.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.decoder=null,this.encoding=null,e&&e.encoding&&(this.decoder=new D(e.encoding),this.encoding=e.encoding)}function Q(e){if(!(this instanceof Q))return new Q(e);let t=this instanceof hr();this._readableState=new K(e,this,t),e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.construct&&(this._construct=e.construct),e.signal&&!t&&O(e.signal,this)),v.call(this,e),k.construct(this,(()=>{this._readableState.needReadable&&te(this,this._readableState)}))}function Y(e,t,r,i){S("readableAddChunk",t);let n,o=e._readableState;if(0===(1&o.state)&&("string"==typeof t?(r=r||o.defaultEncoding,o.encoding!==r&&(i&&o.encoding?t=_.from(t,r).toString(o.encoding):(t=_.from(t,r),r=""))):t instanceof _?r="":v._isUint8Array(t)?(t=v._uint8ArrayToBuffer(t),r=""):null!=t&&(n=new R("chunk",["string","Buffer","Uint8Array"],t))),n)H(e,n);else if(null===t)o.state&=-9,function(e,t){if(S("onEofChunk"),!t.ended){if(t.decoder){let e=t.decoder.end();e&&e.length&&(t.buffer.push(e),t.length+=t.objectMode?1:e.length)}t.ended=!0,t.sync?Z(e):(t.needReadable=!1,t.emittedReadable=!0,ee(e))}}(e,o);else if(0!==(1&o.state)||t&&t.length>0)if(i)if(0!==(4&o.state))H(e,new N);else{if(o.destroyed||o.errored)return!1;J(e,o,t,!0)}else if(o.ended)H(e,new B);else{if(o.destroyed||o.errored)return!1;o.state&=-9,o.decoder&&!r?(t=o.decoder.write(t),o.objectMode||0!==t.length?J(e,o,t,!1):te(e,o)):J(e,o,t,!1)}else i||(o.state&=-9,te(e,o));return!o.ended&&(o.length<o.highWaterMark||0===o.length)}function J(e,t,r,i){t.flowing&&0===t.length&&!t.sync&&e.listenerCount("data")>0?(0!==(65536&t.state)?t.awaitDrainWriters.clear():t.awaitDrainWriters=null,t.dataEmitted=!0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),0!==(64&t.state)&&Z(e)),te(e,t)}a(K.prototype,{objectMode:G(1),ended:G(2),endEmitted:G(4),reading:G(8),constructed:G(16),sync:G(32),needReadable:G(64),emittedReadable:G(128),readableListening:G(256),resumeScheduled:G(512),errorEmitted:G(1024),emitClose:G(V),autoDestroy:G(z),destroyed:G(8192),closed:G(16384),closeEmitted:G(32768),multiAwaitDrain:G(65536),readingMore:G(1<<17),dataEmitted:G(1<<18)}),Q.prototype.destroy=k.destroy,Q.prototype._undestroy=k.undestroy,Q.prototype._destroy=function(e,t){t(e)},Q.prototype[y.captureRejectionSymbol]=function(e){this.destroy(e)},Q.prototype[d]=function(){let e;return this.destroyed||(e=this.readableEnded?null:new L,this.destroy(e)),new u(((t,r)=>j(this,(i=>i&&i!==e?r(i):t(null)))))},Q.prototype.push=function(e,t){return Y(this,e,t,!1)},Q.prototype.unshift=function(e,t){return Y(this,e,t,!0)},Q.prototype.isPaused=function(){let e=this._readableState;return!0===e[W]||!1===e.flowing},Q.prototype.setEncoding=function(e){let t=new D(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;let r=this._readableState.buffer,i="";for(let n of r)i+=t.write(n);return r.clear(),""!==i&&r.push(i),this._readableState.length=i.length,this};var X;function $(e,t){return e<=0||0===t.length&&t.ended?0:0!==(1&t.state)?1:o(e)?t.flowing&&t.length?t.buffer.first().length:t.length:e<=t.length?e:t.ended?t.length:0}function Z(e){let t=e._readableState;S("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(S("emitReadable",t.flowing),t.emittedReadable=!0,r.nextTick(ee,e))}function ee(e){let t=e._readableState;S("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&!t.errored&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,se(e)}function te(e,t){!t.readingMore&&t.constructed&&(t.readingMore=!0,r.nextTick(re,e,t))}function re(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){let r=t.length;if(S("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}function ie(e){let t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!1===t[W]?t.flowing=!0:e.listenerCount("data")>0?e.resume():t.readableListening||(t.flowing=null)}function ne(e){S("readable nexttick read 0"),e.read(0)}function oe(e,t){S("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),se(e),t.flowing&&!t.reading&&e.read(0)}function se(e){let t=e._readableState;for(S("flow",t.flowing);t.flowing&&null!==e.read(););}function ae(e,t){"function"!=typeof e.read&&(e=Q.wrap(e,{objectMode:!0}));let r=function(e,t){return le.apply(this,arguments)}(e,t);return r.stream=e,r}function le(){return(le=Object(m.a)((function*(e,t){let r=q;function i(t){this===e?(r(),r=q):r=t}e.on("readable",i);let n,o=j(e,{writable:!1},(e=>{n=e?C(n,e):null,r(),r=q}));try{for(;;){let t=e.destroyed?null:e.read();if(null!==t)yield t;else{if(n)throw n;if(null===n)return;yield Object(g.a)(new u(i))}}}catch(s){throw n=C(n,s),n}finally{!n&&!1===(null===t||void 0===t?void 0:t.destroyOnReturn)||void 0!==n&&!e._readableState.autoDestroy?(e.off("readable",i),o()):k.destroyer(e,null)}}))).apply(this,arguments)}function ce(e,t){if(0===t.length)return null;let r;return t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r}function ue(e){let t=e._readableState;S("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,r.nextTick(he,t,e))}function he(e,t){if(S("endReadableNT",e.endEmitted,e.length),!e.errored&&!e.closeEmitted&&!e.endEmitted&&0===e.length)if(e.endEmitted=!0,t.emit("end"),t.writable&&!1===t.allowHalfOpen)r.nextTick(de,t);else if(e.autoDestroy){let e=t._writableState;(!e||e.autoDestroy&&(e.finished||!1===e.writable))&&t.destroy()}}function de(e){e.writable&&!e.writableEnded&&!e.destroyed&&e.end()}function fe(){return void 0===X&&(X={}),X}Q.prototype.read=function(e){S("read",e),void 0===e?e=NaN:n(e)||(e=s(e,10));let t=this._readableState,r=e;if(e>t.highWaterMark&&(t.highWaterMark=function(e){if(e>1073741824)throw new M("size","<= 1GiB",e);return e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,++e}(e)),0!==e&&(t.state&=-129),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return S("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?ue(this):Z(this),null;if(0===(e=$(e,t))&&t.ended)return 0===t.length&&ue(this),null;let i,o=0!==(64&t.state);if(S("need readable",o),(0===t.length||t.length-e<t.highWaterMark)&&(o=!0,S("length less than watermark",o)),t.ended||t.reading||t.destroyed||t.errored||!t.constructed)o=!1,S("reading, ended or constructing",o);else if(o){S("do read"),t.state|=40,0===t.length&&(t.state|=64);try{this._read(t.highWaterMark)}catch(a){H(this,a)}t.state&=-33,t.reading||(e=$(r,t))}return i=e>0?ce(e,t):null,null===i?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.multiAwaitDrain?t.awaitDrainWriters.clear():t.awaitDrainWriters=null),0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&ue(this)),null!==i&&!t.errorEmitted&&!t.closeEmitted&&(t.dataEmitted=!0,this.emit("data",i)),i},Q.prototype._read=function(e){throw new P("_read()")},Q.prototype.pipe=function(e,t){let i=this,n=this._readableState;1===n.pipes.length&&(n.multiAwaitDrain||(n.multiAwaitDrain=!0,n.awaitDrainWriters=new h(n.awaitDrainWriters?[n.awaitDrainWriters]:[]))),n.pipes.push(e),S("pipe count=%d opts=%j",n.pipes.length,t);let o=t&&!1===t.end||e===r.stdout||e===r.stderr?g:a;function s(t,r){S("onunpipe"),t===i&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,S("cleanup"),e.removeListener("close",p),e.removeListener("finish",b),l&&e.removeListener("drain",l),e.removeListener("error",f),e.removeListener("unpipe",s),i.removeListener("end",a),i.removeListener("end",g),i.removeListener("data",d),c=!0,l&&n.awaitDrainWriters&&(!e._writableState||e._writableState.needDrain)&&l())}function a(){S("onend"),e.end()}n.endEmitted?r.nextTick(o):i.once("end",o),e.on("unpipe",s);let l,c=!1;function u(){c||(1===n.pipes.length&&n.pipes[0]===e?(S("false write response, pause",0),n.awaitDrainWriters=e,n.multiAwaitDrain=!1):n.pipes.length>1&&n.pipes.includes(e)&&(S("false write response, pause",n.awaitDrainWriters.size),n.awaitDrainWriters.add(e)),i.pause()),l||(l=function(e,t){return function(){let r=e._readableState;r.awaitDrainWriters===t?(S("pipeOnDrain",1),r.awaitDrainWriters=null):r.multiAwaitDrain&&(S("pipeOnDrain",r.awaitDrainWriters.size),r.awaitDrainWriters.delete(t)),(!r.awaitDrainWriters||0===r.awaitDrainWriters.size)&&e.listenerCount("data")&&e.resume()}}(i,e),e.on("drain",l))}function d(t){S("ondata");let r=e.write(t);S("dest.write",r),!1===r&&u()}function f(t){if(S("onerror",t),g(),e.removeListener("error",f),0===e.listenerCount("error")){let r=e._writableState||e._readableState;r&&!r.errorEmitted?H(e,t):e.emit("error",t)}}function p(){e.removeListener("finish",b),g()}function b(){S("onfinish"),e.removeListener("close",p),g()}function g(){S("unpipe"),i.unpipe(e)}return i.on("data",d),w(e,"error",f),e.once("close",p),e.once("finish",b),e.emit("pipe",i),!0===e.writableNeedDrain?u():n.flowing||(S("pipe resume"),i.resume()),e},Q.prototype.unpipe=function(e){let t=this._readableState;if(0===t.pipes.length)return this;if(!e){let e=t.pipes;t.pipes=[],this.pause();for(let t=0;t<e.length;t++)e[t].emit("unpipe",this,{hasUnpiped:!1});return this}let r=i(t.pipes,e);return-1===r||(t.pipes.splice(r,1),0===t.pipes.length&&this.pause(),e.emit("unpipe",this,{hasUnpiped:!1})),this},Q.prototype.on=function(e,t){let i=v.prototype.on.call(this,e,t),n=this._readableState;return"data"===e?(n.readableListening=this.listenerCount("readable")>0,!1!==n.flowing&&this.resume()):"readable"===e&&!n.endEmitted&&!n.readableListening&&(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,S("on readable",n.length,n.reading),n.length?Z(this):n.reading||r.nextTick(ne,this)),i},Q.prototype.addListener=Q.prototype.on,Q.prototype.removeListener=function(e,t){let i=v.prototype.removeListener.call(this,e,t);return"readable"===e&&r.nextTick(ie,this),i},Q.prototype.off=Q.prototype.removeListener,Q.prototype.removeAllListeners=function(e){let t=v.prototype.removeAllListeners.apply(this,arguments);return("readable"===e||void 0===e)&&r.nextTick(ie,this),t},Q.prototype.resume=function(){let e=this._readableState;return e.flowing||(S("resume"),e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,r.nextTick(oe,e,t))}(this,e)),e[W]=!1,this},Q.prototype.pause=function(){return S("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(S("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState[W]=!0,this},Q.prototype.wrap=function(e){let t=!1;e.on("data",(r=>{!this.push(r)&&e.pause&&(t=!0,e.pause())})),e.on("end",(()=>{this.push(null)})),e.on("error",(e=>{H(this,e)})),e.on("close",(()=>{this.destroy()})),e.on("destroy",(()=>{this.destroy()})),this._read=()=>{t&&e.resume&&(t=!1,e.resume())};let r=l(e);for(let i=1;i<r.length;i++){let t=r[i];void 0===this[t]&&"function"==typeof e[t]&&(this[t]=e[t].bind(e))}return this},Q.prototype[p]=function(){return ae(this)},Q.prototype.iterator=function(e){return void 0!==e&&U(e,"options"),ae(this,e)},a(Q.prototype,{readable:{__proto__:null,get(){let e=this._readableState;return!!e&&!1!==e.readable&&!e.destroyed&&!e.errorEmitted&&!e.endEmitted},set(e){this._readableState&&(this._readableState.readable=!!e)}},readableDidRead:{__proto__:null,enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{__proto__:null,enumerable:!1,get:function(){return!(!1===this._readableState.readable||!this._readableState.destroyed&&!this._readableState.errored||this._readableState.endEmitted)}},readableHighWaterMark:{__proto__:null,enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{__proto__:null,enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{__proto__:null,enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}},readableLength:{__proto__:null,enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{__proto__:null,enumerable:!1,get(){return!!this._readableState&&this._readableState.objectMode}},readableEncoding:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},errored:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.errored:null}},closed:{__proto__:null,get(){return!!this._readableState&&this._readableState.closed}},destroyed:{__proto__:null,enumerable:!1,get(){return!!this._readableState&&this._readableState.destroyed},set(e){this._readableState&&(this._readableState.destroyed=e)}},readableEnded:{__proto__:null,enumerable:!1,get(){return!!this._readableState&&this._readableState.endEmitted}}}),a(K.prototype,{pipesCount:{__proto__:null,get(){return this.pipes.length}},paused:{__proto__:null,get(){return!1!==this[W]},set(e){this[W]=!!e}}}),Q._fromList=ce,Q.from=function(e,t){return F(Q,e,t)},Q.fromWeb=function(e,t){return fe().newStreamReadableFromReadableStream(e,t)},Q.toWeb=function(e,t){return fe().newReadableStreamFromStreamReadable(e,t)},Q.wrap=function(e,t){var r,i;return new Q(Object(f.a)(Object(f.a)({objectMode:null===(r=null!==(i=e.readableObjectMode)&&void 0!==i?i:e.objectMode)||void 0===r||r},t),{},{destroy(t,r){k.destroyer(e,t),r(t)}})).wrap(e)}})),cr=S(((e,t)=>{x(),yt(),nt();var r=Xt(),{ArrayPrototypeSlice:i,Error:n,FunctionPrototypeSymbolHasInstance:o,ObjectDefineProperty:s,ObjectDefineProperties:a,ObjectSetPrototypeOf:l,StringPrototypeToLowerCase:c,Symbol:u,SymbolHasInstance:h}=wt();t.exports=B,B.WritableState=P;var{EventEmitter:d}=(Qt(),A(Ft)),f=tr().Stream,{Buffer:p}=(mt(),A(ot)),b=er(),{addAbortSignal:g}=rr(),{getHighWaterMark:m,getDefaultHighWaterMark:y}=nr(),{ERR_INVALID_ARG_TYPE:v,ERR_METHOD_NOT_IMPLEMENTED:w,ERR_MULTIPLE_CALLBACK:_,ERR_STREAM_CANNOT_PIPE:O,ERR_STREAM_DESTROYED:j,ERR_STREAM_ALREADY_FINISHED:S,ERR_STREAM_NULL_VALUES:E,ERR_STREAM_WRITE_AFTER_END:k,ERR_UNKNOWN_ENCODING:I}=Ot().codes,{errorOrDestroy:T}=b;function C(){}l(B.prototype,f.prototype),l(B,f);var R=u("kOnFinished");function P(e,t,r){"boolean"!=typeof r&&(r=t instanceof hr()),this.objectMode=!(!e||!e.objectMode),r&&(this.objectMode=this.objectMode||!(!e||!e.writableObjectMode)),this.highWaterMark=e?m(this,e,"writableHighWaterMark",r):y(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;let i=!(!e||!1!==e.decodeStrings);this.decodeStrings=!i,this.defaultEncoding=e&&e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=W.bind(void 0,t),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,M(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!e||!1!==e.emitClose,this.autoDestroy=!e||!1!==e.autoDestroy,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[R]=[]}function M(e){e.buffered=[],e.bufferedIndex=0,e.allBuffers=!0,e.allNoop=!0}function B(e){let t=this instanceof hr();if(!t&&!o(B,this))return new B(e);this._writableState=new P(e,this,t),e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final),"function"==typeof e.construct&&(this._construct=e.construct),e.signal&&g(e.signal,this)),f.call(this,e),b.construct(this,(()=>{let e=this._writableState;e.writing||H(this,e),G(this,e)}))}function N(e,t,i,n){let o,s=e._writableState;if("function"==typeof i)n=i,i=s.defaultEncoding;else{if(i){if("buffer"!==i&&!p.isEncoding(i))throw new I(i)}else i=s.defaultEncoding;"function"!=typeof n&&(n=C)}if(null===t)throw new E;if(!s.objectMode)if("string"==typeof t)!1!==s.decodeStrings&&(t=p.from(t,i),i="buffer");else if(t instanceof p)i="buffer";else{if(!f._isUint8Array(t))throw new v("chunk",["string","Buffer","Uint8Array"],t);t=f._uint8ArrayToBuffer(t),i="buffer"}return s.ending?o=new k:s.destroyed&&(o=new j("write")),o?(r.nextTick(n,o),T(e,o,!0),o):(s.pendingcb++,function(e,t,r,i,n){let o=t.objectMode?1:r.length;t.length+=o;let s=t.length<t.highWaterMark;return s||(t.needDrain=!0),t.writing||t.corked||t.errored||!t.constructed?(t.buffered.push({chunk:r,encoding:i,callback:n}),t.allBuffers&&"buffer"!==i&&(t.allBuffers=!1),t.allNoop&&n!==C&&(t.allNoop=!1)):(t.writelen=o,t.writecb=n,t.writing=!0,t.sync=!0,e._write(r,i,t.onwrite),t.sync=!1),s&&!t.errored&&!t.destroyed}(e,s,t,i,n))}function L(e,t,r,i,n,o,s){t.writelen=i,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new j("write")):r?e._writev(n,t.onwrite):e._write(n,o,t.onwrite),t.sync=!1}function U(e,t,r,i){--t.pendingcb,i(r),q(t),T(e,r)}function W(e,t){let i=e._writableState,n=i.sync,o=i.writecb;"function"==typeof o?(i.writing=!1,i.writecb=null,i.length-=i.writelen,i.writelen=0,t?(t.stack,i.errored||(i.errored=t),e._readableState&&!e._readableState.errored&&(e._readableState.errored=t),n?r.nextTick(U,e,i,t,o):U(e,i,t,o)):(i.buffered.length>i.bufferedIndex&&H(e,i),n?null!==i.afterWriteTickInfo&&i.afterWriteTickInfo.cb===o?i.afterWriteTickInfo.count++:(i.afterWriteTickInfo={count:1,cb:o,stream:e,state:i},r.nextTick(D,i.afterWriteTickInfo)):F(e,i,1,o))):T(e,new _)}function D(e){let{stream:t,state:r,count:i,cb:n}=e;return r.afterWriteTickInfo=null,F(t,r,i,n)}function F(e,t,r,i){for(!t.ending&&!e.destroyed&&0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"));r-- >0;)t.pendingcb--,i();t.destroyed&&q(t),G(e,t)}function q(e){if(e.writing)return;for(let n=e.bufferedIndex;n<e.buffered.length;++n){var t;let{chunk:r,callback:i}=e.buffered[n],o=e.objectMode?1:r.length;e.length-=o,i(null!==(t=e.errored)&&void 0!==t?t:new j("write"))}let r=e[R].splice(0);for(let n=0;n<r.length;n++){var i;r[n](null!==(i=e.errored)&&void 0!==i?i:new j("end"))}M(e)}function H(e,t){if(t.corked||t.bufferProcessing||t.destroyed||!t.constructed)return;let{buffered:r,bufferedIndex:n,objectMode:o}=t,s=r.length-n;if(!s)return;let a=n;if(t.bufferProcessing=!0,s>1&&e._writev){t.pendingcb-=s-1;let n=t.allNoop?C:e=>{for(let t=a;t<r.length;++t)r[t].callback(e)},o=t.allNoop&&0===a?r:i(r,a);o.allBuffers=t.allBuffers,L(e,t,!0,t.length,o,"",n),M(t)}else{do{let{chunk:i,encoding:n,callback:s}=r[a];r[a++]=null,L(e,t,!1,o?1:i.length,i,n,s)}while(a<r.length&&!t.writing);a===r.length?M(t):a>256?(r.splice(0,a),t.bufferedIndex=0):t.bufferedIndex=a}t.bufferProcessing=!1}function V(e){return e.ending&&!e.destroyed&&e.constructed&&0===e.length&&!e.errored&&0===e.buffered.length&&!e.finished&&!e.writing&&!e.errorEmitted&&!e.closeEmitted}function z(e,t){!t.prefinished&&!t.finalCalled&&("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.finalCalled=!0,function(e,t){let i=!1;function n(n){if(i)T(e,null!==n&&void 0!==n?n:_());else if(i=!0,t.pendingcb--,n){let r=t[R].splice(0);for(let e=0;e<r.length;e++)r[e](n);T(e,n,t.sync)}else V(t)&&(t.prefinished=!0,e.emit("prefinish"),t.pendingcb++,r.nextTick(K,e,t))}t.sync=!0,t.pendingcb++;try{e._final(n)}catch(o){n(o)}t.sync=!1}(e,t)))}function G(e,t,i){V(t)&&(z(e,t),0===t.pendingcb&&(i?(t.pendingcb++,r.nextTick(((e,t)=>{V(t)?K(e,t):t.pendingcb--}),e,t)):V(t)&&(t.pendingcb++,K(e,t))))}function K(e,t){t.pendingcb--,t.finished=!0;let r=t[R].splice(0);for(let i=0;i<r.length;i++)r[i]();if(e.emit("finish"),t.autoDestroy){let t=e._readableState;(!t||t.autoDestroy&&(t.endEmitted||!1===t.readable))&&e.destroy()}}P.prototype.getBuffer=function(){return i(this.buffered,this.bufferedIndex)},s(P.prototype,"bufferedRequestCount",{__proto__:null,get(){return this.buffered.length-this.bufferedIndex}}),s(B,h,{__proto__:null,value:function(e){return!!o(this,e)||this===B&&(e&&e._writableState instanceof P)}}),B.prototype.pipe=function(){T(this,new O)},B.prototype.write=function(e,t,r){return!0===N(this,e,t,r)},B.prototype.cork=function(){this._writableState.corked++},B.prototype.uncork=function(){let e=this._writableState;e.corked&&(e.corked--,e.writing||H(this,e))},B.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=c(e)),!p.isEncoding(e))throw new I(e);return this._writableState.defaultEncoding=e,this},B.prototype._write=function(e,t,r){if(!this._writev)throw new w("_write()");this._writev([{chunk:e,encoding:t}],r)},B.prototype._writev=null,B.prototype.end=function(e,t,i){let o,s=this._writableState;if("function"==typeof e?(i=e,e=null,t=null):"function"==typeof t&&(i=t,t=null),null!=e){let r=N(this,e,t);r instanceof n&&(o=r)}return s.corked&&(s.corked=1,this.uncork()),o||(s.errored||s.ending?s.finished?o=new S("end"):s.destroyed&&(o=new j("end")):(s.ending=!0,G(this,s,!0),s.ended=!0)),"function"==typeof i&&(o||s.finished?r.nextTick(i,o):s[R].push(i)),this},a(B.prototype,{closed:{__proto__:null,get(){return!!this._writableState&&this._writableState.closed}},destroyed:{__proto__:null,get(){return!!this._writableState&&this._writableState.destroyed},set(e){this._writableState&&(this._writableState.destroyed=e)}},writable:{__proto__:null,get(){let e=this._writableState;return!!e&&!1!==e.writable&&!e.destroyed&&!e.errored&&!e.ending&&!e.ended},set(e){this._writableState&&(this._writableState.writable=!!e)}},writableFinished:{__proto__:null,get(){return!!this._writableState&&this._writableState.finished}},writableObjectMode:{__proto__:null,get(){return!!this._writableState&&this._writableState.objectMode}},writableBuffer:{__proto__:null,get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{__proto__:null,get(){return!!this._writableState&&this._writableState.ending}},writableNeedDrain:{__proto__:null,get(){let e=this._writableState;return!!e&&(!e.destroyed&&!e.ending&&e.needDrain)}},writableHighWaterMark:{__proto__:null,get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{__proto__:null,get(){return this._writableState?this._writableState.corked:0}},writableLength:{__proto__:null,get(){return this._writableState&&this._writableState.length}},errored:{__proto__:null,enumerable:!1,get(){return this._writableState?this._writableState.errored:null}},writableAborted:{__proto__:null,enumerable:!1,get:function(){return!(!1===this._writableState.writable||!this._writableState.destroyed&&!this._writableState.errored||this._writableState.finished)}}});var Q,Y=b.destroy;function J(){return void 0===Q&&(Q={}),Q}B.prototype.destroy=function(e,t){let i=this._writableState;return!i.destroyed&&(i.bufferedIndex<i.buffered.length||i[R].length)&&r.nextTick(q,i),Y.call(this,e,t),this},B.prototype._undestroy=b.undestroy,B.prototype._destroy=function(e,t){t(e)},B.prototype[d.captureRejectionSymbol]=function(e){this.destroy(e)},B.fromWeb=function(e,t){return J().newStreamWritableFromWritableStream(e,t)},B.toWeb=function(e){return J().newWritableStreamFromStreamWritable(e)}})),ur=S(((e,t)=>{x(),yt(),nt();var r=Xt(),i=(mt(),A(ot)),{isReadable:n,isWritable:o,isIterable:s,isNodeStream:a,isReadableNodeStream:l,isWritableNodeStream:c,isDuplexNodeStream:u,isReadableStream:h,isWritableStream:d}=$t(),f=Zt(),{AbortError:p,codes:{ERR_INVALID_ARG_TYPE:b,ERR_INVALID_RETURN_VALUE:y}}=Ot(),{destroyer:v}=er(),w=hr(),_=lr(),O=cr(),{createDeferredPromise:j}=Yt(),S=ar(),E=globalThis.Blob||i.Blob,k=typeof E<"u"?function(e){return e instanceof E}:function(e){return!1},I=globalThis.AbortController||jt().AbortController,{FunctionPrototypeCall:T}=wt(),C=class extends w{constructor(e){super(e),!1===(null===e||void 0===e?void 0:e.readable)&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),!1===(null===e||void 0===e?void 0:e.writable)&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}};function R(e){let t,r,i,s,a,l=e.readable&&"function"!=typeof e.readable.read?_.wrap(e.readable):e.readable,c=e.writable,u=!!n(l),h=!!o(c);function d(e){let t=s;s=null,t?t(e):e&&a.destroy(e)}return a=new C({readableObjectMode:!(null==l||!l.readableObjectMode),writableObjectMode:!(null==c||!c.writableObjectMode),readable:u,writable:h}),h&&(f(c,(e=>{h=!1,e&&v(l,e),d(e)})),a._write=function(e,r,i){c.write(e,r)?i():t=i},a._final=function(e){c.end(),r=e},c.on("drain",(function(){if(t){let e=t;t=null,e()}})),c.on("finish",(function(){if(r){let e=r;r=null,e()}}))),u&&(f(l,(e=>{u=!1,e&&v(l,e),d(e)})),l.on("readable",(function(){if(i){let e=i;i=null,e()}})),l.on("end",(function(){a.push(null)})),a._read=function(){for(;;){let e=l.read();if(null===e)return void(i=a._read);if(!a.push(e))return}}),a._destroy=function(e,n){!e&&null!==s&&(e=new p),i=null,t=null,r=null,null===s?n(e):(s=n,v(c,e),v(l,e))},a}t.exports=function e(t,i){if(u(t))return t;if(l(t))return R({readable:t});if(c(t))return R({writable:t});if(a(t))return R({writable:!1,readable:!1});if(h(t))return R({readable:_.fromWeb(t)});if(d(t))return R({writable:O.fromWeb(t)});if("function"==typeof t){let{value:e,write:n,final:o,destroy:a}=function(e){let{promise:t,resolve:i}=j(),n=new I,o=n.signal;return{value:e(Object(m.a)((function*(){for(;;){let e=t;t=null;let{chunk:n,done:s,cb:a}=yield Object(g.a)(e);if(r.nextTick(a),s)return;if(o.aborted)throw new p(void 0,{cause:o.reason});({promise:t,resolve:i}=j()),yield n}}))(),{signal:o}),write(e,t,r){let n=i;i=null,n({chunk:e,done:!1,cb:r})},final(e){let t=i;i=null,t({done:!0,cb:e})},destroy(e,t){n.abort(),t(e)}}}(t);if(s(e))return S(C,e,{objectMode:!0,write:n,final:o,destroy:a});let l=null===e||void 0===e?void 0:e.then;if("function"==typeof l){let t,i=T(l,e,(e=>{if(null!=e)throw new y("nully","body",e)}),(e=>{v(t,e)}));return t=new C({objectMode:!0,readable:!1,write:n,final(e){o((async()=>{try{await i,r.nextTick(e,null)}catch(t){r.nextTick(e,t)}}))},destroy:a})}throw new y("Iterable, AsyncIterable or AsyncFunction",i,e)}if(k(t))return e(t.arrayBuffer());if(s(t))return S(C,t,{objectMode:!0,writable:!1});if(h(null===t||void 0===t?void 0:t.readable)&&d(null===t||void 0===t?void 0:t.writable))return C.fromWeb(t);if("object"==typeof(null===t||void 0===t?void 0:t.writable)||"object"==typeof(null===t||void 0===t?void 0:t.readable)){return R({readable:null!=t&&t.readable?l(null===t||void 0===t?void 0:t.readable)?null===t||void 0===t?void 0:t.readable:e(t.readable):void 0,writable:null!=t&&t.writable?c(null===t||void 0===t?void 0:t.writable)?null===t||void 0===t?void 0:t.writable:e(t.writable):void 0})}let n=null===t||void 0===t?void 0:t.then;if("function"==typeof n){let e;return T(n,t,(t=>{null!=t&&e.push(t),e.push(null)}),(t=>{v(e,t)})),e=new C({objectMode:!0,writable:!1,read(){}})}throw new b(i,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],t)}})),hr=S(((e,t)=>{x(),yt(),nt();var{ObjectDefineProperties:r,ObjectGetOwnPropertyDescriptor:i,ObjectKeys:n,ObjectSetPrototypeOf:o}=wt();t.exports=u;var s,a,l=lr(),c=cr();o(u.prototype,l.prototype),o(u,l);{let e=n(c.prototype);for(let t=0;t<e.length;t++){let r=e[t];u.prototype[r]||(u.prototype[r]=c.prototype[r])}}function u(e){if(!(this instanceof u))return new u(e);l.call(this,e),c.call(this,e),e?(this.allowHalfOpen=!1!==e.allowHalfOpen,!1===e.readable&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),!1===e.writable&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}function h(){return void 0===s&&(s={}),s}r(u.prototype,{writable:Object(f.a)({__proto__:null},i(c.prototype,"writable")),writableHighWaterMark:Object(f.a)({__proto__:null},i(c.prototype,"writableHighWaterMark")),writableObjectMode:Object(f.a)({__proto__:null},i(c.prototype,"writableObjectMode")),writableBuffer:Object(f.a)({__proto__:null},i(c.prototype,"writableBuffer")),writableLength:Object(f.a)({__proto__:null},i(c.prototype,"writableLength")),writableFinished:Object(f.a)({__proto__:null},i(c.prototype,"writableFinished")),writableCorked:Object(f.a)({__proto__:null},i(c.prototype,"writableCorked")),writableEnded:Object(f.a)({__proto__:null},i(c.prototype,"writableEnded")),writableNeedDrain:Object(f.a)({__proto__:null},i(c.prototype,"writableNeedDrain")),destroyed:{__proto__:null,get(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set(e){this._readableState&&this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}}),u.fromWeb=function(e,t){return h().newStreamDuplexFromReadableWritablePair(e,t)},u.toWeb=function(e){return h().newReadableWritablePairFromDuplex(e)},u.from=function(e){return a||(a=ur()),a(e,"body")}})),dr=S(((e,t)=>{x(),yt(),nt();var{ObjectSetPrototypeOf:r,Symbol:i}=wt();t.exports=l;var{ERR_METHOD_NOT_IMPLEMENTED:n}=Ot().codes,o=hr(),{getHighWaterMark:s}=nr();r(l.prototype,o.prototype),r(l,o);var a=i("kCallback");function l(e){if(!(this instanceof l))return new l(e);let t=e?s(this,e,"readableHighWaterMark",!0):null;0===t&&(e=Object(f.a)(Object(f.a)({},e),{},{highWaterMark:null,readableHighWaterMark:t,writableHighWaterMark:e.writableHighWaterMark||0})),o.call(this,e),this._readableState.sync=!1,this[a]=null,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",u)}function c(e){"function"!=typeof this._flush||this.destroyed?(this.push(null),e&&e()):this._flush(((t,r)=>{t?e?e(t):this.destroy(t):(null!=r&&this.push(r),this.push(null),e&&e())}))}function u(){this._final!==c&&c.call(this)}l.prototype._final=c,l.prototype._transform=function(e,t,r){throw new n("_transform()")},l.prototype._write=function(e,t,r){let i=this._readableState,n=this._writableState,o=i.length;this._transform(e,t,((e,t)=>{e?r(e):(null!=t&&this.push(t),n.ended||o===i.length||i.length<i.highWaterMark?r():this[a]=r)}))},l.prototype._read=function(){if(this[a]){let e=this[a];this[a]=null,e()}}})),fr=S(((e,t)=>{x(),yt(),nt();var{ObjectSetPrototypeOf:r}=wt();t.exports=n;var i=dr();function n(e){if(!(this instanceof n))return new n(e);i.call(this,e)}r(n.prototype,i.prototype),r(n,i),n.prototype._transform=function(e,t,r){r(null,e)}})),pr=S(((e,t)=>{x(),yt(),nt();var r,i,n,o=Xt(),{ArrayIsArray:s,Promise:a,SymbolAsyncIterator:l,SymbolDispose:c}=wt(),u=Zt(),{once:h}=Yt(),d=er(),f=hr(),{aggregateTwoErrors:y,codes:{ERR_INVALID_ARG_TYPE:v,ERR_INVALID_RETURN_VALUE:w,ERR_MISSING_ARGS:_,ERR_STREAM_DESTROYED:O,ERR_STREAM_PREMATURE_CLOSE:j},AbortError:S}=Ot(),{validateFunction:E,validateAbortSignal:A}=Jt(),{isIterable:k,isReadable:I,isReadableNodeStream:T,isNodeStream:C,isTransformStream:R,isWebStream:P,isReadableStream:M,isReadableFinished:B}=$t(),N=globalThis.AbortController||jt().AbortController;function L(e,t,r){let i=!1;return e.on("close",(()=>{i=!0})),{destroy:t=>{i||(i=!0,d.destroyer(e,t||new O("pipe")))},cleanup:u(e,{readable:t,writable:r},(e=>{i=!e}))}}function U(e){return E(e[e.length-1],"streams[stream.length - 1]"),e.pop()}function W(e){if(k(e))return e;if(T(e))return function(e){return D.apply(this,arguments)}(e);throw new v("val",["Readable","Iterable","AsyncIterable"],e)}function D(){return(D=Object(m.a)((function*(e){i||(i=lr()),yield*Object(p.a)(Object(b.a)(i.prototype[l].call(e)),g.a)}))).apply(this,arguments)}async function F(e,t,r,i){let n,{end:o}=i,s=null,l=e=>{if(e&&(n=e),s){let e=s;s=null,e()}},c=()=>new a(((e,t)=>{n?t(n):s=()=>{n?t(n):e()}}));t.on("drain",l);let h=u(t,{readable:!1},l);try{t.writableNeedDrain&&await c();var d,f=!1,p=!1;try{for(var g,m=Object(b.a)(e);f=!(g=await m.next()).done;f=!1){let e=g.value;t.write(e)||await c()}}catch(v){p=!0,d=v}finally{try{f&&null!=m.return&&await m.return()}finally{if(p)throw d}}o&&(t.end(),await c()),r()}catch(w){r(n!==w?y(n,w):w)}finally{h(),t.off("drain",l)}}async function q(e,t,r,i){let{end:n}=i;R(t)&&(t=t.writable);let o=t.getWriter();try{var s,a=!1,l=!1;try{for(var c,u=Object(b.a)(e);a=!(c=await u.next()).done;a=!1){let e=c.value;await o.ready,o.write(e).catch((()=>{}))}}catch(h){l=!0,s=h}finally{try{a&&null!=u.return&&await u.return()}finally{if(l)throw s}}await o.ready,n&&await o.close(),r()}catch(d){try{await o.abort(d),r(d)}catch(f){r(f)}}}function H(e,t,i){if(1===e.length&&s(e[0])&&(e=e[0]),e.length<2)throw new _("streams");let a,l=new N,u=l.signal,h=null===i||void 0===i?void 0:i.signal,d=[];function p(){E(new S)}A(h,"options.signal"),n=n||Yt().addAbortListener,h&&(a=n(h,p));let b,g,m,y=[],O=0;function j(e){E(e,0===--O)}function E(e,r){var i;if(e&&(!b||"ERR_STREAM_PREMATURE_CLOSE"===b.code)&&(b=e),b||r){for(;y.length;)y.shift()(b);null===(i=a)||void 0===i||i[c](),l.abort(),r&&(b||d.forEach((e=>e())),o.nextTick(t,b,g))}}for(let n=0;n<e.length;n++){let t=e[n],s=n<e.length-1,a=n>0,l=s||!1!==(null===i||void 0===i?void 0:i.end),c=n===e.length-1;if(C(t)){let e=function(e){e&&"AbortError"!==e.name&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code&&j(e)};if(l){let{destroy:e,cleanup:r}=L(t,s,a);y.push(e),I(t)&&c&&d.push(r)}t.on("error",e),I(t)&&c&&d.push((()=>{t.removeListener("error",e)}))}if(0===n)if("function"==typeof t){if(m=t({signal:u}),!k(m))throw new w("Iterable, AsyncIterable or Stream","source",m)}else m=k(t)||T(t)||R(t)?t:f.from(t);else if("function"==typeof t){var x;if(R(m))m=W(null===(x=m)||void 0===x?void 0:x.readable);else m=W(m);if(m=t(m,{signal:u}),s){if(!k(m,!0))throw new w("AsyncIterable","transform[".concat(n-1,"]"),m)}else{var B;r||(r=fr());let e=new r({objectMode:!0}),t=null===(B=m)||void 0===B?void 0:B.then;if("function"==typeof t)O++,t.call(m,(t=>{g=t,null!=t&&e.write(t),l&&e.end(),o.nextTick(j)}),(t=>{e.destroy(t),o.nextTick(j,t)}));else if(k(m,!0))O++,F(m,e,j,{end:l});else{if(!M(m)&&!R(m))throw new w("AsyncIterable or Promise","destination",m);{let t=m.readable||m;O++,F(t,e,j,{end:l})}}m=e;let{destroy:i,cleanup:n}=L(m,!1,!0);y.push(i),c&&d.push(n)}}else if(C(t)){if(T(m)){O+=2;let e=V(m,t,j,{end:l});I(t)&&c&&d.push(e)}else if(R(m)||M(m)){let e=m.readable||m;O++,F(e,t,j,{end:l})}else{if(!k(m))throw new v("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],m);O++,F(m,t,j,{end:l})}m=t}else if(P(t)){if(T(m))O++,q(W(m),t,j,{end:l});else if(M(m)||k(m))O++,q(m,t,j,{end:l});else{if(!R(m))throw new v("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],m);O++,q(m.readable,t,j,{end:l})}m=t}else m=f.from(t)}return(null!=u&&u.aborted||null!=h&&h.aborted)&&o.nextTick(p),m}function V(e,t,r,i){let{end:n}=i,s=!1;if(t.on("close",(()=>{s||r(new j)})),e.pipe(t,{end:!1}),n){let r=function(){s=!0,t.end()};B(e)?o.nextTick(r):e.once("end",r)}else r();return u(e,{readable:!0,writable:!1},(t=>{let i=e._readableState;t&&"ERR_STREAM_PREMATURE_CLOSE"===t.code&&i&&i.ended&&!i.errored&&!i.errorEmitted?e.once("end",r).once("error",r):r(t)})),u(t,{readable:!1,writable:!0},r)}t.exports={pipelineImpl:H,pipeline:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return H(t,h(U(t)))}}})),br=S(((e,t)=>{x(),yt(),nt();var{pipeline:r}=pr(),i=hr(),{destroyer:n}=er(),{isNodeStream:o,isReadable:s,isWritable:a,isWebStream:l,isTransformStream:c,isWritableStream:u,isReadableStream:h}=$t(),{AbortError:d,codes:{ERR_INVALID_ARG_VALUE:f,ERR_MISSING_ARGS:p}}=Ot(),b=Zt();t.exports=function(){for(var e=arguments.length,t=new Array(e),g=0;g<e;g++)t[g]=arguments[g];if(0===t.length)throw new p("streams");if(1===t.length)return i.from(t[0]);let m,y,v,w,_,O=[...t];if("function"==typeof t[0]&&(t[0]=i.from(t[0])),"function"==typeof t[t.length-1]){let e=t.length-1;t[e]=i.from(t[e])}for(let r=0;r<t.length;++r)if(o(t[r])||l(t[r])){if(r<t.length-1&&!(s(t[r])||h(t[r])||c(t[r])))throw new f("streams[".concat(r,"]"),O[r],"must be readable");if(r>0&&!(a(t[r])||u(t[r])||c(t[r])))throw new f("streams[".concat(r,"]"),O[r],"must be writable")}function j(e){let t=w;w=null,t?t(e):e?_.destroy(e):!x&&!A&&_.destroy()}let S=t[0],E=r(t,j),A=!!(a(S)||u(S)||c(S)),x=!!(s(E)||h(E)||c(E));if(_=new i({writableObjectMode:!(null==S||!S.writableObjectMode),readableObjectMode:!(null==E||!E.readableObjectMode),writable:A,readable:x}),A){if(o(S))_._write=function(e,t,r){S.write(e,t)?r():m=r},_._final=function(e){S.end(),y=e},S.on("drain",(function(){if(m){let e=m;m=null,e()}}));else if(l(S)){let e=(c(S)?S.writable:S).getWriter();_._write=async function(t,r,i){try{await e.ready,e.write(t).catch((()=>{})),i()}catch(n){i(n)}},_._final=async function(t){try{await e.ready,e.close().catch((()=>{})),y=t}catch(r){t(r)}}}let e=c(E)?E.readable:E;b(e,(()=>{if(y){let e=y;y=null,e()}}))}if(x)if(o(E))E.on("readable",(function(){if(v){let e=v;v=null,e()}})),E.on("end",(function(){_.push(null)})),_._read=function(){for(;;){let e=E.read();if(null===e)return void(v=_._read);if(!_.push(e))return}};else if(l(E)){let e=(c(E)?E.readable:E).getReader();_._read=async function(){for(;;)try{let{value:t,done:r}=await e.read();if(!_.push(t))return;if(r)return void _.push(null)}catch(t){return}}}return _._destroy=function(e,t){!e&&null!==w&&(e=new d),v=null,m=null,y=null,null===w?t(e):(w=t,o(E)&&n(E,e))},_}})),gr=S(((e,t)=>{x(),yt(),nt();var r=globalThis.AbortController||jt().AbortController,{codes:{ERR_INVALID_ARG_VALUE:i,ERR_INVALID_ARG_TYPE:n,ERR_MISSING_ARGS:o,ERR_OUT_OF_RANGE:s},AbortError:a}=Ot(),{validateAbortSignal:l,validateInteger:c,validateObject:u}=Jt(),h=wt().Symbol("kWeak"),d=wt().Symbol("kResistStopPropagation"),{finished:f}=Zt(),y=br(),{addAbortSignalNoValidate:v}=rr(),{isWritable:w,isNodeStream:_}=$t(),{deprecate:O}=Yt(),{ArrayPrototypePush:j,Boolean:S,MathFloor:E,Number:A,NumberIsNaN:k,Promise:I,PromiseReject:T,PromiseResolve:C,PromisePrototypeThen:R,Symbol:P}=wt(),M=P("kEmpty"),B=P("kEof");function N(e,t){if("function"!=typeof e)throw new n("fn",["Function","AsyncFunction"],e);null!=t&&u(t,"options"),null!=(null===t||void 0===t?void 0:t.signal)&&l(t.signal,"options.signal");let r=1;null!=(null===t||void 0===t?void 0:t.concurrency)&&(r=E(t.concurrency));let i=r-1;return null!=(null===t||void 0===t?void 0:t.highWaterMark)&&(i=E(t.highWaterMark)),c(r,"options.concurrency",1),c(i,"options.highWaterMark",0),i+=r,Object(m.a)((function*(){let n,o,s=Yt().AbortSignalAny([null===t||void 0===t?void 0:t.signal].filter(S)),l=this,c=[],u={signal:s},h=!1,d=0;function f(){h=!0,p()}function p(){d-=1,m()}function m(){o&&!h&&d<r&&c.length<i&&(o(),o=null)}!async function(){try{var t,g=!1,m=!1;try{for(var y,v=Object(b.a)(l);g=!(y=await v.next()).done;g=!1){let t=y.value;if(h)return;if(s.aborted)throw new a;try{if(t=e(t,u),t===M)continue;t=C(t)}catch(w){t=T(w)}d+=1,R(t,p,f),c.push(t),n&&(n(),n=null),!h&&(c.length>=i||d>=r)&&await new I((e=>{o=e}))}}catch(_){m=!0,t=_}finally{try{g&&null!=v.return&&await v.return()}finally{if(m)throw t}}c.push(B)}catch(O){let e=T(O);R(e,p,f),c.push(e)}finally{h=!0,n&&(n(),n=null)}}();try{for(;;){for(;c.length>0;){let e=yield Object(g.a)(c[0]);if(e===B)return;if(s.aborted)throw new a;e!==M&&(yield e),c.shift(),m()}yield Object(g.a)(new I((e=>{n=e})))}}finally{h=!0,o&&(o(),o=null)}})).call(this)}async function L(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;var r,i=!1,n=!1;try{for(var o,s=Object(b.a)(U.call(this,e,t));i=!(o=await s.next()).done;i=!1){o.value;return!0}}catch(a){n=!0,r=a}finally{try{i&&null!=s.return&&await s.return()}finally{if(n)throw r}}return!1}function U(e,t){if("function"!=typeof e)throw new n("fn",["Function","AsyncFunction"],e);return N.call(this,(async function(t,r){return await e(t,r)?t:M}),t)}var W=class extends o{constructor(){super("reduce"),this.message="Reduce of an empty stream requires an initial value"}};function D(e){if(e=A(e),k(e))return 0;if(e<0)throw new s("number",">= 0",e);return e}t.exports.streamReturningOperators={asIndexedPairs:O((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;return null!=e&&u(e,"options"),null!=(null===e||void 0===e?void 0:e.signal)&&l(e.signal,"options.signal"),Object(m.a)((function*(){let t=0;var r,i=!1,n=!1;try{for(var o,s=Object(b.a)(this);i=!(o=yield Object(g.a)(s.next())).done;i=!1){let r=o.value;var l;if(null!=e&&null!==(l=e.signal)&&void 0!==l&&l.aborted)throw new a({cause:e.signal.reason});yield[t++,r]}}catch(c){n=!0,r=c}finally{try{i&&null!=s.return&&(yield Object(g.a)(s.return()))}finally{if(n)throw r}}})).call(this)}),"readable.asIndexedPairs will be removed in a future version."),drop:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return null!=t&&u(t,"options"),null!=(null===t||void 0===t?void 0:t.signal)&&l(t.signal,"options.signal"),e=D(e),Object(m.a)((function*(){var r;if(null!=t&&null!==(r=t.signal)&&void 0!==r&&r.aborted)throw new a;var i,n=!1,o=!1;try{for(var s,l=Object(b.a)(this);n=!(s=yield Object(g.a)(l.next())).done;n=!1){let r=s.value;var c;if(null!=t&&null!==(c=t.signal)&&void 0!==c&&c.aborted)throw new a;e--<=0&&(yield r)}}catch(u){o=!0,i=u}finally{try{n&&null!=l.return&&(yield Object(g.a)(l.return()))}finally{if(o)throw i}}})).call(this)},filter:U,flatMap:function(e,t){let r=N.call(this,e,t);return Object(m.a)((function*(){var e,t=!1,i=!1;try{for(var n,o=Object(b.a)(r);t=!(n=yield Object(g.a)(o.next())).done;t=!1){let e=n.value;yield*Object(p.a)(Object(b.a)(e),g.a)}}catch(s){i=!0,e=s}finally{try{t&&null!=o.return&&(yield Object(g.a)(o.return()))}finally{if(i)throw e}}})).call(this)},map:N,take:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return null!=t&&u(t,"options"),null!=(null===t||void 0===t?void 0:t.signal)&&l(t.signal,"options.signal"),e=D(e),Object(m.a)((function*(){var r;if(null!=t&&null!==(r=t.signal)&&void 0!==r&&r.aborted)throw new a;var i,n=!1,o=!1;try{for(var s,l=Object(b.a)(this);n=!(s=yield Object(g.a)(l.next())).done;n=!1){let r=s.value;var c;if(null!=t&&null!==(c=t.signal)&&void 0!==c&&c.aborted)throw new a;if(e-- >0&&(yield r),e<=0)return}}catch(u){o=!0,i=u}finally{try{n&&null!=l.return&&(yield Object(g.a)(l.return()))}finally{if(o)throw i}}})).call(this)},compose:function(e,t){if(null!=t&&u(t,"options"),null!=(null===t||void 0===t?void 0:t.signal)&&l(t.signal,"options.signal"),_(e)&&!w(e))throw new i("stream",e,"must be writable");let r=y(this,e);return null!=t&&t.signal&&v(t.signal,r),r}},t.exports.promiseReturningOperators={every:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if("function"!=typeof e)throw new n("fn",["Function","AsyncFunction"],e);return!await L.call(this,(async function(){return!await e(...arguments)}),t)},forEach:async function(e,t){if("function"!=typeof e)throw new n("fn",["Function","AsyncFunction"],e);var r,i=!1,o=!1;try{for(var s,a=Object(b.a)(N.call(this,(async function(t,r){return await e(t,r),M}),t));i=!(s=await a.next()).done;i=!1){s.value}}catch(l){o=!0,r=l}finally{try{i&&null!=a.return&&await a.return()}finally{if(o)throw r}}},reduce:async function(e,t,i){var o;if("function"!=typeof e)throw new n("reducer",["Function","AsyncFunction"],e);null!=i&&u(i,"options"),null!=(null===i||void 0===i?void 0:i.signal)&&l(i.signal,"options.signal");let s=arguments.length>1;if(null!=i&&null!==(o=i.signal)&&void 0!==o&&o.aborted){let e=new a(void 0,{cause:i.signal.reason});throw this.once("error",(()=>{})),await f(this.destroy(e)),e}let c=new r,p=c.signal;if(null!=i&&i.signal){let e={once:!0,[h]:this,[d]:!0};i.signal.addEventListener("abort",(()=>c.abort()),e)}let g=!1;try{var m,y=!1,v=!1;try{for(var w,_=Object(b.a)(this);y=!(w=await _.next()).done;y=!1){let r=w.value;var O;if(g=!0,null!=i&&null!==(O=i.signal)&&void 0!==O&&O.aborted)throw new a;s?t=await e(t,r,{signal:p}):(t=r,s=!0)}}catch(j){v=!0,m=j}finally{try{y&&null!=_.return&&await _.return()}finally{if(v)throw m}}if(!g&&!s)throw new W}finally{c.abort()}return t},toArray:async function(e){null!=e&&u(e,"options"),null!=(null===e||void 0===e?void 0:e.signal)&&l(e.signal,"options.signal");let t=[];var r,i=!1,n=!1;try{for(var o,s=Object(b.a)(this);i=!(o=await s.next()).done;i=!1){let r=o.value;var c;if(null!=e&&null!==(c=e.signal)&&void 0!==c&&c.aborted)throw new a(void 0,{cause:e.signal.reason});j(t,r)}}catch(h){n=!0,r=h}finally{try{i&&null!=s.return&&await s.return()}finally{if(n)throw r}}return t},some:L,find:async function(e,t){var r,i=!1,n=!1;try{for(var o,s=Object(b.a)(U.call(this,e,t));i=!(o=await s.next()).done;i=!1){return o.value}}catch(a){n=!0,r=a}finally{try{i&&null!=s.return&&await s.return()}finally{if(n)throw r}}}}})),mr=S(((e,t)=>{x(),yt(),nt();var{ArrayPrototypePop:r,Promise:i}=wt(),{isIterable:n,isNodeStream:o,isWebStream:s}=$t(),{pipelineImpl:a}=pr(),{finished:l}=Zt();yr(),t.exports={finished:l,pipeline:function(){for(var e=arguments.length,t=new Array(e),l=0;l<e;l++)t[l]=arguments[l];return new i(((e,i)=>{let l,c,u=t[t.length-1];if(u&&"object"==typeof u&&!o(u)&&!n(u)&&!s(u)){let e=r(t);l=e.signal,c=e.end}a(t,((t,r)=>{t?i(t):e(r)}),{signal:l,end:c})}))}}})),yr=S(((e,t)=>{x(),yt(),nt();var{Buffer:r}=(mt(),A(ot)),{ObjectDefineProperty:i,ObjectKeys:n,ReflectApply:o}=wt(),{promisify:{custom:s}}=Yt(),{streamReturningOperators:a,promiseReturningOperators:l}=gr(),{codes:{ERR_ILLEGAL_CONSTRUCTOR:c}}=Ot(),u=br(),{setDefaultHighWaterMark:h,getDefaultHighWaterMark:d}=nr(),{pipeline:f}=pr(),{destroyer:p}=er(),b=Zt(),g=mr(),m=$t(),y=t.exports=tr().Stream;y.isDestroyed=m.isDestroyed,y.isDisturbed=m.isDisturbed,y.isErrored=m.isErrored,y.isReadable=m.isReadable,y.isWritable=m.isWritable,y.Readable=lr();for(let w of n(a)){let e=function(){if(new.target)throw c();for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return y.Readable.from(o(t,this,r))},t=a[w];i(e,"name",{__proto__:null,value:t.name}),i(e,"length",{__proto__:null,value:t.length}),i(y.Readable.prototype,w,{__proto__:null,value:e,enumerable:!1,configurable:!0,writable:!0})}for(let w of n(l)){let e=function(){if(new.target)throw c();for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return o(t,this,r)},t=l[w];i(e,"name",{__proto__:null,value:t.name}),i(e,"length",{__proto__:null,value:t.length}),i(y.Readable.prototype,w,{__proto__:null,value:e,enumerable:!1,configurable:!0,writable:!0})}y.Writable=cr(),y.Duplex=hr(),y.Transform=dr(),y.PassThrough=fr(),y.pipeline=f;var{addAbortSignal:v}=rr();y.addAbortSignal=v,y.finished=b,y.destroy=p,y.compose=u,y.setDefaultHighWaterMark=h,y.getDefaultHighWaterMark=d,i(y,"promises",{__proto__:null,configurable:!0,enumerable:!0,get:()=>g}),i(f,s,{__proto__:null,enumerable:!0,get:()=>g.pipeline}),i(b,s,{__proto__:null,enumerable:!0,get:()=>g.finished}),y.Stream=y,y._isUint8Array=function(e){return e instanceof Uint8Array},y._uint8ArrayToBuffer=function(e){return r.from(e.buffer,e.byteOffset,e.byteLength)}})),vr=S(((e,t)=>{x(),yt(),nt();var r=yr(),i=mr(),n=r.Readable.destroy;t.exports=r.Readable,t.exports._uint8ArrayToBuffer=r._uint8ArrayToBuffer,t.exports._isUint8Array=r._isUint8Array,t.exports.isDisturbed=r.isDisturbed,t.exports.isErrored=r.isErrored,t.exports.isReadable=r.isReadable,t.exports.Readable=r.Readable,t.exports.Writable=r.Writable,t.exports.Duplex=r.Duplex,t.exports.Transform=r.Transform,t.exports.PassThrough=r.PassThrough,t.exports.addAbortSignal=r.addAbortSignal,t.exports.finished=r.finished,t.exports.destroy=r.destroy,t.exports.destroy=n,t.exports.pipeline=r.pipeline,t.exports.compose=r.compose,Object.defineProperty(r,"promises",{configurable:!0,enumerable:!0,get:()=>i}),t.exports.Stream=r.Stream,t.exports.default=t.exports})),wr=S(((e,t)=>{x(),yt(),nt(),"function"==typeof Object.create?t.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}})),_r=S(((e,t)=>{x(),yt(),nt();var{Buffer:r}=(mt(),A(ot)),i=Symbol.for("BufferList");function n(e){if(!(this instanceof n))return new n(e);n._init.call(this,e)}n._init=function(e){Object.defineProperty(this,i,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)},n.prototype._new=function(e){return new n(e)},n.prototype._offset=function(e){if(0===e)return[0,0];let t=0;for(let r=0;r<this._bufs.length;r++){let i=t+this._bufs[r].length;if(e<i||r===this._bufs.length-1)return[r,e-t];t=i}},n.prototype._reverseOffset=function(e){let t=e[0],r=e[1];for(let i=0;i<t;i++)r+=this._bufs[i].length;return r},n.prototype.get=function(e){if(e>this.length||e<0)return;let t=this._offset(e);return this._bufs[t[0]][t[1]]},n.prototype.slice=function(e,t){return"number"==typeof e&&e<0&&(e+=this.length),"number"==typeof t&&t<0&&(t+=this.length),this.copy(null,0,e,t)},n.prototype.copy=function(e,t,i,n){if(("number"!=typeof i||i<0)&&(i=0),("number"!=typeof n||n>this.length)&&(n=this.length),i>=this.length||n<=0)return e||r.alloc(0);let o=!!e,s=this._offset(i),a=n-i,l=a,c=o&&t||0,u=s[1];if(0===i&&n===this.length){if(!o)return 1===this._bufs.length?this._bufs[0]:r.concat(this._bufs,this.length);for(let t=0;t<this._bufs.length;t++)this._bufs[t].copy(e,c),c+=this._bufs[t].length;return e}if(l<=this._bufs[s[0]].length-u)return o?this._bufs[s[0]].copy(e,t,u,u+l):this._bufs[s[0]].slice(u,u+l);o||(e=r.allocUnsafe(a));for(let r=s[0];r<this._bufs.length;r++){let t=this._bufs[r].length-u;if(!(l>t)){this._bufs[r].copy(e,c,u,u+l),c+=t;break}this._bufs[r].copy(e,c,u),c+=t,l-=t,u&&(u=0)}return e.length>c?e.slice(0,c):e},n.prototype.shallowSlice=function(e,t){if(e=e||0,t="number"!=typeof t?this.length:t,e<0&&(e+=this.length),t<0&&(t+=this.length),e===t)return this._new();let r=this._offset(e),i=this._offset(t),n=this._bufs.slice(r[0],i[0]+1);return 0===i[1]?n.pop():n[n.length-1]=n[n.length-1].slice(0,i[1]),0!==r[1]&&(n[0]=n[0].slice(r[1])),this._new(n)},n.prototype.toString=function(e,t,r){return this.slice(t,r).toString(e)},n.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;for(;this._bufs.length;){if(!(e>=this._bufs[0].length)){this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift()}return this},n.prototype.duplicate=function(){let e=this._new();for(let t=0;t<this._bufs.length;t++)e.append(this._bufs[t]);return e},n.prototype.append=function(e){if(null==e)return this;if(e.buffer)this._appendBuffer(r.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e))for(let t=0;t<e.length;t++)this.append(e[t]);else if(this._isBufferList(e))for(let t=0;t<e._bufs.length;t++)this.append(e._bufs[t]);else"number"==typeof e&&(e=e.toString()),this._appendBuffer(r.from(e));return this},n.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length},n.prototype.indexOf=function(e,t,i){if(void 0===i&&"string"==typeof t&&(i=t,t=void 0),"function"==typeof e||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if("number"==typeof e?e=r.from([e]):"string"==typeof e?e=r.from(e,i):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=r.from(e.buffer,e.byteOffset,e.byteLength):r.isBuffer(e)||(e=r.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),0===e.length)return t>this.length?this.length:t;let n=this._offset(t),o=n[0],s=n[1];for(;o<this._bufs.length;o++){let t=this._bufs[o];for(;s<t.length;)if(t.length-s>=e.length){let r=t.indexOf(e,s);if(-1!==r)return this._reverseOffset([o,r]);s=t.length-e.length+1}else{let t=this._reverseOffset([o,s]);if(this._match(t,e))return t;s++}s=0}return-1},n.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(let r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0},function(){let e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readBigInt64BE:8,readBigInt64LE:8,readBigUInt64BE:8,readBigUInt64LE:8,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let t in e)!function(t){n.prototype[t]=null===e[t]?function(e,r){return this.slice(e,e+r)[t](0,r)}:function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.slice(r,r+e[t])[t](0)}}(t)}(),n.prototype._isBufferList=function(e){return e instanceof n||n.isBufferList(e)},n.isBufferList=function(e){return null!=e&&e[i]},t.exports=n})),Or=S(((e,t)=>{x(),yt(),nt();var r=vr().Duplex,i=wr(),n=_r();function o(e){if(!(this instanceof o))return new o(e);if("function"==typeof e){this._callback=e;let t=function(e){this._callback&&(this._callback(e),this._callback=null)}.bind(this);this.on("pipe",(function(e){e.on("error",t)})),this.on("unpipe",(function(e){e.removeListener("error",t)})),e=null}n._init.call(this,e),r.call(this)}i(o,r),Object.assign(o.prototype,n.prototype),o.prototype._new=function(e){return new o(e)},o.prototype._write=function(e,t,r){this._appendBuffer(e),"function"==typeof r&&r()},o.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)},o.prototype.end=function(e){r.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)},o.prototype._destroy=function(e,t){this._bufs.length=0,this.length=0,t(e)},o.prototype._isBufferList=function(e){return e instanceof o||e instanceof n||o.isBufferList(e)},o.isBufferList=n.isBufferList,t.exports=o,t.exports.BufferListStream=o,t.exports.BufferList=n})),jr=S(((e,t)=>{x(),yt(),nt();t.exports=class{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}}})),Sr=S(((e,t)=>{x(),yt(),nt();var r=t.exports,{Buffer:i}=(mt(),A(ot));r.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},r.requiredHeaderFlags={1:0,2:0,4:0,5:0,6:2,7:0,8:2,9:0,10:2,11:0,12:0,13:0,14:0,15:0},r.requiredHeaderFlagsErrors={};for(let o in r.requiredHeaderFlags){let e=r.requiredHeaderFlags[o];r.requiredHeaderFlagsErrors[o]="Invalid header flag bits, must be 0x"+e.toString(16)+" for "+r.types[o]+" packet"}r.codes={};for(let o in r.types){let e=r.types[o];r.codes[e]=o}r.CMD_SHIFT=4,r.CMD_MASK=240,r.DUP_MASK=8,r.QOS_MASK=3,r.QOS_SHIFT=1,r.RETAIN_MASK=1,r.VARBYTEINT_MASK=127,r.VARBYTEINT_FIN_MASK=128,r.VARBYTEINT_MAX=268435455,r.SESSIONPRESENT_MASK=1,r.SESSIONPRESENT_HEADER=i.from([r.SESSIONPRESENT_MASK]),r.CONNACK_HEADER=i.from([r.codes.connack<<r.CMD_SHIFT]),r.USERNAME_MASK=128,r.PASSWORD_MASK=64,r.WILL_RETAIN_MASK=32,r.WILL_QOS_MASK=24,r.WILL_QOS_SHIFT=3,r.WILL_FLAG_MASK=4,r.CLEAN_SESSION_MASK=2,r.CONNECT_HEADER=i.from([r.codes.connect<<r.CMD_SHIFT]),r.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},r.propertiesCodes={};for(let o in r.properties){let e=r.properties[o];r.propertiesCodes[e]=o}function n(e){return[0,1,2].map((t=>[0,1].map((n=>[0,1].map((o=>{let s=i.alloc(1);return s.writeUInt8(r.codes[e]<<r.CMD_SHIFT|(n?r.DUP_MASK:0)|t<<r.QOS_SHIFT|o,0,!0),s}))))))}r.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"},r.PUBLISH_HEADER=n("publish"),r.SUBSCRIBE_HEADER=n("subscribe"),r.SUBSCRIBE_OPTIONS_QOS_MASK=3,r.SUBSCRIBE_OPTIONS_NL_MASK=1,r.SUBSCRIBE_OPTIONS_NL_SHIFT=2,r.SUBSCRIBE_OPTIONS_RAP_MASK=1,r.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,r.SUBSCRIBE_OPTIONS_RH_MASK=3,r.SUBSCRIBE_OPTIONS_RH_SHIFT=4,r.SUBSCRIBE_OPTIONS_RH=[0,16,32],r.SUBSCRIBE_OPTIONS_NL=4,r.SUBSCRIBE_OPTIONS_RAP=8,r.SUBSCRIBE_OPTIONS_QOS=[0,1,2],r.UNSUBSCRIBE_HEADER=n("unsubscribe"),r.ACKS={unsuback:n("unsuback"),puback:n("puback"),pubcomp:n("pubcomp"),pubrel:n("pubrel"),pubrec:n("pubrec")},r.SUBACK_HEADER=i.from([r.codes.suback<<r.CMD_SHIFT]),r.VERSION3=i.from([3]),r.VERSION4=i.from([4]),r.VERSION5=i.from([5]),r.VERSION131=i.from([131]),r.VERSION132=i.from([132]),r.QOS=[0,1,2].map((e=>i.from([e]))),r.EMPTY={pingreq:i.from([r.codes.pingreq<<4,0]),pingresp:i.from([r.codes.pingresp<<4,0]),disconnect:i.from([r.codes.disconnect<<4,0])},r.MQTT5_PUBACK_PUBREC_CODES={0:"Success",16:"No matching subscribers",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",144:"Topic Name invalid",145:"Packet identifier in use",151:"Quota exceeded",153:"Payload format invalid"},r.MQTT5_PUBREL_PUBCOMP_CODES={0:"Success",146:"Packet Identifier not found"},r.MQTT5_SUBACK_CODES={0:"Granted QoS 0",1:"Granted QoS 1",2:"Granted QoS 2",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use",151:"Quota exceeded",158:"Shared Subscriptions not supported",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"},r.MQTT5_UNSUBACK_CODES={0:"Success",17:"No subscription existed",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use"},r.MQTT5_DISCONNECT_CODES={0:"Normal disconnection",4:"Disconnect with Will Message",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",135:"Not authorized",137:"Server busy",139:"Server shutting down",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"},r.MQTT5_AUTH_CODES={0:"Success",24:"Continue authentication",25:"Re-authenticate"}})),Er=S(((e,t)=>{x(),yt(),nt();var r=1e3,i=6e4,n=60*i,o=24*n,s=7*o,a=365.25*o;function l(e,t,r,i){var n=t>=1.5*r;return Math.round(e/r)+" "+i+(n?"s":"")}t.exports=function(e,t){t=t||{};var c=typeof e;if("string"===c&&e.length>0)return function(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var l=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return l*a;case"weeks":case"week":case"w":return l*s;case"days":case"day":case"d":return l*o;case"hours":case"hour":case"hrs":case"hr":case"h":return l*n;case"minutes":case"minute":case"mins":case"min":case"m":return l*i;case"seconds":case"second":case"secs":case"sec":case"s":return l*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}}}(e);if("number"===c&&isFinite(e))return t.long?function(e){var t=Math.abs(e);return t>=o?l(e,t,o,"day"):t>=n?l(e,t,n,"hour"):t>=i?l(e,t,i,"minute"):t>=r?l(e,t,r,"second"):e+" ms"}(e):function(e){var t=Math.abs(e);return t>=o?Math.round(e/o)+"d":t>=n?Math.round(e/n)+"h":t>=i?Math.round(e/i)+"m":t>=r?Math.round(e/r)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}})),Ar=S(((e,t)=>{x(),yt(),nt(),t.exports=function(e){function t(e){let i,n,o,s=null;function a(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];if(!a.enabled)return;let o=a,s=Number(new Date),l=s-(i||s);o.diff=l,o.prev=i,o.curr=s,i=s,r[0]=t.coerce(r[0]),"string"!=typeof r[0]&&r.unshift("%O");let c=0;r[0]=r[0].replace(/%([a-zA-Z%])/g,((e,i)=>{if("%%"===e)return"%";c++;let n=t.formatters[i];if("function"==typeof n){let t=r[c];e=n.call(o,t),r.splice(c,1),c--}return e})),t.formatArgs.call(o,r),(o.log||t.log).apply(o,r)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(n!==t.namespaces&&(n=t.namespaces,o=t.enabled(e)),o),set:e=>{s=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,r){let i=t(this.namespace+(typeof r>"u"?":":r)+e);return i.log=this.log,i}function i(e,t){let r=0,i=0,n=-1,o=0;for(;r<e.length;)if(i<t.length&&(t[i]===e[r]||"*"===t[i]))"*"===t[i]?(n=i,o=r,i++):(r++,i++);else{if(-1===n)return!1;i=n+1,o++,r=o}for(;i<t.length&&"*"===t[i];)i++;return i===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(let i of r)"-"===i[0]?t.skips.push(i.slice(1)):t.names.push(i)},t.enabled=function(e){for(let r of t.skips)if(i(e,r))return!1;for(let r of t.names)if(i(e,r))return!0;return!1},t.humanize=Er(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}})),xr=S(((e,t)=>{x(),yt(),nt(),e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let i=0,n=0;e[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(i++,"%c"===e&&(n=i))})),e.splice(n,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(r){}},e.load=function(){let t;try{t=e.storage.getItem("debug")}catch(r){}return!t&&typeof k<"u"&&"env"in k&&(t=k.env.DEBUG),t},e.useColors=function(){if(typeof window<"u"&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage=function(){try{return localStorage}catch(e){}}(),e.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=Ar()(e);var{formatters:r}=t.exports;r.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}})),kr=S(((e,t)=>{x(),yt(),nt();var r=Or(),{EventEmitter:i}=(Qt(),A(Ft)),n=jr(),o=Sr(),s=xr()("mqtt-packet:parser");t.exports=class e extends i{constructor(){super(),this.parser=this.constructor.parser}static parser(t){return this instanceof e?(this.settings=t||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):(new e).parser(t)}_resetState(){s("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new n,this.error=null,this._list=r(),this._stateCounter=0}parse(e){for(this.error&&this._resetState(),this._list.append(e),s("parse: current state: %s",this._states[this._stateCounter]);(-1!==this.packet.length||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,s("parse: state complete. _stateCounter is now: %d",this._stateCounter),s("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return s("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){let e=this._list.readUInt8(0),t=e>>o.CMD_SHIFT;this.packet.cmd=o.types[t];let r=15&e,i=o.requiredHeaderFlags[t];return null!=i&&r!==i?this._emitError(new Error(o.requiredHeaderFlagsErrors[t])):(this.packet.retain=0!==(e&o.RETAIN_MASK),this.packet.qos=e>>o.QOS_SHIFT&o.QOS_MASK,this.packet.qos>2?this._emitError(new Error("Packet must not have both QoS bits set to 1")):(this.packet.dup=0!==(e&o.DUP_MASK),s("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0))}_parseLength(){let e=this._parseVarByteNum(!0);return e&&(this.packet.length=e.value,this._list.consume(e.bytes)),s("_parseLength %d",e.value),!!e}_parsePayload(){s("_parsePayload: payload %O",this._list);let e=!1;if(0===this.packet.length||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return s("_parsePayload complete result: %s",e),e}_parseConnect(){s("_parseConnect");let e,t,r,i,n={},a=this.packet,l=this._parseString();if(null===l)return this._emitError(new Error("Cannot parse protocolId"));if("MQTT"!==l&&"MQIsdp"!==l)return this._emitError(new Error("Invalid protocolId"));if(a.protocolId=l,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(a.protocolVersion=this._list.readUInt8(this._pos),a.protocolVersion>=128&&(a.bridgeMode=!0,a.protocolVersion=a.protocolVersion-128),3!==a.protocolVersion&&4!==a.protocolVersion&&5!==a.protocolVersion)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(1&this._list.readUInt8(this._pos))return this._emitError(new Error("Connect flag bit 0 must be 0, but got 1"));n.username=this._list.readUInt8(this._pos)&o.USERNAME_MASK,n.password=this._list.readUInt8(this._pos)&o.PASSWORD_MASK,n.will=this._list.readUInt8(this._pos)&o.WILL_FLAG_MASK;let c=!!(this._list.readUInt8(this._pos)&o.WILL_RETAIN_MASK),u=(this._list.readUInt8(this._pos)&o.WILL_QOS_MASK)>>o.WILL_QOS_SHIFT;if(n.will)a.will={},a.will.retain=c,a.will.qos=u;else{if(c)return this._emitError(new Error("Will Retain Flag must be set to zero when Will Flag is set to 0"));if(u)return this._emitError(new Error("Will QoS must be set to zero when Will Flag is set to 0"))}if(a.clean=0!==(this._list.readUInt8(this._pos)&o.CLEAN_SESSION_MASK),this._pos++,a.keepalive=this._parseNum(),-1===a.keepalive)return this._emitError(new Error("Packet too short"));if(5===a.protocolVersion){let e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(a.properties=e)}let h=this._parseString();if(null===h)return this._emitError(new Error("Packet too short"));if(a.clientId=h,s("_parseConnect: packet.clientId: %s",a.clientId),n.will){if(5===a.protocolVersion){let e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(a.will.properties=e)}if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse will topic"));if(a.will.topic=e,s("_parseConnect: packet.will.topic: %s",a.will.topic),t=this._parseBuffer(),null===t)return this._emitError(new Error("Cannot parse will payload"));a.will.payload=t,s("_parseConnect: packet.will.paylaod: %s",a.will.payload)}if(n.username){if(i=this._parseString(),null===i)return this._emitError(new Error("Cannot parse username"));a.username=i,s("_parseConnect: packet.username: %s",a.username)}if(n.password){if(r=this._parseBuffer(),null===r)return this._emitError(new Error("Cannot parse password"));a.password=r}return this.settings=a,s("_parseConnect: complete"),a}_parseConnack(){s("_parseConnack");let e=this.packet;if(this._list.length<1)return null;let t=this._list.readUInt8(this._pos++);if(t>1)return this._emitError(new Error("Invalid connack flags, bits 7-1 must be set to 0"));if(e.sessionPresent=!!(t&o.SESSIONPRESENT_MASK),5===this.settings.protocolVersion)this._list.length>=2?e.reasonCode=this._list.readUInt8(this._pos++):e.reasonCode=0;else{if(this._list.length<2)return null;e.returnCode=this._list.readUInt8(this._pos++)}if(-1===e.returnCode||-1===e.reasonCode)return this._emitError(new Error("Cannot parse return code"));if(5===this.settings.protocolVersion){let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}s("_parseConnack: complete")}_parsePublish(){s("_parsePublish");let e=this.packet;if(e.topic=this._parseString(),null===e.topic)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0)||this._parseMessageId()){if(5===this.settings.protocolVersion){let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}e.payload=this._list.slice(this._pos,e.length),s("_parsePublish: payload from buffer list: %o",e.payload)}}_parseSubscribe(){s("_parseSubscribe");let e,t,r,i,n,a,l,c=this.packet;if(c.subscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){let e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(c.properties=e)}if(c.length<=0)return this._emitError(new Error("Malformed subscribe, no payload specified"));for(;this._pos<c.length;){if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=c.length)return this._emitError(new Error("Malformed Subscribe Payload"));if(t=this._parseByte(),5===this.settings.protocolVersion){if(192&t)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-6 must be 0"))}else if(252&t)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-2 must be 0"));if(r=t&o.SUBSCRIBE_OPTIONS_QOS_MASK,r>2)return this._emitError(new Error("Invalid subscribe QoS, must be <= 2"));if(a=0!==(t>>o.SUBSCRIBE_OPTIONS_NL_SHIFT&o.SUBSCRIBE_OPTIONS_NL_MASK),n=0!==(t>>o.SUBSCRIBE_OPTIONS_RAP_SHIFT&o.SUBSCRIBE_OPTIONS_RAP_MASK),i=t>>o.SUBSCRIBE_OPTIONS_RH_SHIFT&o.SUBSCRIBE_OPTIONS_RH_MASK,i>2)return this._emitError(new Error("Invalid retain handling, must be <= 2"));l={topic:e,qos:r},5===this.settings.protocolVersion?(l.nl=a,l.rap=n,l.rh=i):this.settings.bridgeMode&&(l.rh=0,l.rap=!0,l.nl=!0),s("_parseSubscribe: push subscription `%s` to subscription",l),c.subscriptions.push(l)}}}_parseSuback(){s("_parseSuback");let e=this.packet;if(this.packet.granted=[],this._parseMessageId()){if(5===this.settings.protocolVersion){let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}if(e.length<=0)return this._emitError(new Error("Malformed suback, no payload specified"));for(;this._pos<this.packet.length;){let e=this._list.readUInt8(this._pos++);if(5===this.settings.protocolVersion){if(!o.MQTT5_SUBACK_CODES[e])return this._emitError(new Error("Invalid suback code"))}else if(e>2&&128!==e)return this._emitError(new Error("Invalid suback QoS, must be 0, 1, 2 or 128"));this.packet.granted.push(e)}}}_parseUnsubscribe(){s("_parseUnsubscribe");let e=this.packet;if(e.unsubscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}if(e.length<=0)return this._emitError(new Error("Malformed unsubscribe, no payload specified"));for(;this._pos<e.length;){let t=this._parseString();if(null===t)return this._emitError(new Error("Cannot parse topic"));s("_parseUnsubscribe: push topic `%s` to unsubscriptions",t),e.unsubscriptions.push(t)}}}_parseUnsuback(){s("_parseUnsuback");let e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if((3===this.settings.protocolVersion||4===this.settings.protocolVersion)&&2!==e.length)return this._emitError(new Error("Malformed unsuback, payload length must be 2"));if(e.length<=0)return this._emitError(new Error("Malformed unsuback, no payload specified"));if(5===this.settings.protocolVersion){let t=this._parseProperties();for(Object.getOwnPropertyNames(t).length&&(e.properties=t),e.granted=[];this._pos<this.packet.length;){let e=this._list.readUInt8(this._pos++);if(!o.MQTT5_UNSUBACK_CODES[e])return this._emitError(new Error("Invalid unsuback code"));this.packet.granted.push(e)}}}_parseConfirmation(){s("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);let e=this.packet;if(this._parseMessageId(),5===this.settings.protocolVersion){if(e.length>2){switch(e.reasonCode=this._parseByte(),this.packet.cmd){case"puback":case"pubrec":if(!o.MQTT5_PUBACK_PUBREC_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break;case"pubrel":case"pubcomp":if(!o.MQTT5_PUBREL_PUBCOMP_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"))}s("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)}else e.reasonCode=0;if(e.length>3){let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}}return!0}_parseDisconnect(){let e=this.packet;if(s("_parseDisconnect"),5===this.settings.protocolVersion){this._list.length>0?(e.reasonCode=this._parseByte(),o.MQTT5_DISCONNECT_CODES[e.reasonCode]||this._emitError(new Error("Invalid disconnect reason code"))):e.reasonCode=0;let t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}return s("_parseDisconnect result: true"),!0}_parseAuth(){s("_parseAuth");let e=this.packet;if(5!==this.settings.protocolVersion)return this._emitError(new Error("Not supported auth packet for this version MQTT"));if(e.reasonCode=this._parseByte(),!o.MQTT5_AUTH_CODES[e.reasonCode])return this._emitError(new Error("Invalid auth reason code"));let t=this._parseProperties();return Object.getOwnPropertyNames(t).length&&(e.properties=t),s("_parseAuth: result: true"),!0}_parseMessageId(){let e=this.packet;return e.messageId=this._parseNum(),null===e.messageId?(this._emitError(new Error("Cannot parse messageId")),!1):(s("_parseMessageId: packet.messageId %d",e.messageId),!0)}_parseString(e){let t=this._parseNum(),r=t+this._pos;if(-1===t||r>this._list.length||r>this.packet.length)return null;let i=this._list.toString("utf8",this._pos,r);return this._pos+=t,s("_parseString: result: %s",i),i}_parseStringPair(){return s("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){let e=this._parseNum(),t=e+this._pos;if(-1===e||t>this._list.length||t>this.packet.length)return null;let r=this._list.slice(this._pos,t);return this._pos+=e,s("_parseBuffer: result: %o",r),r}_parseNum(){if(this._list.length-this._pos<2)return-1;let e=this._list.readUInt16BE(this._pos);return this._pos+=2,s("_parseNum: result: %s",e),e}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;let e=this._list.readUInt32BE(this._pos);return this._pos+=4,s("_parse4ByteNum: result: %s",e),e}_parseVarByteNum(e){s("_parseVarByteNum");let t,r=0,i=1,n=0,a=!1,l=this._pos?this._pos:0;for(;r<4&&l+r<this._list.length;){if(t=this._list.readUInt8(l+r++),n+=i*(t&o.VARBYTEINT_MASK),i*=128,0===(t&o.VARBYTEINT_FIN_MASK)){a=!0;break}if(this._list.length<=r)break}return!a&&4===r&&this._list.length>=r&&this._emitError(new Error("Invalid variable byte integer")),l&&(this._pos+=r),a=!!a&&(e?{bytes:r,value:n}:n),s("_parseVarByteNum: result: %o",a),a}_parseByte(){let e;return this._pos<this._list.length&&(e=this._list.readUInt8(this._pos),this._pos++),s("_parseByte: result: %o",e),e}_parseByType(e){switch(s("_parseByType: type: %s",e),e){case"byte":return 0!==this._parseByte();case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){s("_parseProperties");let e=this._parseVarByteNum(),t=this._pos+e,r={};for(;this._pos<t;){let e=this._parseByte();if(!e)return this._emitError(new Error("Cannot parse property code type")),!1;let t=o.propertiesCodes[e];if(!t)return this._emitError(new Error("Unknown property")),!1;if("userProperties"!==t)r[t]?(Array.isArray(r[t])||(r[t]=[r[t]]),r[t].push(this._parseByType(o.propertiesTypes[t]))):r[t]=this._parseByType(o.propertiesTypes[t]);else{r[t]||(r[t]=Object.create(null));let e=this._parseByType(o.propertiesTypes[t]);if(r[t][e.name])if(Array.isArray(r[t][e.name]))r[t][e.name].push(e.value);else{let i=r[t][e.name];r[t][e.name]=[i],r[t][e.name].push(e.value)}else r[t][e.name]=e.value}}return r}_newPacket(){return s("_newPacket"),this.packet&&(this._list.consume(this.packet.length),s("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),s("_newPacket: new packet"),this.packet=new n,this._pos=0,!0}_emitError(e){s("_emitError",e),this.error=e,this.emit("error",e)}}})),Ir=S(((e,t)=>{x(),yt(),nt();var{Buffer:r}=(mt(),A(ot)),i={},n=r.isBuffer(r.from([1,2]).subarray(0,1));function o(e){let t=r.allocUnsafe(2);return t.writeUInt8(e>>8,0),t.writeUInt8(255&e,1),t}t.exports={cache:i,generateCache:function(){for(let e=0;e<65536;e++)i[e]=o(e)},generateNumber:o,genBufVariableByteInt:function(e){let t=0,i=0,o=r.allocUnsafe(4);do{t=e%128|0,(e=e/128|0)>0&&(t|=128),o.writeUInt8(t,i++)}while(e>0&&i<4);return e>0&&(i=0),n?o.subarray(0,i):o.slice(0,i)},generate4ByteBuffer:function(e){let t=r.allocUnsafe(4);return t.writeUInt32BE(e,0),t}}})),Tr=S(((e,t)=>{x(),yt(),nt(),typeof k>"u"||!k.version||0===k.version.indexOf("v0.")||0===k.version.indexOf("v1.")&&0!==k.version.indexOf("v1.8.")?t.exports={nextTick:function(e,t,r,i){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var n,o,s=arguments.length;switch(s){case 0:case 1:return k.nextTick(e);case 2:return k.nextTick((function(){e.call(null,t)}));case 3:return k.nextTick((function(){e.call(null,t,r)}));case 4:return k.nextTick((function(){e.call(null,t,r,i)}));default:for(n=new Array(s-1),o=0;o<n.length;)n[o++]=arguments[o];return k.nextTick((function(){e.apply(null,n)}))}}}:t.exports=k})),Cr=S(((e,t)=>{x(),yt(),nt();var r=Sr(),{Buffer:i}=(mt(),A(ot)),n=i.allocUnsafe(0),o=i.from([0]),s=Ir(),a=Tr().nextTick,l=xr()("mqtt-packet:writeToStream"),c=s.cache,u=s.generateNumber,h=s.generateCache,d=s.genBufVariableByteInt,f=s.generate4ByteBuffer,p=O,b=!0;function g(e,t,s){switch(l("generate called"),t.cork&&(t.cork(),a(m,t)),b&&(b=!1,h()),l("generate: packet.cmd: %s",e.cmd),e.cmd){case"connect":return function(e,t,n){let o=e||{},s=o.protocolId||"MQTT",a=o.protocolVersion||4,l=o.will,c=o.clean,u=o.keepalive||0,h=o.clientId||"",d=o.username,f=o.password,b=o.properties;void 0===c&&(c=!0);let g,m,y=0;if(!s||"string"!=typeof s&&!i.isBuffer(s))return t.destroy(new Error("Invalid protocolId")),!1;if(y+=s.length+2,3!==a&&4!==a&&5!==a)return t.destroy(new Error("Invalid protocol version")),!1;if(y+=1,("string"==typeof h||i.isBuffer(h))&&(h||a>=4)&&(h||c))y+=i.byteLength(h)+2;else{if(a<4)return t.destroy(new Error("clientId must be supplied before 3.1.1")),!1;if(1*c===0)return t.destroy(new Error("clientId must be given if cleanSession set to 0")),!1}if("number"!=typeof u||u<0||u>65535||u%1!==0)return t.destroy(new Error("Invalid keepalive")),!1;if(y+=2,y+=1,5===a){if(g=E(t,b),!g)return!1;y+=g.length}if(l){if("object"!=typeof l)return t.destroy(new Error("Invalid will")),!1;if(!l.topic||"string"!=typeof l.topic)return t.destroy(new Error("Invalid will topic")),!1;if(y+=i.byteLength(l.topic)+2,y+=2,l.payload){if(!(l.payload.length>=0))return t.destroy(new Error("Invalid will payload")),!1;"string"==typeof l.payload?y+=i.byteLength(l.payload):y+=l.payload.length}if(m={},5===a){if(m=E(t,l.properties),!m)return!1;y+=m.length}}let _=!1;if(null!=d){if(!R(d))return t.destroy(new Error("Invalid username")),!1;_=!0,y+=i.byteLength(d)+2}if(null!=f){if(!_)return t.destroy(new Error("Username is required to use password")),!1;if(!R(f))return t.destroy(new Error("Invalid password")),!1;y+=C(f)+2}t.write(r.CONNECT_HEADER),v(t,y),S(t,s),o.bridgeMode&&(a+=128),t.write(131===a?r.VERSION131:132===a?r.VERSION132:4===a?r.VERSION4:5===a?r.VERSION5:r.VERSION3);let O=0;return O|=null!=d?r.USERNAME_MASK:0,O|=null!=f?r.PASSWORD_MASK:0,O|=l&&l.retain?r.WILL_RETAIN_MASK:0,O|=l&&l.qos?l.qos<<r.WILL_QOS_SHIFT:0,O|=l?r.WILL_FLAG_MASK:0,O|=c?r.CLEAN_SESSION_MASK:0,t.write(i.from([O])),p(t,u),5===a&&g.write(),S(t,h),l&&(5===a&&m.write(),w(t,l.topic),S(t,l.payload)),null!=d&&S(t,d),null!=f&&S(t,f),!0}(e,t);case"connack":return function(e,t,n){var s;let a=n?n.protocolVersion:4,l=e||{},c=5===a?l.reasonCode:l.returnCode,u=l.properties,h=2;if("number"!=typeof c)return t.destroy(new Error("Invalid return code")),!1;let d=null;if(5===a){if(d=E(t,u),!d)return!1;h+=d.length}return t.write(r.CONNACK_HEADER),v(t,h),t.write(l.sessionPresent?r.SESSIONPRESENT_HEADER:o),t.write(i.from([c])),null!==(s=d)&&void 0!==s&&s.write(),!0}(e,t,s);case"publish":return function(e,t,o){var s;l("publish: packet: %o",e);let a=o?o.protocolVersion:4,c=e||{},u=c.qos||0,h=c.retain?r.RETAIN_MASK:0,d=c.topic,f=c.payload||n,b=c.messageId,g=c.properties,m=0;if("string"==typeof d)m+=i.byteLength(d)+2;else{if(!i.isBuffer(d))return t.destroy(new Error("Invalid topic")),!1;m+=d.length+2}if(i.isBuffer(f)?m+=f.length:m+=i.byteLength(f),u&&"number"!=typeof b)return t.destroy(new Error("Invalid messageId")),!1;u&&(m+=2);let y=null;if(5===a){if(y=E(t,g),!y)return!1;m+=y.length}return t.write(r.PUBLISH_HEADER[u][c.dup?1:0][h?1:0]),v(t,m),p(t,C(d)),t.write(d),u>0&&p(t,b),null!==(s=y)&&void 0!==s&&s.write(),l("publish: payload: %o",f),t.write(f)}(e,t,s);case"puback":case"pubrec":case"pubrel":case"pubcomp":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.cmd||"puback",l=s.messageId,c=s.dup&&"pubrel"===a?r.DUP_MASK:0,u=0,h=s.reasonCode,d=s.properties,f=5===o?3:2;if("pubrel"===a&&(u=1),"number"!=typeof l)return t.destroy(new Error("Invalid messageId")),!1;let b=null;if(5===o&&"object"==typeof d){if(b=k(t,d,n,f),!b)return!1;f+=b.length}return t.write(r.ACKS[a][u][c][0]),3===f&&(f+=0!==h?1:-1),v(t,f),p(t,l),5===o&&2!==f&&t.write(i.from([h])),null!==b?b.write():4===f&&t.write(i.from([0])),!0}(e,t,s);case"subscribe":return function(e,t,n){l("subscribe: packet: ");let o=n?n.protocolVersion:4,s=e||{},a=s.dup?r.DUP_MASK:0,c=s.messageId,u=s.subscriptions,h=s.properties,d=0;if("number"!=typeof c)return t.destroy(new Error("Invalid messageId")),!1;d+=2;let f=null;if(5===o){if(f=E(t,h),!f)return!1;d+=f.length}if("object"!=typeof u||!u.length)return t.destroy(new Error("Invalid subscriptions")),!1;for(let r=0;r<u.length;r+=1){let e=u[r].topic,n=u[r].qos;if("string"!=typeof e)return t.destroy(new Error("Invalid subscriptions - invalid topic")),!1;if("number"!=typeof n)return t.destroy(new Error("Invalid subscriptions - invalid qos")),!1;if(5===o){if("boolean"!=typeof(u[r].nl||!1))return t.destroy(new Error("Invalid subscriptions - invalid No Local")),!1;if("boolean"!=typeof(u[r].rap||!1))return t.destroy(new Error("Invalid subscriptions - invalid Retain as Published")),!1;let e=u[r].rh||0;if("number"!=typeof e||e>2)return t.destroy(new Error("Invalid subscriptions - invalid Retain Handling")),!1}d+=i.byteLength(e)+2+1}l("subscribe: writing to stream: %o",r.SUBSCRIBE_HEADER),t.write(r.SUBSCRIBE_HEADER[1][a?1:0][0]),v(t,d),p(t,c),null!==f&&f.write();let b=!0;for(let l of u){let e,n=l.topic,s=l.qos,a=+l.nl,c=+l.rap,u=l.rh;w(t,n),e=r.SUBSCRIBE_OPTIONS_QOS[s],5===o&&(e|=a?r.SUBSCRIBE_OPTIONS_NL:0,e|=c?r.SUBSCRIBE_OPTIONS_RAP:0,e|=u?r.SUBSCRIBE_OPTIONS_RH[u]:0),b=t.write(i.from([e]))}return b}(e,t,s);case"suback":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.messageId,l=s.granted,c=s.properties,u=0;if("number"!=typeof a)return t.destroy(new Error("Invalid messageId")),!1;if(u+=2,"object"!=typeof l||!l.length)return t.destroy(new Error("Invalid qos vector")),!1;for(let r=0;r<l.length;r+=1){if("number"!=typeof l[r])return t.destroy(new Error("Invalid qos vector")),!1;u+=1}let h=null;if(5===o){if(h=k(t,c,n,u),!h)return!1;u+=h.length}return t.write(r.SUBACK_HEADER),v(t,u),p(t,a),null!==h&&h.write(),t.write(i.from(l))}(e,t,s);case"unsubscribe":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.messageId,l=s.dup?r.DUP_MASK:0,c=s.unsubscriptions,u=s.properties,h=0;if("number"!=typeof a)return t.destroy(new Error("Invalid messageId")),!1;if(h+=2,"object"!=typeof c||!c.length)return t.destroy(new Error("Invalid unsubscriptions")),!1;for(let r=0;r<c.length;r+=1){if("string"!=typeof c[r])return t.destroy(new Error("Invalid unsubscriptions")),!1;h+=i.byteLength(c[r])+2}let d=null;if(5===o){if(d=E(t,u),!d)return!1;h+=d.length}t.write(r.UNSUBSCRIBE_HEADER[1][l?1:0][0]),v(t,h),p(t,a),null!==d&&d.write();let f=!0;for(let r=0;r<c.length;r++)f=w(t,c[r]);return f}(e,t,s);case"unsuback":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.messageId,l=s.dup?r.DUP_MASK:0,c=s.granted,u=s.properties,h=s.cmd,d=0,f=2;if("number"!=typeof a)return t.destroy(new Error("Invalid messageId")),!1;if(5===o){if("object"!=typeof c||!c.length)return t.destroy(new Error("Invalid qos vector")),!1;for(let e=0;e<c.length;e+=1){if("number"!=typeof c[e])return t.destroy(new Error("Invalid qos vector")),!1;f+=1}}let b=null;if(5===o){if(b=k(t,u,n,f),!b)return!1;f+=b.length}return t.write(r.ACKS[h][d][l][0]),v(t,f),p(t,a),null!==b&&b.write(),5===o&&t.write(i.from(c)),!0}(e,t,s);case"pingreq":case"pingresp":return function(e,t,i){return t.write(r.EMPTY[e.cmd])}(e,t);case"disconnect":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.reasonCode,l=s.properties,c=5===o?1:0,u=null;if(5===o){if(u=k(t,l,n,c),!u)return!1;c+=u.length}return t.write(i.from([r.codes.disconnect<<4])),v(t,c),5===o&&t.write(i.from([a])),null!==u&&u.write(),!0}(e,t,s);case"auth":return function(e,t,n){let o=n?n.protocolVersion:4,s=e||{},a=s.reasonCode,l=s.properties,c=5===o?1:0;5!==o&&t.destroy(new Error("Invalid mqtt version for auth packet"));let u=k(t,l,n,c);return!!u&&(c+=u.length,t.write(i.from([r.codes.auth<<4])),v(t,c),t.write(i.from([a])),null!==u&&u.write(),!0)}(e,t,s);default:return t.destroy(new Error("Unknown command")),!1}}function m(e){e.uncork()}Object.defineProperty(g,"cacheNumbers",{get:()=>p===O,set(e){e?((!c||0===Object.keys(c).length)&&(b=!0),p=O):(b=!1,p=j)}});var y={};function v(e,t){if(t>r.VARBYTEINT_MAX)return e.destroy(new Error("Invalid variable byte integer: ".concat(t))),!1;let i=y[t];return i||(i=d(t),t<16384&&(y[t]=i)),l("writeVarByteInt: writing to stream: %o",i),e.write(i)}function w(e,t){let r=i.byteLength(t);return p(e,r),l("writeString: %s",t),e.write(t,"utf8")}function _(e,t,r){w(e,t),w(e,r)}function O(e,t){return l("writeNumberCached: number: %d",t),l("writeNumberCached: %o",c[t]),e.write(c[t])}function j(e,t){let r=u(t);return l("writeNumberGenerated: %o",r),e.write(r)}function S(e,t){"string"==typeof t?w(e,t):t?(p(e,t.length),e.write(t)):p(e,0)}function E(e,t){if("object"!=typeof t||null!=t.length)return{length:1,write(){T(e,{},0)}};let n=0;function o(t,n){let o=0;switch(r.propertiesTypes[t]){case"byte":if("boolean"!=typeof n)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=2;break;case"int8":if("number"!=typeof n||n<0||n>255)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=2;break;case"binary":if(n&&null===n)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=1+i.byteLength(n)+2;break;case"int16":if("number"!=typeof n||n<0||n>65535)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=3;break;case"int32":if("number"!=typeof n||n<0||n>4294967295)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=5;break;case"var":if("number"!=typeof n||n<0||n>268435455)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=1+i.byteLength(d(n));break;case"string":if("string"!=typeof n)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=3+i.byteLength(n.toString());break;case"pair":if("object"!=typeof n)return e.destroy(new Error("Invalid ".concat(t,": ").concat(n))),!1;o+=Object.getOwnPropertyNames(n).reduce(((e,t)=>{let r=n[t];return Array.isArray(r)?e+=r.reduce(((e,r)=>e+=3+i.byteLength(t.toString())+2+i.byteLength(r.toString())),0):e+=3+i.byteLength(t.toString())+2+i.byteLength(n[t].toString()),e}),0);break;default:return e.destroy(new Error("Invalid property ".concat(t,": ").concat(n))),!1}return o}if(t)for(let r in t){let e=0,i=0,s=t[r];if(void 0!==s){if(Array.isArray(s))for(let t=0;t<s.length;t++){if(i=o(r,s[t]),!i)return!1;e+=i}else{if(i=o(r,s),!i)return!1;e=i}if(!e)return!1;n+=e}}return{length:i.byteLength(d(n))+n,write(){T(e,t,n)}}}function k(e,t,r,i){let n=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0,s=E(e,t);if(o)for(;i+s.length>o;){let r=n.shift();if(!r||!t[r])return!1;delete t[r],s=E(e,t)}return s}function I(e,t,n){switch(r.propertiesTypes[t]){case"byte":e.write(i.from([r.properties[t]])),e.write(i.from([+n]));break;case"int8":e.write(i.from([r.properties[t]])),e.write(i.from([n]));break;case"binary":e.write(i.from([r.properties[t]])),S(e,n);break;case"int16":e.write(i.from([r.properties[t]])),p(e,n);break;case"int32":e.write(i.from([r.properties[t]])),function(e,t){let r=f(t);l("write4ByteNumber: %o",r),e.write(r)}(e,n);break;case"var":e.write(i.from([r.properties[t]])),v(e,n);break;case"string":e.write(i.from([r.properties[t]])),w(e,n);break;case"pair":Object.getOwnPropertyNames(n).forEach((o=>{let s=n[o];Array.isArray(s)?s.forEach((n=>{e.write(i.from([r.properties[t]])),_(e,o.toString(),n.toString())})):(e.write(i.from([r.properties[t]])),_(e,o.toString(),s.toString()))}));break;default:return e.destroy(new Error("Invalid property ".concat(t," value: ").concat(n))),!1}}function T(e,t,r){v(e,r);for(let i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&null!=t[i]){let r=t[i];if(Array.isArray(r))for(let t=0;t<r.length;t++)I(e,i,r[t]);else I(e,i,r)}}function C(e){return e?e instanceof i?e.length:i.byteLength(e):0}function R(e){return"string"==typeof e||e instanceof i}t.exports=g})),Rr=S(((e,t)=>{x(),yt(),nt();var r=Cr(),{EventEmitter:i}=(Qt(),A(Ft)),{Buffer:n}=(mt(),A(ot));var o=class extends i{constructor(){super(),this._array=new Array(20),this._i=0}write(e){return this._array[this._i++]=e,!0}concat(){let e,t=0,r=new Array(this._array.length),i=this._array,o=0;for(e=0;e<i.length&&void 0!==i[e];e++)"string"!=typeof i[e]?r[e]=i[e].length:r[e]=n.byteLength(i[e]),t+=r[e];let s=n.allocUnsafe(t);for(e=0;e<i.length&&void 0!==i[e];e++)"string"!=typeof i[e]?(i[e].copy(s,o),o+=r[e]):(s.write(i[e],o),o+=r[e]);return s}destroy(e){e&&this.emit("error",e)}};t.exports=function(e,t){let i=new o;return r(e,i,t),i.concat()}})),Pr=S((e=>{x(),yt(),nt(),e.parser=kr().parser,e.generate=Rr(),e.writeToStream=Cr()})),Mr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});e.default=class{constructor(){this.nextId=Math.max(1,Math.floor(65535*Math.random()))}allocate(){let e=this.nextId++;return 65536===this.nextId&&(this.nextId=1),e}getLastAllocated(){return 1===this.nextId?65535:this.nextId-1}register(e){return!0}deallocate(e){}clear(){}}})),Br=S(((e,t)=>{function r(e){return e instanceof pt?pt.from(e):new e.constructor(e.buffer.slice(),e.byteOffset,e.length)}x(),yt(),nt(),t.exports=function(e){if((e=e||{}).circles)return function(e){let t=[],i=[],n=new Map;if(n.set(Date,(e=>new Date(e))),n.set(Map,((e,t)=>new Map(s(Array.from(e),t)))),n.set(Set,((e,t)=>new Set(s(Array.from(e),t)))),e.constructorHandlers)for(let r of e.constructorHandlers)n.set(r[0],r[1]);let o=null;return e.proto?l:a;function s(e,s){let a=Object.keys(e),l=new Array(a.length);for(let c=0;c<a.length;c++){let u=a[c],h=e[u];if("object"!=typeof h||null===h)l[u]=h;else if(h.constructor!==Object&&(o=n.get(h.constructor)))l[u]=o(h,s);else if(ArrayBuffer.isView(h))l[u]=r(h);else{let e=t.indexOf(h);l[u]=-1!==e?i[e]:s(h)}}return l}function a(e){if("object"!=typeof e||null===e)return e;if(Array.isArray(e))return s(e,a);if(e.constructor!==Object&&(o=n.get(e.constructor)))return o(e,a);let l={};t.push(e),i.push(l);for(let s in e){if(!1===Object.hasOwnProperty.call(e,s))continue;let c=e[s];if("object"!=typeof c||null===c)l[s]=c;else if(c.constructor!==Object&&(o=n.get(c.constructor)))l[s]=o(c,a);else if(ArrayBuffer.isView(c))l[s]=r(c);else{let e=t.indexOf(c);l[s]=-1!==e?i[e]:a(c)}}return t.pop(),i.pop(),l}function l(e){if("object"!=typeof e||null===e)return e;if(Array.isArray(e))return s(e,l);if(e.constructor!==Object&&(o=n.get(e.constructor)))return o(e,l);let a={};t.push(e),i.push(a);for(let s in e){let c=e[s];if("object"!=typeof c||null===c)a[s]=c;else if(c.constructor!==Object&&(o=n.get(c.constructor)))a[s]=o(c,l);else if(ArrayBuffer.isView(c))a[s]=r(c);else{let e=t.indexOf(c);a[s]=-1!==e?i[e]:l(c)}}return t.pop(),i.pop(),a}}(e);let t=new Map;if(t.set(Date,(e=>new Date(e))),t.set(Map,((e,t)=>new Map(n(Array.from(e),t)))),t.set(Set,((e,t)=>new Set(n(Array.from(e),t)))),e.constructorHandlers)for(let r of e.constructorHandlers)t.set(r[0],r[1]);let i=null;return e.proto?function e(o){if("object"!=typeof o||null===o)return o;if(Array.isArray(o))return n(o,e);if(o.constructor!==Object&&(i=t.get(o.constructor)))return i(o,e);let s={};for(let n in o){let a=o[n];"object"!=typeof a||null===a?s[n]=a:a.constructor!==Object&&(i=t.get(a.constructor))?s[n]=i(a,e):ArrayBuffer.isView(a)?s[n]=r(a):s[n]=e(a)}return s}:function e(o){if("object"!=typeof o||null===o)return o;if(Array.isArray(o))return n(o,e);if(o.constructor!==Object&&(i=t.get(o.constructor)))return i(o,e);let s={};for(let n in o){if(!1===Object.hasOwnProperty.call(o,n))continue;let a=o[n];"object"!=typeof a||null===a?s[n]=a:a.constructor!==Object&&(i=t.get(a.constructor))?s[n]=i(a,e):ArrayBuffer.isView(a)?s[n]=r(a):s[n]=e(a)}return s};function n(e,n){let o=Object.keys(e),s=new Array(o.length);for(let a=0;a<o.length;a++){let l=o[a],c=e[l];"object"!=typeof c||null===c?s[l]=c:c.constructor!==Object&&(i=t.get(c.constructor))?s[l]=i(c,n):ArrayBuffer.isView(c)?s[l]=r(c):s[l]=n(c)}return s}}})),Nr=S(((e,t)=>{x(),yt(),nt(),t.exports=Br()()})),Lr=S((e=>{function t(e){let t=e.split("/");for(let r=0;r<t.length;r++)if("+"!==t[r]){if("#"===t[r])return r===t.length-1;if(-1!==t[r].indexOf("+")||-1!==t[r].indexOf("#"))return!1}return!0}x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.validateTopic=t,e.validateTopics=function(e){if(0===e.length)return"empty_topic_list";for(let r=0;r<e.length;r++)if(!t(e[r]))return e[r];return null}})),Ur=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t=vr(),r={objectMode:!0},i={clean:!0};e.default=class{constructor(e){this.options=e||{},this.options=Object.assign(Object.assign({},i),e),this._inflights=new Map}put(e,t){return this._inflights.set(e.messageId,e),t&&t(),this}createStream(){let e=new t.Readable(r),i=[],n=!1,o=0;return this._inflights.forEach(((e,t)=>{i.push(e)})),e._read=()=>{!n&&o<i.length?e.push(i[o++]):e.push(null)},e.destroy=t=>{if(!n)return n=!0,setTimeout((()=>{e.emit("close")}),0),e},e}del(e,t){let r=this._inflights.get(e.messageId);return r?(this._inflights.delete(e.messageId),t(null,r)):t&&t(new Error("missing packet")),this}get(e,t){let r=this._inflights.get(e.messageId);return r?t(null,r):t&&t(new Error("missing packet")),this}close(e){this.options.clean&&(this._inflights=null),e&&e()}}})),Wr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t=[0,16,128,131,135,144,145,151,153];e.default=(e,r,i)=>{e.log("handlePublish: packet %o",r),i=typeof i<"u"?i:e.noop;let n=r.topic.toString(),o=r.payload,{qos:s}=r,{messageId:a}=r,{options:l}=e;if(5===e.options.protocolVersion){let t;if(r.properties&&(t=r.properties.topicAlias),typeof t<"u")if(0===n.length){if(!(t>0&&t<=65535))return e.log("handlePublish :: topic alias out of range. alias: %d",t),void e.emit("error",new Error("Received Topic Alias is out of range"));{let r=e.topicAliasRecv.getTopicByAlias(t);if(!r)return e.log("handlePublish :: unregistered topic alias. alias: %d",t),void e.emit("error",new Error("Received unregistered Topic Alias"));n=r,e.log("handlePublish :: topic complemented by alias. topic: %s - alias: %d",n,t)}}else{if(!e.topicAliasRecv.put(n,t))return e.log("handlePublish :: topic alias out of range. alias: %d",t),void e.emit("error",new Error("Received Topic Alias is out of range"));e.log("handlePublish :: registered topic: %s - alias: %d",n,t)}}switch(e.log("handlePublish: qos %d",s),s){case 2:l.customHandleAcks(n,o,r,((n,o)=>("number"==typeof n&&(o=n,n=null),n?e.emit("error",n):-1===t.indexOf(o)?e.emit("error",new Error("Wrong reason code for pubrec")):void(o?e._sendPacket({cmd:"pubrec",messageId:a,reasonCode:o},i):e.incomingStore.put(r,(()=>{e._sendPacket({cmd:"pubrec",messageId:a},i)}))))));break;case 1:l.customHandleAcks(n,o,r,((s,l)=>("number"==typeof s&&(l=s,s=null),s?e.emit("error",s):-1===t.indexOf(l)?e.emit("error",new Error("Wrong reason code for puback")):(l||e.emit("message",n,o,r),void e.handleMessage(r,(t=>{if(t)return i&&i(t);e._sendPacket({cmd:"puback",messageId:a,reasonCode:l},i)}))))));break;case 0:e.emit("message",n,o,r),e.handleMessage(r,i);break;default:e.log("handlePublish: unknown QoS. Doing nothing.")}}})),Dr=S(((e,t)=>{t.exports={version:"5.13.0"}})),Fr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.MQTTJS_VERSION=e.nextTick=e.ErrorWithSubackPacket=e.ErrorWithReasonCode=void 0,e.applyMixin=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var i;let n=[t];for(;;){let e=n[0],t=Object.getPrototypeOf(e);if(null===t||void 0===t||!t.prototype)break;n.unshift(t)}for(let o of n)for(let t of Object.getOwnPropertyNames(o.prototype))(r||"constructor"!==t)&&Object.defineProperty(e.prototype,t,null!==(i=Object.getOwnPropertyDescriptor(o.prototype,t))&&void 0!==i?i:Object.create(null))};var t=class e extends Error{constructor(t,r){super(t),this.code=r,Object.setPrototypeOf(this,e.prototype),Object.getPrototypeOf(this).name="ErrorWithReasonCode"}};e.ErrorWithReasonCode=t;var r=class e extends Error{constructor(t,r){super(t),this.packet=r,Object.setPrototypeOf(this,e.prototype),Object.getPrototypeOf(this).name="ErrorWithSubackPacket"}};e.ErrorWithSubackPacket=r,e.nextTick="function"==typeof(null===k||void 0===k?void 0:k.nextTick)?k.nextTick:e=>{setTimeout(e,0)},e.MQTTJS_VERSION=Dr().version})),qr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.ReasonCodes=void 0;var t=Fr();e.ReasonCodes={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};e.default=(r,i)=>{let{messageId:n}=i,o=i.cmd,s=null,a=r.outgoing[n]?r.outgoing[n].cb:null,l=null;if(a){switch(r.log("_handleAck :: packet type",o),o){case"pubcomp":case"puback":{let o=i.reasonCode;o&&o>0&&16!==o?(l=new t.ErrorWithReasonCode("Publish error: ".concat(e.ReasonCodes[o]),o),r._removeOutgoingAndStoreMessage(n,(()=>{a(l,i)}))):r._removeOutgoingAndStoreMessage(n,a);break}case"pubrec":{s={cmd:"pubrel",qos:2,messageId:n};let o=i.reasonCode;o&&o>0&&16!==o?(l=new t.ErrorWithReasonCode("Publish error: ".concat(e.ReasonCodes[o]),o),r._removeOutgoingAndStoreMessage(n,(()=>{a(l,i)}))):r._sendPacket(s);break}case"suback":{delete r.outgoing[n],r.messageIdProvider.deallocate(n);let t=i.granted;for(let i=0;i<t.length;i++){let o=t[i];if(0!==(128&o)){l=new Error("Subscribe error: ".concat(e.ReasonCodes[o])),l.code=o;let t=r.messageIdToTopic[n];t&&t.forEach((e=>{delete r._resubscribeTopics[e]}))}}delete r.messageIdToTopic[n],r._invokeStoreProcessingQueue(),a(l,i);break}case"unsuback":delete r.outgoing[n],r.messageIdProvider.deallocate(n),r._invokeStoreProcessingQueue(),a(null,i);break;default:r.emit("error",new Error("unrecognized packet type"))}r.disconnecting&&0===Object.keys(r.outgoing).length&&r.emit("outgoingEmpty")}else r.log("_handleAck :: Server sent an ack in error. Ignoring.")}})),Hr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t=Fr(),r=qr();e.default=(e,i)=>{let{options:n}=e,o=n.protocolVersion,s=5===o?i.reasonCode:i.returnCode;if(5===o)e.handleAuth(i,((i,n)=>{if(i)e.emit("error",i);else if(24===s)e.reconnecting=!1,e._sendPacket(n);else{let i=new t.ErrorWithReasonCode("Connection refused: ".concat(r.ReasonCodes[s]),s);e.emit("error",i)}}));else{let r=new t.ErrorWithReasonCode("Protocol error: Auth packets are only supported in MQTT 5. Your version:".concat(o),s);e.emit("error",r)}}})),Vr=S((e=>{var t,r,i,p,b,g,m,v,w,_,O,j,S,E,A,I,T,C,R,P,M,B,N,L,U,W,D,F,q,H,V,z,G,K,Q,Y,J,X,$,Z,ee,te,re,ie;x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.LRUCache=void 0;var ne="object"==typeof performance&&performance&&"function"==typeof performance.now?performance:Date,oe=new Set,se="object"==typeof k&&k?k:{},ae=(e,t,r,i)=>{"function"==typeof se.emitWarning?se.emitWarning(e,t,r,i):console.error("[".concat(r,"] ").concat(t,": ").concat(e))},le=globalThis.AbortController,ce=globalThis.AbortSignal;if(typeof le>"u"){var ue;ce=class{constructor(){Object(d.a)(this,"onabort",void 0),Object(d.a)(this,"_onabort",[]),Object(d.a)(this,"reason",void 0),Object(d.a)(this,"aborted",!1)}addEventListener(e,t){this._onabort.push(t)}},le=class{constructor(){Object(d.a)(this,"signal",new ce),t()}abort(e){if(!this.signal.aborted){var t,r;this.signal.reason=e,this.signal.aborted=!0;for(let t of this.signal._onabort)t(e);null===(t=(r=this.signal).onabort)||void 0===t||t.call(r,e)}}};let e="1"!==(null===(ue=se.env)||void 0===ue?void 0:ue.LRU_CACHE_IGNORE_AC_WARNING),t=()=>{e&&(e=!1,ae("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}Symbol("type");var he=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),de=e=>he(e)?e<=Math.pow(2,8)?Uint8Array:e<=Math.pow(2,16)?Uint16Array:e<=Math.pow(2,32)?Uint32Array:e<=Number.MAX_SAFE_INTEGER?fe:null:null,fe=class extends Array{constructor(e){super(e),this.fill(0)}},pe=(t=class e{static create(i){let n=de(i);if(!n)return[];Object(h.a)(e,t,r,!0);let o=new e(i,n);return Object(h.a)(e,t,r,!1),o}constructor(i,n){if(Object(d.a)(this,"heap",void 0),Object(d.a)(this,"length",void 0),!Object(u.a)(e,t,r))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(i),this.length=0}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}},r={writable:!0,value:!1},t),be=(i=new WeakMap,p=new WeakMap,b=new WeakMap,g=new WeakMap,m=new WeakMap,v=new WeakMap,w=new WeakMap,_=new WeakMap,O=new WeakMap,j=new WeakMap,S=new WeakMap,E=new WeakMap,A=new WeakMap,I=new WeakMap,T=new WeakMap,C=new WeakMap,R=new WeakMap,P=new WeakMap,M=new WeakMap,B=new WeakMap,N=new WeakMap,L=new WeakMap,U=new WeakMap,W=new WeakSet,D=new WeakMap,F=new WeakMap,q=new WeakMap,H=new WeakMap,V=new WeakSet,z=new WeakMap,G=new WeakMap,K=new WeakMap,Q=new WeakSet,Y=new WeakSet,J=new WeakSet,X=new WeakSet,$=new WeakSet,Z=new WeakSet,ee=new WeakSet,te=new WeakSet,re=new WeakSet,ie=new WeakSet,class e{static unsafeExposeInternals(e){return{starts:Object(c.a)(e,M),ttls:Object(c.a)(e,B),sizes:Object(c.a)(e,P),keyMap:Object(c.a)(e,O),keyList:Object(c.a)(e,j),valList:Object(c.a)(e,S),next:Object(c.a)(e,E),prev:Object(c.a)(e,A),get head(){return Object(c.a)(e,I)},get tail(){return Object(c.a)(e,T)},free:Object(c.a)(e,C),isBackgroundFetch:t=>Object(l.a)(e,Z,je).call(e,t),backgroundFetch:(t,r,i,n)=>Object(l.a)(e,$,Oe).call(e,t,r,i,n),moveToTail:t=>Object(l.a)(e,te,Ee).call(e,t),indexes:t=>Object(l.a)(e,Q,ye).call(e,t),rindexes:t=>Object(l.a)(e,Y,ve).call(e,t),isStale:t=>Object(c.a)(e,H).call(e,t)}}get max(){return Object(c.a)(this,i)}get maxSize(){return Object(c.a)(this,p)}get calculatedSize(){return Object(c.a)(this,_)}get size(){return Object(c.a)(this,w)}get fetchMethod(){return Object(c.a)(this,m)}get memoMethod(){return Object(c.a)(this,v)}get dispose(){return Object(c.a)(this,b)}get disposeAfter(){return Object(c.a)(this,g)}constructor(t){Object(o.a)(this,ie),Object(o.a)(this,re),Object(o.a)(this,te),Object(o.a)(this,ee),Object(o.a)(this,Z),Object(o.a)(this,$),Object(o.a)(this,X),Object(o.a)(this,J),Object(o.a)(this,Y),Object(o.a)(this,Q),Object(o.a)(this,V),Object(o.a)(this,W),Object(s.a)(this,i,{writable:!0,value:void 0}),Object(s.a)(this,p,{writable:!0,value:void 0}),Object(s.a)(this,b,{writable:!0,value:void 0}),Object(s.a)(this,g,{writable:!0,value:void 0}),Object(s.a)(this,m,{writable:!0,value:void 0}),Object(s.a)(this,v,{writable:!0,value:void 0}),Object(d.a)(this,"ttl",void 0),Object(d.a)(this,"ttlResolution",void 0),Object(d.a)(this,"ttlAutopurge",void 0),Object(d.a)(this,"updateAgeOnGet",void 0),Object(d.a)(this,"updateAgeOnHas",void 0),Object(d.a)(this,"allowStale",void 0),Object(d.a)(this,"noDisposeOnSet",void 0),Object(d.a)(this,"noUpdateTTL",void 0),Object(d.a)(this,"maxEntrySize",void 0),Object(d.a)(this,"sizeCalculation",void 0),Object(d.a)(this,"noDeleteOnFetchRejection",void 0),Object(d.a)(this,"noDeleteOnStaleGet",void 0),Object(d.a)(this,"allowStaleOnFetchAbort",void 0),Object(d.a)(this,"allowStaleOnFetchRejection",void 0),Object(d.a)(this,"ignoreFetchAbort",void 0),Object(s.a)(this,w,{writable:!0,value:void 0}),Object(s.a)(this,_,{writable:!0,value:void 0}),Object(s.a)(this,O,{writable:!0,value:void 0}),Object(s.a)(this,j,{writable:!0,value:void 0}),Object(s.a)(this,S,{writable:!0,value:void 0}),Object(s.a)(this,E,{writable:!0,value:void 0}),Object(s.a)(this,A,{writable:!0,value:void 0}),Object(s.a)(this,I,{writable:!0,value:void 0}),Object(s.a)(this,T,{writable:!0,value:void 0}),Object(s.a)(this,C,{writable:!0,value:void 0}),Object(s.a)(this,R,{writable:!0,value:void 0}),Object(s.a)(this,P,{writable:!0,value:void 0}),Object(s.a)(this,M,{writable:!0,value:void 0}),Object(s.a)(this,B,{writable:!0,value:void 0}),Object(s.a)(this,N,{writable:!0,value:void 0}),Object(s.a)(this,L,{writable:!0,value:void 0}),Object(s.a)(this,U,{writable:!0,value:void 0}),Object(s.a)(this,D,{writable:!0,value:()=>{}}),Object(s.a)(this,F,{writable:!0,value:()=>{}}),Object(s.a)(this,q,{writable:!0,value:()=>{}}),Object(s.a)(this,H,{writable:!0,value:()=>!1}),Object(s.a)(this,z,{writable:!0,value:e=>{}}),Object(s.a)(this,G,{writable:!0,value:(e,t,r)=>{}}),Object(s.a)(this,K,{writable:!0,value:(e,t,r,i)=>{if(r||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}}),Object(d.a)(this,Symbol.toStringTag,"LRUCache");let{max:r=0,ttl:n,ttlResolution:u=1,ttlAutopurge:h,updateAgeOnGet:f,updateAgeOnHas:y,allowStale:x,dispose:k,disposeAfter:ne,noDisposeOnSet:se,noUpdateTTL:le,maxSize:ce=0,maxEntrySize:ue=0,sizeCalculation:fe,fetchMethod:be,memoMethod:ye,noDeleteOnFetchRejection:ve,noDeleteOnStaleGet:we,allowStaleOnFetchRejection:_e,allowStaleOnFetchAbort:Oe,ignoreFetchAbort:je}=t;if(0!==r&&!he(r))throw new TypeError("max option must be a nonnegative integer");let Se=r?de(r):Array;if(!Se)throw new Error("invalid max value: "+r);if(Object(a.a)(this,i,r),Object(a.a)(this,p,ce),this.maxEntrySize=ue||Object(c.a)(this,p),this.sizeCalculation=fe,this.sizeCalculation){if(!Object(c.a)(this,p)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if("function"!=typeof this.sizeCalculation)throw new TypeError("sizeCalculation set to non-function")}if(void 0!==ye&&"function"!=typeof ye)throw new TypeError("memoMethod must be a function if defined");if(Object(a.a)(this,v,ye),void 0!==be&&"function"!=typeof be)throw new TypeError("fetchMethod must be a function if specified");if(Object(a.a)(this,m,be),Object(a.a)(this,L,!!be),Object(a.a)(this,O,new Map),Object(a.a)(this,j,new Array(r).fill(void 0)),Object(a.a)(this,S,new Array(r).fill(void 0)),Object(a.a)(this,E,new Se(r)),Object(a.a)(this,A,new Se(r)),Object(a.a)(this,I,0),Object(a.a)(this,T,0),Object(a.a)(this,C,pe.create(r)),Object(a.a)(this,w,0),Object(a.a)(this,_,0),"function"==typeof k&&Object(a.a)(this,b,k),"function"==typeof ne?(Object(a.a)(this,g,ne),Object(a.a)(this,R,[])):(Object(a.a)(this,g,void 0),Object(a.a)(this,R,void 0)),Object(a.a)(this,N,!!Object(c.a)(this,b)),Object(a.a)(this,U,!!Object(c.a)(this,g)),this.noDisposeOnSet=!!se,this.noUpdateTTL=!!le,this.noDeleteOnFetchRejection=!!ve,this.allowStaleOnFetchRejection=!!_e,this.allowStaleOnFetchAbort=!!Oe,this.ignoreFetchAbort=!!je,0!==this.maxEntrySize){if(0!==Object(c.a)(this,p)&&!he(Object(c.a)(this,p)))throw new TypeError("maxSize must be a positive integer if specified");if(!he(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");Object(l.a)(this,V,me).call(this)}if(this.allowStale=!!x,this.noDeleteOnStaleGet=!!we,this.updateAgeOnGet=!!f,this.updateAgeOnHas=!!y,this.ttlResolution=he(u)||0===u?u:1,this.ttlAutopurge=!!h,this.ttl=n||0,this.ttl){if(!he(this.ttl))throw new TypeError("ttl must be a positive integer if specified");Object(l.a)(this,W,ge).call(this)}if(0===Object(c.a)(this,i)&&0===this.ttl&&0===Object(c.a)(this,p))throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!Object(c.a)(this,i)&&!Object(c.a)(this,p)){let t="LRU_CACHE_UNBOUNDED";(e=>!oe.has(e))(t)&&(oe.add(t),ae("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",t,e))}}getRemainingTTL(e){return Object(c.a)(this,O).has(e)?1/0:0}*entries(){for(let e of Object(l.a)(this,Q,ye).call(this))void 0!==Object(c.a)(this,S)[e]&&void 0!==Object(c.a)(this,j)[e]&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield[Object(c.a)(this,j)[e],Object(c.a)(this,S)[e]])}*rentries(){for(let e of Object(l.a)(this,Y,ve).call(this))void 0!==Object(c.a)(this,S)[e]&&void 0!==Object(c.a)(this,j)[e]&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield[Object(c.a)(this,j)[e],Object(c.a)(this,S)[e]])}*keys(){for(let e of Object(l.a)(this,Q,ye).call(this)){let t=Object(c.a)(this,j)[e];void 0!==t&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield t)}}*rkeys(){for(let e of Object(l.a)(this,Y,ve).call(this)){let t=Object(c.a)(this,j)[e];void 0!==t&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield t)}}*values(){for(let e of Object(l.a)(this,Q,ye).call(this))void 0!==Object(c.a)(this,S)[e]&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield Object(c.a)(this,S)[e])}*rvalues(){for(let e of Object(l.a)(this,Y,ve).call(this))void 0!==Object(c.a)(this,S)[e]&&!Object(l.a)(this,Z,je).call(this,Object(c.a)(this,S)[e])&&(yield Object(c.a)(this,S)[e])}[Symbol.iterator](){return this.entries()}find(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(let r of Object(l.a)(this,Q,ye).call(this)){let i=Object(c.a)(this,S)[r],n=Object(l.a)(this,Z,je).call(this,i)?i.__staleWhileFetching:i;if(void 0!==n&&e(n,Object(c.a)(this,j)[r],this))return this.get(Object(c.a)(this,j)[r],t)}}forEach(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;for(let r of Object(l.a)(this,Q,ye).call(this)){let i=Object(c.a)(this,S)[r],n=Object(l.a)(this,Z,je).call(this,i)?i.__staleWhileFetching:i;void 0!==n&&e.call(t,n,Object(c.a)(this,j)[r],this)}}rforEach(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;for(let r of Object(l.a)(this,Y,ve).call(this)){let i=Object(c.a)(this,S)[r],n=Object(l.a)(this,Z,je).call(this,i)?i.__staleWhileFetching:i;void 0!==n&&e.call(t,n,Object(c.a)(this,j)[r],this)}}purgeStale(){let e=!1;for(let t of Object(l.a)(this,Y,ve).call(this,{allowStale:!0}))Object(c.a)(this,H).call(this,t)&&(Object(l.a)(this,re,Ae).call(this,Object(c.a)(this,j)[t],"expire"),e=!0);return e}info(e){let t=Object(c.a)(this,O).get(e);if(void 0===t)return;let r=Object(c.a)(this,S)[t],i=Object(l.a)(this,Z,je).call(this,r)?r.__staleWhileFetching:r;if(void 0===i)return;let n={value:i};if(Object(c.a)(this,B)&&Object(c.a)(this,M)){let e=Object(c.a)(this,B)[t],r=Object(c.a)(this,M)[t];if(e&&r){let t=e-(ne.now()-r);n.ttl=t,n.start=Date.now()}}return Object(c.a)(this,P)&&(n.size=Object(c.a)(this,P)[t]),n}dump(){let e=[];for(let t of Object(l.a)(this,Q,ye).call(this,{allowStale:!0})){let r=Object(c.a)(this,j)[t],i=Object(c.a)(this,S)[t],n=Object(l.a)(this,Z,je).call(this,i)?i.__staleWhileFetching:i;if(void 0===n||void 0===r)continue;let o={value:n};if(Object(c.a)(this,B)&&Object(c.a)(this,M)){o.ttl=Object(c.a)(this,B)[t];let e=ne.now()-Object(c.a)(this,M)[t];o.start=Math.floor(Date.now()-e)}Object(c.a)(this,P)&&(o.size=Object(c.a)(this,P)[t]),e.unshift([r,o])}return e}load(e){this.clear();for(let[t,r]of e){if(r.start){let e=Date.now()-r.start;r.start=ne.now()-e}this.set(t,r.value,r)}}set(e,t){var r;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(void 0===t)return this.delete(e),this;let{ttl:o=this.ttl,start:s,noDisposeOnSet:u=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:d}=n,{noUpdateTTL:f=this.noUpdateTTL}=n,p=Object(c.a)(this,K).call(this,e,t,n.size||0,h);if(this.maxEntrySize&&p>this.maxEntrySize)return d&&(d.set="miss",d.maxEntrySizeExceeded=!0),Object(l.a)(this,re,Ae).call(this,e,"set"),this;let m=0===Object(c.a)(this,w)?void 0:Object(c.a)(this,O).get(e);if(void 0===m)m=0===Object(c.a)(this,w)?Object(c.a)(this,T):0!==Object(c.a)(this,C).length?Object(c.a)(this,C).pop():Object(c.a)(this,w)===Object(c.a)(this,i)?Object(l.a)(this,X,_e).call(this,!1):Object(c.a)(this,w),Object(c.a)(this,j)[m]=e,Object(c.a)(this,S)[m]=t,Object(c.a)(this,O).set(e,m),Object(c.a)(this,E)[Object(c.a)(this,T)]=m,Object(c.a)(this,A)[m]=Object(c.a)(this,T),Object(a.a)(this,T,m),Object(a.a)(this,w,(r=Object(c.a)(this,w),r++,r)),Object(c.a)(this,G).call(this,m,p,d),d&&(d.set="add"),f=!1;else{Object(l.a)(this,te,Ee).call(this,m);let r=Object(c.a)(this,S)[m];if(t!==r){var y,v;if(Object(c.a)(this,L)&&Object(l.a)(this,Z,je).call(this,r)){var _,x;r.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:t}=r;void 0!==t&&!u&&(Object(c.a)(this,N)&&null!==(_=Object(c.a)(this,b))&&void 0!==_&&_.call(this,t,e,"set"),Object(c.a)(this,U)&&(null===(x=Object(c.a)(this,R))||void 0===x||x.push([t,e,"set"])))}else u||(Object(c.a)(this,N)&&null!==(y=Object(c.a)(this,b))&&void 0!==y&&y.call(this,r,e,"set"),Object(c.a)(this,U)&&(null===(v=Object(c.a)(this,R))||void 0===v||v.push([r,e,"set"])));if(Object(c.a)(this,z).call(this,m),Object(c.a)(this,G).call(this,m,p,d),Object(c.a)(this,S)[m]=t,d){d.set="replace";let e=r&&Object(l.a)(this,Z,je).call(this,r)?r.__staleWhileFetching:r;void 0!==e&&(d.oldValue=e)}}else d&&(d.set="update")}if(0!==o&&!Object(c.a)(this,B)&&Object(l.a)(this,W,ge).call(this),Object(c.a)(this,B)&&(f||Object(c.a)(this,q).call(this,m,o,s),d&&Object(c.a)(this,F).call(this,d,m)),!u&&Object(c.a)(this,U)&&Object(c.a)(this,R)){let e,t=Object(c.a)(this,R);for(;e=null===t||void 0===t?void 0:t.shift();){var k;null===(k=Object(c.a)(this,g))||void 0===k||k.call(this,...e)}}return this}pop(){try{for(;Object(c.a)(this,w);){let e=Object(c.a)(this,S)[Object(c.a)(this,I)];if(Object(l.a)(this,X,_e).call(this,!0),Object(l.a)(this,Z,je).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(void 0!==e)return e}}finally{if(Object(c.a)(this,U)&&Object(c.a)(this,R)){let t,r=Object(c.a)(this,R);for(;t=null===r||void 0===r?void 0:r.shift();){var e;null===(e=Object(c.a)(this,g))||void 0===e||e.call(this,...t)}}}}has(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{updateAgeOnHas:r=this.updateAgeOnHas,status:i}=t,n=Object(c.a)(this,O).get(e);if(void 0!==n){let e=Object(c.a)(this,S)[n];if(Object(l.a)(this,Z,je).call(this,e)&&void 0===e.__staleWhileFetching)return!1;if(!Object(c.a)(this,H).call(this,n))return r&&Object(c.a)(this,D).call(this,n),i&&(i.has="hit",Object(c.a)(this,F).call(this,i,n)),!0;i&&(i.has="stale",Object(c.a)(this,F).call(this,i,n))}else i&&(i.has="miss");return!1}peek(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{allowStale:r=this.allowStale}=t,i=Object(c.a)(this,O).get(e);if(void 0===i||!r&&Object(c.a)(this,H).call(this,i))return;let n=Object(c.a)(this,S)[i];return Object(l.a)(this,Z,je).call(this,n)?n.__staleWhileFetching:n}async fetch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{allowStale:r=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:s=this.noDisposeOnSet,size:a=0,sizeCalculation:u=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:d=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:p=this.ignoreFetchAbort,allowStaleOnFetchAbort:b=this.allowStaleOnFetchAbort,context:g,forceRefresh:m=!1,status:y,signal:v}=t;if(!Object(c.a)(this,L))return y&&(y.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:i,noDeleteOnStaleGet:n,status:y});let w={allowStale:r,updateAgeOnGet:i,noDeleteOnStaleGet:n,ttl:o,noDisposeOnSet:s,size:a,sizeCalculation:u,noUpdateTTL:h,noDeleteOnFetchRejection:d,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:b,ignoreFetchAbort:p,status:y,signal:v},_=Object(c.a)(this,O).get(e);if(void 0===_){y&&(y.fetch="miss");let t=Object(l.a)(this,$,Oe).call(this,e,_,w,g);return t.__returned=t}{let t=Object(c.a)(this,S)[_];if(Object(l.a)(this,Z,je).call(this,t)){let e=r&&void 0!==t.__staleWhileFetching;return y&&(y.fetch="inflight",e&&(y.returnedStale=!0)),e?t.__staleWhileFetching:t.__returned=t}let n=Object(c.a)(this,H).call(this,_);if(!m&&!n)return y&&(y.fetch="hit"),Object(l.a)(this,te,Ee).call(this,_),i&&Object(c.a)(this,D).call(this,_),y&&Object(c.a)(this,F).call(this,y,_),t;let o=Object(l.a)(this,$,Oe).call(this,e,_,w,g),s=void 0!==o.__staleWhileFetching&&r;return y&&(y.fetch=n?"stale":"refresh",s&&n&&(y.returnedStale=!0)),s?o.__staleWhileFetching:o.__returned=o}}async forceFetch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=await this.fetch(e,t);if(void 0===r)throw new Error("fetch() returned undefined");return r}memo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Object(c.a)(this,v);if(!r)throw new Error("no memoMethod provided to constructor");let{context:i,forceRefresh:o}=t,s=Object(n.a)(t,y),a=this.get(e,s);if(!o&&void 0!==a)return a;let l=r(e,a,{options:s,context:i});return this.set(e,l,s),l}get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{allowStale:r=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:o}=t,s=Object(c.a)(this,O).get(e);if(void 0!==s){let t=Object(c.a)(this,S)[s],a=Object(l.a)(this,Z,je).call(this,t);return o&&Object(c.a)(this,F).call(this,o,s),Object(c.a)(this,H).call(this,s)?(o&&(o.get="stale"),a?(o&&r&&void 0!==t.__staleWhileFetching&&(o.returnedStale=!0),r?t.__staleWhileFetching:void 0):(n||Object(l.a)(this,re,Ae).call(this,e,"expire"),o&&r&&(o.returnedStale=!0),r?t:void 0)):(o&&(o.get="hit"),a?t.__staleWhileFetching:(Object(l.a)(this,te,Ee).call(this,s),i&&Object(c.a)(this,D).call(this,s),t))}o&&(o.get="miss")}delete(e){return Object(l.a)(this,re,Ae).call(this,e,"delete")}clear(){return Object(l.a)(this,ie,xe).call(this,"delete")}});function ge(){var e=this;let t=new fe(Object(c.a)(this,i)),r=new fe(Object(c.a)(this,i));Object(a.a)(this,B,t),Object(a.a)(this,M,r),Object(a.a)(this,q,(function(i,n){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ne.now();if(r[i]=0!==n?o:0,t[i]=n,0!==n&&e.ttlAutopurge){let t=setTimeout((()=>{Object(c.a)(e,H).call(e,i)&&Object(l.a)(e,re,Ae).call(e,Object(c.a)(e,j)[i],"expire")}),n+1);t.unref&&t.unref()}})),Object(a.a)(this,D,(e=>{r[e]=0!==t[e]?ne.now():0})),Object(a.a)(this,F,((e,i)=>{if(t[i]){let s=t[i],a=r[i];if(!s||!a)return;e.ttl=s,e.start=a,e.now=n||o();let l=e.now-a;e.remainingTTL=s-l}}));let n=0,o=()=>{let e=ne.now();if(this.ttlResolution>0){n=e;let t=setTimeout((()=>n=0),this.ttlResolution);t.unref&&t.unref()}return e};this.getRemainingTTL=e=>{let i=Object(c.a)(this,O).get(e);if(void 0===i)return 0;let s=t[i],a=r[i];return s&&a?s-((n||o())-a):1/0},Object(a.a)(this,H,(e=>{let i=r[e],s=t[e];return!!s&&!!i&&(n||o())-i>s}))}function me(){let e=new fe(Object(c.a)(this,i));Object(a.a)(this,_,0),Object(a.a)(this,P,e),Object(a.a)(this,z,(t=>{Object(a.a)(this,_,Object(c.a)(this,_)-e[t]),e[t]=0})),Object(a.a)(this,K,((e,t,r,i)=>{if(Object(l.a)(this,Z,je).call(this,t))return 0;if(!he(r)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if("function"!=typeof i)throw new TypeError("sizeCalculation must be a function");if(r=i(t,e),!he(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return r})),Object(a.a)(this,G,((t,r,i)=>{if(e[t]=r,Object(c.a)(this,p)){let r=Object(c.a)(this,p)-e[t];for(;Object(c.a)(this,_)>r;)Object(l.a)(this,X,_e).call(this,!0)}Object(a.a)(this,_,Object(c.a)(this,_)+e[t]),i&&(i.entrySize=r,i.totalCalculatedSize=Object(c.a)(this,_))}))}function ye(){var e=this;let{allowStale:t=this.allowStale}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function*(){if(Object(c.a)(e,w))for(let r=Object(c.a)(e,T);Object(l.a)(e,J,we).call(e,r)&&((t||!Object(c.a)(e,H).call(e,r))&&(yield r),r!==Object(c.a)(e,I));)r=Object(c.a)(e,A)[r]}()}function ve(){var e=this;let{allowStale:t=this.allowStale}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function*(){if(Object(c.a)(e,w))for(let r=Object(c.a)(e,I);Object(l.a)(e,J,we).call(e,r)&&((t||!Object(c.a)(e,H).call(e,r))&&(yield r),r!==Object(c.a)(e,T));)r=Object(c.a)(e,E)[r]}()}function we(e){return void 0!==e&&Object(c.a)(this,O).get(Object(c.a)(this,j)[e])===e}function _e(e){var t,r,i;let n=Object(c.a)(this,I),o=Object(c.a)(this,j)[n],s=Object(c.a)(this,S)[n];return Object(c.a)(this,L)&&Object(l.a)(this,Z,je).call(this,s)?s.__abortController.abort(new Error("evicted")):(Object(c.a)(this,N)||Object(c.a)(this,U))&&(Object(c.a)(this,N)&&null!==(t=Object(c.a)(this,b))&&void 0!==t&&t.call(this,s,o,"evict"),Object(c.a)(this,U)&&(null===(r=Object(c.a)(this,R))||void 0===r||r.push([s,o,"evict"]))),Object(c.a)(this,z).call(this,n),e&&(Object(c.a)(this,j)[n]=void 0,Object(c.a)(this,S)[n]=void 0,Object(c.a)(this,C).push(n)),1===Object(c.a)(this,w)?(Object(a.a)(this,I,Object(a.a)(this,T,0)),Object(c.a)(this,C).length=0):Object(a.a)(this,I,Object(c.a)(this,E)[n]),Object(c.a)(this,O).delete(o),Object(a.a)(this,w,(i=Object(c.a)(this,w),i--,i)),n}function Oe(e,t,r,i){var n=this;let o=void 0===t?void 0:Object(c.a)(this,S)[t];if(Object(l.a)(this,Z,je).call(this,o))return o;let s=new le,{signal:a}=r;null===a||void 0===a||a.addEventListener("abort",(()=>s.abort(a.reason)),{signal:s.signal});let u={signal:s.signal,options:r,context:i},h=function(i){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{aborted:a}=s.signal,h=r.ignoreFetchAbort&&void 0!==i;if(r.status&&(a&&!o?(r.status.fetchAborted=!0,r.status.fetchError=s.signal.reason,h&&(r.status.fetchAbortIgnored=!0)):r.status.fetchResolved=!0),a&&!h&&!o)return d(s.signal.reason);let f=p;return Object(c.a)(n,S)[t]===p&&(void 0===i?f.__staleWhileFetching?Object(c.a)(n,S)[t]=f.__staleWhileFetching:Object(l.a)(n,re,Ae).call(n,e,"fetch"):(r.status&&(r.status.fetchUpdated=!0),n.set(e,i,u.options))),i},d=i=>{let{aborted:n}=s.signal,o=n&&r.allowStaleOnFetchAbort,a=o||r.allowStaleOnFetchRejection,u=a||r.noDeleteOnFetchRejection,h=p;if(Object(c.a)(this,S)[t]===p&&(u&&void 0!==h.__staleWhileFetching?o||(Object(c.a)(this,S)[t]=h.__staleWhileFetching):Object(l.a)(this,re,Ae).call(this,e,"fetch")),a)return r.status&&void 0!==h.__staleWhileFetching&&(r.status.returnedStale=!0),h.__staleWhileFetching;if(h.__returned===h)throw i};r.status&&(r.status.fetchDispatched=!0);let p=new Promise(((t,i)=>{var n;let a=null===(n=Object(c.a)(this,m))||void 0===n?void 0:n.call(this,e,o,u);a&&a instanceof Promise&&a.then((e=>t(void 0===e?void 0:e)),i),s.signal.addEventListener("abort",(()=>{(!r.ignoreFetchAbort||r.allowStaleOnFetchAbort)&&(t(void 0),r.allowStaleOnFetchAbort&&(t=e=>h(e,!0)))}))})).then(h,(e=>(r.status&&(r.status.fetchRejected=!0,r.status.fetchError=e),d(e)))),b=Object.assign(p,{__abortController:s,__staleWhileFetching:o,__returned:void 0});return void 0===t?(this.set(e,b,Object(f.a)(Object(f.a)({},u.options),{},{status:void 0})),t=Object(c.a)(this,O).get(e)):Object(c.a)(this,S)[t]=b,b}function je(e){if(!Object(c.a)(this,L))return!1;let t=e;return!!t&&t instanceof Promise&&t.hasOwnProperty("__staleWhileFetching")&&t.__abortController instanceof le}function Se(e,t){Object(c.a)(this,A)[t]=e,Object(c.a)(this,E)[e]=t}function Ee(e){e!==Object(c.a)(this,T)&&(e===Object(c.a)(this,I)?Object(a.a)(this,I,Object(c.a)(this,E)[e]):Object(l.a)(this,ee,Se).call(this,Object(c.a)(this,A)[e],Object(c.a)(this,E)[e]),Object(l.a)(this,ee,Se).call(this,Object(c.a)(this,T),e),Object(a.a)(this,T,e))}function Ae(e,t){var r;let i=!1;if(0!==Object(c.a)(this,w)){let r=Object(c.a)(this,O).get(e);if(void 0!==r)if(i=!0,1===Object(c.a)(this,w))Object(l.a)(this,ie,xe).call(this,t);else{var n,o,s;Object(c.a)(this,z).call(this,r);let i=Object(c.a)(this,S)[r];if(Object(l.a)(this,Z,je).call(this,i)?i.__abortController.abort(new Error("deleted")):(Object(c.a)(this,N)||Object(c.a)(this,U))&&(Object(c.a)(this,N)&&null!==(o=Object(c.a)(this,b))&&void 0!==o&&o.call(this,i,e,t),Object(c.a)(this,U)&&(null===(s=Object(c.a)(this,R))||void 0===s||s.push([i,e,t]))),Object(c.a)(this,O).delete(e),Object(c.a)(this,j)[r]=void 0,Object(c.a)(this,S)[r]=void 0,r===Object(c.a)(this,T))Object(a.a)(this,T,Object(c.a)(this,A)[r]);else if(r===Object(c.a)(this,I))Object(a.a)(this,I,Object(c.a)(this,E)[r]);else{let e=Object(c.a)(this,A)[r];Object(c.a)(this,E)[e]=Object(c.a)(this,E)[r];let t=Object(c.a)(this,E)[r];Object(c.a)(this,A)[t]=Object(c.a)(this,A)[r]}Object(a.a)(this,w,(n=Object(c.a)(this,w),n--,n)),Object(c.a)(this,C).push(r)}}if(Object(c.a)(this,U)&&null!==(r=Object(c.a)(this,R))&&void 0!==r&&r.length){let e,t=Object(c.a)(this,R);for(;e=null===t||void 0===t?void 0:t.shift();){var u;null===(u=Object(c.a)(this,g))||void 0===u||u.call(this,...e)}}return i}function xe(e){for(let n of Object(l.a)(this,Y,ve).call(this,{allowStale:!0})){let i=Object(c.a)(this,S)[n];if(Object(l.a)(this,Z,je).call(this,i))i.__abortController.abort(new Error("deleted"));else{var t,r;let o=Object(c.a)(this,j)[n];Object(c.a)(this,N)&&null!==(t=Object(c.a)(this,b))&&void 0!==t&&t.call(this,i,o,e),Object(c.a)(this,U)&&(null===(r=Object(c.a)(this,R))||void 0===r||r.push([i,o,e]))}}if(Object(c.a)(this,O).clear(),Object(c.a)(this,S).fill(void 0),Object(c.a)(this,j).fill(void 0),Object(c.a)(this,B)&&Object(c.a)(this,M)&&(Object(c.a)(this,B).fill(0),Object(c.a)(this,M).fill(0)),Object(c.a)(this,P)&&Object(c.a)(this,P).fill(0),Object(a.a)(this,I,0),Object(a.a)(this,T,0),Object(c.a)(this,C).length=0,Object(a.a)(this,_,0),Object(a.a)(this,w,0),Object(c.a)(this,U)&&Object(c.a)(this,R)){let e,t=Object(c.a)(this,R);for(;e=null===t||void 0===t?void 0:t.shift();){var i;null===(i=Object(c.a)(this,g))||void 0===i||i.call(this,...e)}}}e.LRUCache=be})),zr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.ContainerIterator=e.Container=e.Base=void 0;e.ContainerIterator=class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.iteratorType=e}equals(e){return this.o===e.o}};var t=class{constructor(){this.i=0}get length(){return this.i}size(){return this.i}empty(){return 0===this.i}};e.Base=t;e.Container=class extends t{}})),Gr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=zr(),r=class extends t.Base{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super(),this.S=[];let t=this;e.forEach((function(e){t.push(e)}))}clear(){this.i=0,this.S=[]}push(e){return this.S.push(e),this.i+=1,this.i}pop(){if(0!==this.i)return this.i-=1,this.S.pop()}top(){return this.S[this.i-1]}},i=r;e.default=i})),Kr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=zr(),r=class extends t.Base{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super(),this.j=0,this.q=[];let t=this;e.forEach((function(e){t.push(e)}))}clear(){this.q=[],this.i=this.j=0}push(e){let t=this.q.length;if(this.j/t>.5&&this.j+this.i>=t&&t>4096){let t=this.i;for(let e=0;e<t;++e)this.q[e]=this.q[this.j+e];this.j=0,this.q[this.i]=e}else this.q[this.j+this.i]=e;return++this.i}pop(){if(0===this.i)return;let e=this.q[this.j++];return this.i-=1,e}front(){if(0!==this.i)return this.q[this.j]}},i=r;e.default=i})),Qr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=zr(),r=class extends t.Base{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e,t){return e>t?-1:e<t?1:0},r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(super(),this.v=t,Array.isArray(e))this.C=r?[...e]:e;else{this.C=[];let t=this;e.forEach((function(e){t.C.push(e)}))}this.i=this.C.length;let i=this.i>>1;for(let n=this.i-1>>1;n>=0;--n)this.k(n,i)}m(e){let t=this.C[e];for(;e>0;){let r=e-1>>1,i=this.C[r];if(this.v(i,t)<=0)break;this.C[e]=i,e=r}this.C[e]=t}k(e,t){let r=this.C[e];for(;e<t;){let t=e<<1|1,i=t+1,n=this.C[t];if(i<this.i&&this.v(n,this.C[i])>0&&(t=i,n=this.C[i]),this.v(n,r)>=0)break;this.C[e]=n,e=t}this.C[e]=r}clear(){this.i=0,this.C.length=0}push(e){this.C.push(e),this.m(this.i),this.i+=1}pop(){if(0===this.i)return;let e=this.C[0],t=this.C.pop();return this.i-=1,this.i&&(this.C[0]=t,this.k(0,this.i>>1)),e}top(){return this.C[0]}find(e){return this.C.indexOf(e)>=0}remove(e){let t=this.C.indexOf(e);return!(t<0)&&(0===t?this.pop():t===this.i-1?(this.C.pop(),this.i-=1):(this.C.splice(t,1,this.C.pop()),this.i-=1,this.m(t),this.k(t,this.i>>1)),!0)}updateItem(e){let t=this.C.indexOf(e);return!(t<0)&&(this.m(t),this.k(t,this.i>>1),!0)}toArray(){return[...this.C]}},i=r;e.default=i})),Yr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=zr(),r=class extends t.Container{};e.default=r})),Jr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.throwIteratorAccessError=function(){throw new RangeError("Iterator access denied!")}})),Xr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.RandomIterator=void 0;var t=zr(),r=Jr(),i=class extends t.ContainerIterator{constructor(e,t){super(t),this.o=e,0===this.iteratorType?(this.pre=function(){return 0===this.o&&(0,r.throwIteratorAccessError)(),this.o-=1,this},this.next=function(){return this.o===this.container.size()&&(0,r.throwIteratorAccessError)(),this.o+=1,this}):(this.pre=function(){return this.o===this.container.size()-1&&(0,r.throwIteratorAccessError)(),this.o+=1,this},this.next=function(){return-1===this.o&&(0,r.throwIteratorAccessError)(),this.o-=1,this})}get pointer(){return this.container.getElementByPos(this.o)}set pointer(e){this.container.setElementByPos(this.o,e)}};e.RandomIterator=i})),$r=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t,r=(t=Yr())&&t.t?t:{default:t},i=Xr();var n=class e extends i.RandomIterator{constructor(e,t,r){super(e,r),this.container=t}copy(){return new e(this.o,this.container,this.iteratorType)}},o=class extends r.default{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(super(),Array.isArray(e))this.J=t?[...e]:e,this.i=e.length;else{this.J=[];let t=this;e.forEach((function(e){t.pushBack(e)}))}}clear(){this.i=0,this.J.length=0}begin(){return new n(0,this)}end(){return new n(this.i,this)}rBegin(){return new n(this.i-1,this,1)}rEnd(){return new n(-1,this,1)}front(){return this.J[0]}back(){return this.J[this.i-1]}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J[e]}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J.splice(e,1),this.i-=1,this.i}eraseElementByValue(e){let t=0;for(let r=0;r<this.i;++r)this.J[r]!==e&&(this.J[t++]=this.J[r]);return this.i=this.J.length=t,this.i}eraseElementByIterator(e){let t=e.o;return e=e.next(),this.eraseElementByPos(t),e}pushBack(e){return this.J.push(e),this.i+=1,this.i}popBack(){if(0!==this.i)return this.i-=1,this.J.pop()}setElementByPos(e,t){if(e<0||e>this.i-1)throw new RangeError;this.J[e]=t}insert(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(e<0||e>this.i)throw new RangeError;return this.J.splice(e,0,...new Array(r).fill(t)),this.i+=r,this.i}find(e){for(let t=0;t<this.i;++t)if(this.J[t]===e)return new n(t,this);return this.end()}reverse(){this.J.reverse()}unique(){let e=1;for(let t=1;t<this.i;++t)this.J[t]!==this.J[t-1]&&(this.J[e++]=this.J[t]);return this.i=this.J.length=e,this.i}sort(e){this.J.sort(e)}forEach(e){for(let t=0;t<this.i;++t)e(this.J[t],t,this)}[Symbol.iterator](){return function*(){yield*this.J}.bind(this)()}},s=o;e.default=s})),Zr=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t,r=(t=Yr())&&t.t?t:{default:t},i=zr(),n=Jr();var o=class e extends i.ContainerIterator{constructor(e,t,r,i){super(i),this.o=e,this.h=t,this.container=r,0===this.iteratorType?(this.pre=function(){return this.o.L===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.L,this})}get pointer(){return this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o.l}set pointer(e){this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o.l=e}copy(){return new e(this.o,this.h,this.container,this.iteratorType)}},s=class extends r.default{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super(),this.h={},this.p=this._=this.h.L=this.h.B=this.h;let t=this;e.forEach((function(e){t.pushBack(e)}))}V(e){let{L:t,B:r}=e;t.B=r,r.L=t,e===this.p&&(this.p=r),e===this._&&(this._=t),this.i-=1}G(e,t){let r=t.B,i={l:e,L:t,B:r};t.B=i,r.L=i,t===this.h&&(this.p=i),r===this.h&&(this._=i),this.i+=1}clear(){this.i=0,this.p=this._=this.h.L=this.h.B=this.h}begin(){return new o(this.p,this.h,this)}end(){return new o(this.h,this.h,this)}rBegin(){return new o(this._,this.h,this,1)}rEnd(){return new o(this.h,this.h,this,1)}front(){return this.p.l}back(){return this._.l}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=this.p;for(;e--;)t=t.B;return t.l}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=this.p;for(;e--;)t=t.B;return this.V(t),this.i}eraseElementByValue(e){let t=this.p;for(;t!==this.h;)t.l===e&&this.V(t),t=t.B;return this.i}eraseElementByIterator(e){let t=e.o;return t===this.h&&(0,n.throwIteratorAccessError)(),e=e.next(),this.V(t),e}pushBack(e){return this.G(e,this._),this.i}popBack(){if(0===this.i)return;let e=this._.l;return this.V(this._),e}pushFront(e){return this.G(e,this.h),this.i}popFront(){if(0===this.i)return;let e=this.p.l;return this.V(this.p),e}setElementByPos(e,t){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;r.l=t}insert(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(e<0||e>this.i)throw new RangeError;if(r<=0)return this.i;if(0===e)for(;r--;)this.pushFront(t);else if(e===this.i)for(;r--;)this.pushBack(t);else{let i=this.p;for(let t=1;t<e;++t)i=i.B;let n=i.B;for(this.i+=r;r--;)i.B={l:t,L:i},i.B.L=i,i=i.B;i.B=n,n.L=i}return this.i}find(e){let t=this.p;for(;t!==this.h;){if(t.l===e)return new o(t,this.h,this);t=t.B}return this.end()}reverse(){if(this.i<=1)return;let e=this.p,t=this._,r=0;for(;r<<1<this.i;){let i=e.l;e.l=t.l,t.l=i,e=e.B,t=t.L,r+=1}}unique(){if(this.i<=1)return this.i;let e=this.p;for(;e!==this.h;){let t=e;for(;t.B!==this.h&&t.l===t.B.l;)t=t.B,this.i-=1;e.B=t.B,e.B.L=e,e=e.B}return this.i}sort(e){if(this.i<=1)return;let t=[];this.forEach((function(e){t.push(e)})),t.sort(e);let r=this.p;t.forEach((function(e){r.l=e,r=r.B}))}merge(e){let t=this;if(0===this.i)e.forEach((function(e){t.pushBack(e)}));else{let r=this.p;e.forEach((function(e){for(;r!==t.h&&r.l<=e;)r=r.B;t.G(e,r.L)}))}return this.i}forEach(e){let t=this.p,r=0;for(;t!==this.h;)e(t.l,r++,this),t=t.B}[Symbol.iterator](){return function*(){if(0===this.i)return;let e=this.p;for(;e!==this.h;)yield e.l,e=e.B}.bind(this)()}},a=s;e.default=a})),ei=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t,r=(t=Yr())&&t.t?t:{default:t},i=Xr();var n=class e extends i.RandomIterator{constructor(e,t,r){super(e,r),this.container=t}copy(){return new e(this.o,this.container,this.iteratorType)}},o=class extends r.default{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4096;super(),this.j=0,this.D=0,this.R=0,this.N=0,this.P=0,this.A=[];let r=(()=>{if("number"==typeof e.length)return e.length;if("number"==typeof e.size)return e.size;if("function"==typeof e.size)return e.size();throw new TypeError("Cannot get the length or size of the container")})();this.F=t,this.P=Math.max(Math.ceil(r/this.F),1);for(let o=0;o<this.P;++o)this.A.push(new Array(this.F));let i=Math.ceil(r/this.F);this.j=this.R=(this.P>>1)-(i>>1),this.D=this.N=this.F-r%this.F>>1;let n=this;e.forEach((function(e){n.pushBack(e)}))}T(){let e=[],t=Math.max(this.P>>1,1);for(let r=0;r<t;++r)e[r]=new Array(this.F);for(let r=this.j;r<this.P;++r)e[e.length]=this.A[r];for(let r=0;r<this.R;++r)e[e.length]=this.A[r];e[e.length]=[...this.A[this.R]],this.j=t,this.R=e.length-1;for(let r=0;r<t;++r)e[e.length]=new Array(this.F);this.A=e,this.P=e.length}O(e){let t=this.D+e+1,r=t%this.F,i=r-1,n=this.j+(t-r)/this.F;return 0===r&&(n-=1),n%=this.P,i<0&&(i+=this.F),{curNodeBucketIndex:n,curNodePointerIndex:i}}clear(){this.A=[new Array(this.F)],this.P=1,this.j=this.R=this.i=0,this.D=this.N=this.F>>1}begin(){return new n(0,this)}end(){return new n(this.i,this)}rBegin(){return new n(this.i-1,this,1)}rEnd(){return new n(-1,this,1)}front(){if(0!==this.i)return this.A[this.j][this.D]}back(){if(0!==this.i)return this.A[this.R][this.N]}pushBack(e){return this.i&&(this.N<this.F-1?this.N+=1:this.R<this.P-1?(this.R+=1,this.N=0):(this.R=0,this.N=0),this.R===this.j&&this.N===this.D&&this.T()),this.i+=1,this.A[this.R][this.N]=e,this.i}popBack(){if(0===this.i)return;let e=this.A[this.R][this.N];return 1!==this.i&&(this.N>0?this.N-=1:this.R>0?(this.R-=1,this.N=this.F-1):(this.R=this.P-1,this.N=this.F-1)),this.i-=1,e}pushFront(e){return this.i&&(this.D>0?this.D-=1:this.j>0?(this.j-=1,this.D=this.F-1):(this.j=this.P-1,this.D=this.F-1),this.j===this.R&&this.D===this.N&&this.T()),this.i+=1,this.A[this.j][this.D]=e,this.i}popFront(){if(0===this.i)return;let e=this.A[this.j][this.D];return 1!==this.i&&(this.D<this.F-1?this.D+=1:this.j<this.P-1?(this.j+=1,this.D=0):(this.j=0,this.D=0)),this.i-=1,e}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:t,curNodePointerIndex:r}=this.O(e);return this.A[t][r]}setElementByPos(e,t){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:r,curNodePointerIndex:i}=this.O(e);this.A[r][i]=t}insert(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(e<0||e>this.i)throw new RangeError;if(0===e)for(;r--;)this.pushFront(t);else if(e===this.i)for(;r--;)this.pushBack(t);else{let i=[];for(let t=e;t<this.i;++t)i.push(this.getElementByPos(t));this.cut(e-1);for(let e=0;e<r;++e)this.pushBack(t);for(let e=0;e<i.length;++e)this.pushBack(i[e])}return this.i}cut(e){if(e<0)return this.clear(),0;let{curNodeBucketIndex:t,curNodePointerIndex:r}=this.O(e);return this.R=t,this.N=r,this.i=e+1,this.i}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;if(0===e)this.popFront();else if(e===this.i-1)this.popBack();else{let t=[];for(let i=e+1;i<this.i;++i)t.push(this.getElementByPos(i));this.cut(e),this.popBack();let r=this;t.forEach((function(e){r.pushBack(e)}))}return this.i}eraseElementByValue(e){if(0===this.i)return 0;let t=[];for(let i=0;i<this.i;++i){let r=this.getElementByPos(i);r!==e&&t.push(r)}let r=t.length;for(let i=0;i<r;++i)this.setElementByPos(i,t[i]);return this.cut(r-1)}eraseElementByIterator(e){let t=e.o;return this.eraseElementByPos(t),e=e.next()}find(e){for(let t=0;t<this.i;++t)if(this.getElementByPos(t)===e)return new n(t,this);return this.end()}reverse(){let e=0,t=this.i-1;for(;e<t;){let r=this.getElementByPos(e);this.setElementByPos(e,this.getElementByPos(t)),this.setElementByPos(t,r),e+=1,t-=1}}unique(){if(this.i<=1)return this.i;let e=1,t=this.getElementByPos(0);for(let r=1;r<this.i;++r){let i=this.getElementByPos(r);i!==t&&(t=i,this.setElementByPos(e++,i))}for(;this.i>e;)this.popBack();return this.i}sort(e){let t=[];for(let r=0;r<this.i;++r)t.push(this.getElementByPos(r));t.sort(e);for(let r=0;r<this.i;++r)this.setElementByPos(r,t[r])}shrinkToFit(){if(0===this.i)return;let e=[];this.forEach((function(t){e.push(t)})),this.P=Math.max(Math.ceil(this.i/this.F),1),this.i=this.j=this.R=this.D=this.N=0,this.A=[];for(let t=0;t<this.P;++t)this.A.push(new Array(this.F));for(let t=0;t<e.length;++t)this.pushBack(e[t])}forEach(e){for(let t=0;t<this.i;++t)e(this.getElementByPos(t),t,this)}[Symbol.iterator](){return function*(){for(let e=0;e<this.i;++e)yield this.getElementByPos(e)}.bind(this)()}},s=o;e.default=s})),ti=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.TreeNodeEnableIndex=e.TreeNode=void 0;var t=class{constructor(e,t){this.ee=1,this.u=void 0,this.l=void 0,this.U=void 0,this.W=void 0,this.tt=void 0,this.u=e,this.l=t}L(){let e=this;if(1===e.ee&&e.tt.tt===e)e=e.W;else if(e.U)for(e=e.U;e.W;)e=e.W;else{let t=e.tt;for(;t.U===e;)e=t,t=e.tt;e=t}return e}B(){let e=this;if(e.W){for(e=e.W;e.U;)e=e.U;return e}{let t=e.tt;for(;t.W===e;)e=t,t=e.tt;return e.W!==t?t:e}}te(){let e=this.tt,t=this.W,r=t.U;return e.tt===this?e.tt=t:e.U===this?e.U=t:e.W=t,t.tt=e,t.U=this,this.tt=t,this.W=r,r&&(r.tt=this),t}se(){let e=this.tt,t=this.U,r=t.W;return e.tt===this?e.tt=t:e.U===this?e.U=t:e.W=t,t.tt=e,t.W=this,this.tt=t,this.U=r,r&&(r.tt=this),t}};e.TreeNode=t;e.TreeNodeEnableIndex=class extends t{constructor(){super(...arguments),this.rt=1}te(){let e=super.te();return this.ie(),e.ie(),e}se(){let e=super.se();return this.ie(),e.ie(),e}ie(){this.rt=1,this.U&&(this.rt+=this.U.rt),this.W&&(this.rt+=this.W.rt)}}})),ri=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=ti(),r=zr(),i=Jr(),n=class extends r.Container{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e,t){return e<t?-1:e>t?1:0},r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super(),this.Y=void 0,this.v=e,r?(this.re=t.TreeNodeEnableIndex,this.M=function(e,t,r){let i=this.ne(e,t,r);if(i){let e=i.tt;for(;e!==this.h;)e.rt+=1,e=e.tt;let t=this.he(i);if(t){let{parentNode:e,grandParent:r,curNode:i}=t;e.ie(),r.ie(),i.ie()}}return this.i},this.V=function(e){let t=this.fe(e);for(;t!==this.h;)t.rt-=1,t=t.tt}):(this.re=t.TreeNode,this.M=function(e,t,r){let i=this.ne(e,t,r);return i&&this.he(i),this.i},this.V=this.fe),this.h=new this.re}X(e,t){let r=this.h;for(;e;){let i=this.v(e.u,t);if(i<0)e=e.W;else{if(!(i>0))return e;r=e,e=e.U}}return r}Z(e,t){let r=this.h;for(;e;)this.v(e.u,t)<=0?e=e.W:(r=e,e=e.U);return r}$(e,t){let r=this.h;for(;e;){let i=this.v(e.u,t);if(i<0)r=e,e=e.W;else{if(!(i>0))return e;e=e.U}}return r}rr(e,t){let r=this.h;for(;e;)this.v(e.u,t)<0?(r=e,e=e.W):e=e.U;return r}ue(e){for(;;){let t=e.tt;if(t===this.h)return;if(1===e.ee)return void(e.ee=0);if(e===t.U){let r=t.W;if(1===r.ee)r.ee=0,t.ee=1,t===this.Y?this.Y=t.te():t.te();else{if(r.W&&1===r.W.ee)return r.ee=t.ee,t.ee=0,r.W.ee=0,void(t===this.Y?this.Y=t.te():t.te());r.U&&1===r.U.ee?(r.ee=1,r.U.ee=0,r.se()):(r.ee=1,e=t)}}else{let r=t.U;if(1===r.ee)r.ee=0,t.ee=1,t===this.Y?this.Y=t.se():t.se();else{if(r.U&&1===r.U.ee)return r.ee=t.ee,t.ee=0,r.U.ee=0,void(t===this.Y?this.Y=t.se():t.se());r.W&&1===r.W.ee?(r.ee=1,r.W.ee=0,r.te()):(r.ee=1,e=t)}}}}fe(e){if(1===this.i)return this.clear(),this.h;let t=e;for(;t.U||t.W;){if(t.W)for(t=t.W;t.U;)t=t.U;else t=t.U;[e.u,t.u]=[t.u,e.u],[e.l,t.l]=[t.l,e.l],e=t}this.h.U===t?this.h.U=t.tt:this.h.W===t&&(this.h.W=t.tt),this.ue(t);let r=t.tt;return t===r.U?r.U=void 0:r.W=void 0,this.i-=1,this.Y.ee=0,r}oe(e,t){return void 0!==e&&(!(!this.oe(e.U,t)&&!t(e))||this.oe(e.W,t))}he(e){for(;;){let t=e.tt;if(0===t.ee)return;let r=t.tt;if(t===r.U){let i=r.W;if(i&&1===i.ee){if(i.ee=t.ee=0,r===this.Y)return;r.ee=1,e=r;continue}if(e===t.W){if(e.ee=0,e.U&&(e.U.tt=t),e.W&&(e.W.tt=r),t.W=e.U,r.U=e.W,e.U=t,e.W=r,r===this.Y)this.Y=e,this.h.tt=e;else{let t=r.tt;t.U===r?t.U=e:t.W=e}return e.tt=r.tt,t.tt=e,r.tt=e,r.ee=1,{parentNode:t,grandParent:r,curNode:e}}t.ee=0,r===this.Y?this.Y=r.se():r.se(),r.ee=1}else{let i=r.U;if(i&&1===i.ee){if(i.ee=t.ee=0,r===this.Y)return;r.ee=1,e=r;continue}if(e===t.U){if(e.ee=0,e.U&&(e.U.tt=r),e.W&&(e.W.tt=t),r.W=e.U,t.U=e.W,e.U=r,e.W=t,r===this.Y)this.Y=e,this.h.tt=e;else{let t=r.tt;t.U===r?t.U=e:t.W=e}return e.tt=r.tt,t.tt=e,r.tt=e,r.ee=1,{parentNode:t,grandParent:r,curNode:e}}t.ee=0,r===this.Y?this.Y=r.te():r.te(),r.ee=1}return}}ne(e,t,r){if(void 0===this.Y)return this.i+=1,this.Y=new this.re(e,t),this.Y.ee=0,this.Y.tt=this.h,this.h.tt=this.Y,this.h.U=this.Y,void(this.h.W=this.Y);let i,n=this.h.U,o=this.v(n.u,e);if(0!==o){if(o>0)n.U=new this.re(e,t),n.U.tt=n,i=n.U,this.h.U=i;else{let n=this.h.W,o=this.v(n.u,e);if(0===o)return void(n.l=t);if(o<0)n.W=new this.re(e,t),n.W.tt=n,i=n.W,this.h.W=i;else{if(void 0!==r){let n=r.o;if(n!==this.h){let r=this.v(n.u,e);if(0===r)return void(n.l=t);if(r>0){let r=n.L(),o=this.v(r.u,e);if(0===o)return void(r.l=t);o<0&&(i=new this.re(e,t),void 0===r.W?(r.W=i,i.tt=r):(n.U=i,i.tt=n))}}}if(void 0===i)for(i=this.Y;;){let r=this.v(i.u,e);if(r>0){if(void 0===i.U){i.U=new this.re(e,t),i.U.tt=i,i=i.U;break}i=i.U}else{if(!(r<0))return void(i.l=t);if(void 0===i.W){i.W=new this.re(e,t),i.W.tt=i,i=i.W;break}i=i.W}}}}return this.i+=1,i}n.l=t}I(e,t){for(;e;){let r=this.v(e.u,t);if(r<0)e=e.W;else{if(!(r>0))return e;e=e.U}}return e||this.h}clear(){this.i=0,this.Y=void 0,this.h.tt=void 0,this.h.U=this.h.W=void 0}updateKeyByIterator(e,t){let r=e.o;if(r===this.h&&(0,i.throwIteratorAccessError)(),1===this.i)return r.u=t,!0;if(r===this.h.U)return this.v(r.B().u,t)>0&&(r.u=t,!0);if(r===this.h.W)return this.v(r.L().u,t)<0&&(r.u=t,!0);let n=r.L().u;if(this.v(n,t)>=0)return!1;let o=r.B().u;return!(this.v(o,t)<=0)&&(r.u=t,!0)}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=0,r=this;return this.oe(this.Y,(function(i){return e===t?(r.V(i),!0):(t+=1,!1)})),this.i}eraseElementByKey(e){if(0===this.i)return!1;let t=this.I(this.Y,e);return t!==this.h&&(this.V(t),!0)}eraseElementByIterator(e){let t=e.o;t===this.h&&(0,i.throwIteratorAccessError)();let r=void 0===t.W;return 0===e.iteratorType?r&&e.next():(!r||void 0===t.U)&&e.next(),this.V(t),e}forEach(e){let t=0;for(let r of this)e(r,t++,this)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t,r=0;for(let i of this){if(r===e){t=i;break}r+=1}return t}getHeight(){if(0===this.i)return 0;let e=function(t){return t?Math.max(e(t.U),e(t.W))+1:0};return e(this.Y)}},o=n;e.default=o})),ii=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=zr(),r=Jr(),i=class extends t.ContainerIterator{constructor(e,t,i){super(i),this.o=e,this.h=t,0===this.iteratorType?(this.pre=function(){return this.o===this.h.U&&(0,r.throwIteratorAccessError)(),this.o=this.o.L(),this},this.next=function(){return this.o===this.h&&(0,r.throwIteratorAccessError)(),this.o=this.o.B(),this}):(this.pre=function(){return this.o===this.h.W&&(0,r.throwIteratorAccessError)(),this.o=this.o.B(),this},this.next=function(){return this.o===this.h&&(0,r.throwIteratorAccessError)(),this.o=this.o.L(),this})}get index(){let e=this.o,t=this.h.tt;if(e===this.h)return t?t.rt-1:0;let r=0;for(e.U&&(r+=e.U.rt);e!==t;){let t=e.tt;e===t.W&&(r+=1,t.U&&(r+=t.U.rt)),e=t}return r}};e.default=i})),ni=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=n(ri()),r=n(ii()),i=Jr();function n(e){return e&&e.t?e:{default:e}}var o=class e extends r.default{constructor(e,t,r,i){super(e,t,i),this.container=r}get pointer(){return this.o===this.h&&(0,i.throwIteratorAccessError)(),this.o.u}copy(){return new e(this.o,this.h,this.container,this.iteratorType)}},s=class extends t.default{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super(arguments.length>1?arguments[1]:void 0,arguments.length>2?arguments[2]:void 0);let t=this;e.forEach((function(e){t.insert(e)}))}*K(e){void 0!==e&&(yield*this.K(e.U),yield e.u,yield*this.K(e.W))}begin(){return new o(this.h.U||this.h,this.h,this)}end(){return new o(this.h,this.h,this)}rBegin(){return new o(this.h.W||this.h,this.h,this,1)}rEnd(){return new o(this.h,this.h,this,1)}front(){return this.h.U?this.h.U.u:void 0}back(){return this.h.W?this.h.W.u:void 0}insert(e,t){return this.M(e,void 0,t)}find(e){let t=this.I(this.Y,e);return new o(t,this.h,this)}lowerBound(e){let t=this.X(this.Y,e);return new o(t,this.h,this)}upperBound(e){let t=this.Z(this.Y,e);return new o(t,this.h,this)}reverseLowerBound(e){let t=this.$(this.Y,e);return new o(t,this.h,this)}reverseUpperBound(e){let t=this.rr(this.Y,e);return new o(t,this.h,this)}union(e){let t=this;return e.forEach((function(e){t.insert(e)})),this.i}[Symbol.iterator](){return this.K(this.Y)}},a=s;e.default=a})),oi=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=n(ri()),r=n(ii()),i=Jr();function n(e){return e&&e.t?e:{default:e}}var o=class e extends r.default{constructor(e,t,r,i){super(e,t,i),this.container=r}get pointer(){this.o===this.h&&(0,i.throwIteratorAccessError)();let e=this;return new Proxy([],{get:(t,r)=>"0"===r?e.o.u:"1"===r?e.o.l:void 0,set(t,r,i){if("1"!==r)throw new TypeError("props must be 1");return e.o.l=i,!0}})}copy(){return new e(this.o,this.h,this.container,this.iteratorType)}},s=class extends t.default{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super(arguments.length>1?arguments[1]:void 0,arguments.length>2?arguments[2]:void 0);let t=this;e.forEach((function(e){t.setElement(e[0],e[1])}))}*K(e){void 0!==e&&(yield*this.K(e.U),yield[e.u,e.l],yield*this.K(e.W))}begin(){return new o(this.h.U||this.h,this.h,this)}end(){return new o(this.h,this.h,this)}rBegin(){return new o(this.h.W||this.h,this.h,this,1)}rEnd(){return new o(this.h,this.h,this,1)}front(){if(0===this.i)return;let e=this.h.U;return[e.u,e.l]}back(){if(0===this.i)return;let e=this.h.W;return[e.u,e.l]}lowerBound(e){let t=this.X(this.Y,e);return new o(t,this.h,this)}upperBound(e){let t=this.Z(this.Y,e);return new o(t,this.h,this)}reverseLowerBound(e){let t=this.$(this.Y,e);return new o(t,this.h,this)}reverseUpperBound(e){let t=this.rr(this.Y,e);return new o(t,this.h,this)}setElement(e,t,r){return this.M(e,t,r)}find(e){let t=this.I(this.Y,e);return new o(t,this.h,this)}getElementByKey(e){return this.I(this.Y,e).l}union(e){let t=this;return e.forEach((function(e){t.setElement(e[0],e[1])})),this.i}[Symbol.iterator](){return this.K(this.Y)}},a=s;e.default=a})),si=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=function(e){let t=typeof e;return"object"===t&&null!==e||"function"===t}})),ai=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.HashContainerIterator=e.HashContainer=void 0;var t,r=zr(),i=(t=si())&&t.t?t:{default:t},n=Jr();var o=class extends r.ContainerIterator{constructor(e,t,r){super(r),this.o=e,this.h=t,0===this.iteratorType?(this.pre=function(){return this.o.L===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,n.throwIteratorAccessError)(),this.o=this.o.L,this})}};e.HashContainerIterator=o;var s=class extends r.Container{constructor(){super(),this.H=[],this.g={},this.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(this.g,null),this.h={},this.h.L=this.h.B=this.p=this._=this.h}V(e){let{L:t,B:r}=e;t.B=r,r.L=t,e===this.p&&(this.p=r),e===this._&&(this._=t),this.i-=1}M(e,t,r){let n;if(void 0===r&&(r=(0,i.default)(e)),r){let r=e[this.HASH_TAG];if(void 0!==r)return this.H[r].l=t,this.i;Object.defineProperty(e,this.HASH_TAG,{value:this.H.length,configurable:!0}),n={u:e,l:t,L:this._,B:this.h},this.H.push(n)}else{let r=this.g[e];if(r)return r.l=t,this.i;n={u:e,l:t,L:this._,B:this.h},this.g[e]=n}return 0===this.i?(this.p=n,this.h.B=n):this._.B=n,this._=n,this.h.L=n,++this.i}I(e,t){if(void 0===t&&(t=(0,i.default)(e)),t){let t=e[this.HASH_TAG];return void 0===t?this.h:this.H[t]}return this.g[e]||this.h}clear(){let e=this.HASH_TAG;this.H.forEach((function(t){delete t.u[e]})),this.H=[],this.g={},Object.setPrototypeOf(this.g,null),this.i=0,this.p=this._=this.h.L=this.h.B=this.h}eraseElementByKey(e,t){let r;if(void 0===t&&(t=(0,i.default)(e)),t){let t=e[this.HASH_TAG];if(void 0===t)return!1;delete e[this.HASH_TAG],r=this.H[t],delete this.H[t]}else{if(r=this.g[e],void 0===r)return!1;delete this.g[e]}return this.V(r),!0}eraseElementByIterator(e){let t=e.o;return t===this.h&&(0,n.throwIteratorAccessError)(),this.V(t),e.next()}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=this.p;for(;e--;)t=t.B;return this.V(t),this.i}};e.HashContainer=s})),li=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t=ai(),r=Jr(),i=class e extends t.HashContainerIterator{constructor(e,t,r,i){super(e,t,i),this.container=r}get pointer(){return this.o===this.h&&(0,r.throwIteratorAccessError)(),this.o.u}copy(){return new e(this.o,this.h,this.container,this.iteratorType)}},n=class extends t.HashContainer{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super();let t=this;e.forEach((function(e){t.insert(e)}))}begin(){return new i(this.p,this.h,this)}end(){return new i(this.h,this.h,this)}rBegin(){return new i(this._,this.h,this,1)}rEnd(){return new i(this.h,this.h,this,1)}front(){return this.p.u}back(){return this._.u}insert(e,t){return this.M(e,void 0,t)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=this.p;for(;e--;)t=t.B;return t.u}find(e,t){let r=this.I(e,t);return new i(r,this.h,this)}forEach(e){let t=0,r=this.p;for(;r!==this.h;)e(r.u,t++,this),r=r.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield e.u,e=e.B}.bind(this)()}},o=n;e.default=o})),ci=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),e.default=void 0;var t,r=ai(),i=(t=si())&&t.t?t:{default:t},n=Jr();var o=class e extends r.HashContainerIterator{constructor(e,t,r,i){super(e,t,i),this.container=r}get pointer(){this.o===this.h&&(0,n.throwIteratorAccessError)();let e=this;return new Proxy([],{get:(t,r)=>"0"===r?e.o.u:"1"===r?e.o.l:void 0,set(t,r,i){if("1"!==r)throw new TypeError("props must be 1");return e.o.l=i,!0}})}copy(){return new e(this.o,this.h,this.container,this.iteratorType)}},s=class extends r.HashContainer{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];super();let t=this;e.forEach((function(e){t.setElement(e[0],e[1])}))}begin(){return new o(this.p,this.h,this)}end(){return new o(this.h,this.h,this)}rBegin(){return new o(this._,this.h,this,1)}rEnd(){return new o(this.h,this.h,this,1)}front(){if(0!==this.i)return[this.p.u,this.p.l]}back(){if(0!==this.i)return[this._.u,this._.l]}setElement(e,t,r){return this.M(e,t,r)}getElementByKey(e,t){if(void 0===t&&(t=(0,i.default)(e)),t){let t=e[this.HASH_TAG];return void 0!==t?this.H[t].l:void 0}let r=this.g[e];return r?r.l:void 0}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let t=this.p;for(;e--;)t=t.B;return[t.u,t.l]}find(e,t){let r=this.I(e,t);return new o(r,this.h,this)}forEach(e){let t=0,r=this.p;for(;r!==this.h;)e([r.u,r.l],t++,this),r=r.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield[e.u,e.l],e=e.B}.bind(this)()}},a=s;e.default=a})),ui=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"t",{value:!0}),Object.defineProperty(e,"Deque",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"HashMap",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"HashSet",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(e,"LinkList",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"OrderedMap",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"OrderedSet",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"PriorityQueue",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"Queue",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"Stack",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"Vector",{enumerable:!0,get:function(){return n.default}});var t=h(Gr()),r=h(Kr()),i=h(Qr()),n=h($r()),o=h(Zr()),s=h(ei()),a=h(ni()),l=h(oi()),c=h(li()),u=h(ci());function h(e){return e&&e.t?e:{default:e}}})),hi=S(((e,t)=>{x(),yt(),nt();var r=ui().OrderedSet,i=xr()("number-allocator:trace"),n=xr()("number-allocator:error");function o(e,t){this.low=e,this.high=t}function s(e,t){if(!(this instanceof s))return new s(e,t);this.min=e,this.max=t,this.ss=new r([],((e,t)=>e.compare(t))),i("Create"),this.clear()}o.prototype.equals=function(e){return this.low===e.low&&this.high===e.high},o.prototype.compare=function(e){return this.low<e.low&&this.high<e.low?-1:e.low<this.low&&e.high<this.low?1:0},s.prototype.firstVacant=function(){return 0===this.ss.size()?null:this.ss.front().low},s.prototype.alloc=function(){if(0===this.ss.size())return i("alloc():empty"),null;let e=this.ss.begin(),t=e.pointer.low,r=e.pointer.high,n=t;return n+1<=r?this.ss.updateKeyByIterator(e,new o(t+1,r)):this.ss.eraseElementByPos(0),i("alloc():"+n),n},s.prototype.use=function(e){let t=new o(e,e),r=this.ss.lowerBound(t);if(!r.equals(this.ss.end())){let n=r.pointer.low,s=r.pointer.high;return r.pointer.equals(t)?(this.ss.eraseElementByIterator(r),i("use():"+e),!0):!(n>e)&&(n===e?(this.ss.updateKeyByIterator(r,new o(n+1,s)),i("use():"+e),!0):s===e?(this.ss.updateKeyByIterator(r,new o(n,s-1)),i("use():"+e),!0):(this.ss.updateKeyByIterator(r,new o(e+1,s)),this.ss.insert(new o(n,e-1)),i("use():"+e),!0))}return i("use():failed"),!1},s.prototype.free=function(e){if(e<this.min||e>this.max)return void n("free():"+e+" is out of range");let t=new o(e,e),r=this.ss.upperBound(t);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin()))return void this.ss.insert(t);r.pre();let i=r.pointer.high;r.pointer.high+1===e?this.ss.updateKeyByIterator(r,new o(i,e)):this.ss.insert(t)}else if(r.equals(this.ss.begin()))if(e+1===r.pointer.low){let t=r.pointer.high;this.ss.updateKeyByIterator(r,new o(e,t))}else this.ss.insert(t);else{let i=r.pointer.low,n=r.pointer.high;r.pre();let s=r.pointer.low;r.pointer.high+1===e?e+1===i?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new o(s,n))):this.ss.updateKeyByIterator(r,new o(s,e)):e+1===i?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new o(e,n))):this.ss.insert(t)}i("free():"+e)},s.prototype.clear=function(){i("clear()"),this.ss.clear(),this.ss.insert(new o(this.min,this.max))},s.prototype.intervalCount=function(){return this.ss.size()},s.prototype.dump=function(){console.log("length:"+this.ss.size());for(let e of this.ss)console.log(e)},t.exports=s})),di=S(((e,t)=>{x(),yt(),nt();var r=hi();t.exports.NumberAllocator=r})),fi=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t=Vr(),r=di();e.default=class{constructor(e){e>0&&(this.aliasToTopic=new t.LRUCache({max:e}),this.topicToAlias={},this.numberAllocator=new r.NumberAllocator(1,e),this.max=e,this.length=0)}put(e,t){if(0===t||t>this.max)return!1;let r=this.aliasToTopic.get(t);return r&&delete this.topicToAlias[r],this.aliasToTopic.set(t,e),this.topicToAlias[e]=t,this.numberAllocator.use(t),this.length=this.aliasToTopic.size,!0}getTopicByAlias(e){return this.aliasToTopic.get(e)}getAliasByTopic(e){let t=this.topicToAlias[e];return typeof t<"u"&&this.aliasToTopic.get(t),t}clear(){this.aliasToTopic.clear(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0}getLruAlias(){return this.numberAllocator.firstVacant()||[...this.aliasToTopic.keys()][this.aliasToTopic.size-1]}}})),pi=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var r=qr(),i=t(fi()),n=Fr();e.default=(e,t)=>{e.log("_handleConnack");let{options:o}=e,s=5===o.protocolVersion?t.reasonCode:t.returnCode;if(clearTimeout(e.connackTimer),delete e.topicAliasSend,t.properties){if(t.properties.topicAliasMaximum){if(t.properties.topicAliasMaximum>65535)return void e.emit("error",new Error("topicAliasMaximum from broker is out of range"));t.properties.topicAliasMaximum>0&&(e.topicAliasSend=new i.default(t.properties.topicAliasMaximum))}t.properties.serverKeepAlive&&o.keepalive&&(o.keepalive=t.properties.serverKeepAlive),t.properties.maximumPacketSize&&(o.properties||(o.properties={}),o.properties.maximumPacketSize=t.properties.maximumPacketSize)}if(0===s)e.reconnecting=!1,e._onConnect(t);else if(s>0){let t=new n.ErrorWithReasonCode("Connection refused: ".concat(r.ReasonCodes[s]),s);e.emit("error",t),e.options.reconnectOnConnackError&&e._cleanUp(!0)}}})),bi=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});e.default=(e,t,r)=>{e.log("handling pubrel packet");let i=typeof r<"u"?r:e.noop,{messageId:n}=t,o={cmd:"pubcomp",messageId:n};e.incomingStore.get(t,((t,r)=>{t?e._sendPacket(o,i):(e.emit("message",r.topic,r.payload,r),e.handleMessage(r,(t=>{if(t)return i(t);e.incomingStore.del(r,e.noop),e._sendPacket(o,i)})))}))}})),gi=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var r=t(Wr()),i=t(Hr()),n=t(pi()),o=t(qr()),s=t(bi());e.default=(e,t,a)=>{let{options:l}=e;if(5===l.protocolVersion&&l.properties&&l.properties.maximumPacketSize&&l.properties.maximumPacketSize<t.length)return e.emit("error",new Error("exceeding packets size ".concat(t.cmd))),e.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),e;switch(e.log("_handlePacket :: emitting packetreceive"),e.emit("packetreceive",t),t.cmd){case"publish":(0,r.default)(e,t,a);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":e.reschedulePing(),(0,o.default)(e,t),a();break;case"pubrel":e.reschedulePing(),(0,s.default)(e,t,a);break;case"connack":(0,n.default)(e,t),a();break;case"auth":e.reschedulePing(),(0,i.default)(e,t),a();break;case"pingresp":e.log("_handlePacket :: received pingresp"),e.reschedulePing(!0),a();break;case"disconnect":e.emit("disconnect",t),a();break;default:e.log("_handlePacket :: unknown command"),a()}}})),mi=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.TypedEventEmitter=void 0;var r=t((Qt(),A(Ft))),i=Fr(),n=class{};e.TypedEventEmitter=n,(0,i.applyMixin)(n,r.default)})),yi=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.isReactNativeBrowser=e.isWebWorker=void 0;var t=()=>{var e,t,r;return!("object"!=typeof self||null===(r=null===(t=null===(e=self)||void 0===e?void 0:e.constructor)||void 0===t?void 0:t.name)||void 0===r||!r.includes("WorkerGlobalScope"))},r=()=>typeof navigator<"u"&&"ReactNative"===navigator.product,i=(()=>{var e;return typeof window<"u"&&(typeof navigator<"u"&&(null===(e=navigator.userAgent)||void 0===e?void 0:e.toLowerCase().indexOf(" electron/"))>-1&&null!==k&&void 0!==k&&k.versions?!Object.prototype.hasOwnProperty.call(k.versions,"electron"):typeof window.document<"u")})()||t()||r();e.isWebWorker=t(),e.isReactNativeBrowser=r(),e.default=i})),vi=S(((e,t)=>{var i,n;x(),yt(),nt(),i=e,n=function(e){var t,r=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,i=536870912,n=2*i,o=new WeakMap,s=function(e,t){return function(o){var s=t.get(o),a=void 0===s?o.size:s<n?s+1:0;if(!o.has(a))return e(o,a);if(o.size<i){for(;o.has(a);)a=Math.floor(Math.random()*n);return e(o,a)}if(o.size>r)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;o.has(a);)a=Math.floor(Math.random()*r);return e(o,a)}}((t=o,function(e,r){return t.set(e,r),r}),o),a=function(e){return function(t){var r=e(t);return t.add(r),r}}(s);e.addUniqueNumber=a,e.generateUniqueNumber=s},"object"==typeof e&&typeof t<"u"?n(e):"function"==typeof define&&r(704)?define(["exports"],n):n((i=typeof globalThis<"u"?globalThis:i||self).fastUniqueNumbers={})})),wi=S(((e,t)=>{var i,n;x(),yt(),nt(),i=e,n=function(e,t){e.load=function(e){var r=new Map([[0,function(){}]]),i=new Map([[0,function(){}]]),n=new Map,o=new Worker(e);return o.addEventListener("message",(function(e){var t=e.data;if(function(e){return void 0!==e.method&&"call"===e.method}(t)){var o=t.params,s=o.timerId,a=o.timerType;if("interval"===a){var l=r.get(s);if("number"==typeof l){var c=n.get(l);if(void 0===c||c.timerId!==s||c.timerType!==a)throw new Error("The timer is in an undefined state.")}else{if(!(typeof l<"u"))throw new Error("The timer is in an undefined state.");l()}}else if("timeout"===a){var u=i.get(s);if("number"==typeof u){var h=n.get(u);if(void 0===h||h.timerId!==s||h.timerType!==a)throw new Error("The timer is in an undefined state.")}else{if(!(typeof u<"u"))throw new Error("The timer is in an undefined state.");u(),i.delete(s)}}}else{if(!function(e){return null===e.error&&"number"==typeof e.id}(t)){var d=t.error.message;throw new Error(d)}var f=t.id,p=n.get(f);if(void 0===p)throw new Error("The timer is in an undefined state.");var b=p.timerId,g=p.timerType;n.delete(f),"interval"===g?r.delete(b):i.delete(b)}})),{clearInterval:function(e){var i=t.generateUniqueNumber(n);n.set(i,{timerId:e,timerType:"interval"}),r.set(e,i),o.postMessage({id:i,method:"clear",params:{timerId:e,timerType:"interval"}})},clearTimeout:function(e){var r=t.generateUniqueNumber(n);n.set(r,{timerId:e,timerType:"timeout"}),i.set(e,r),o.postMessage({id:r,method:"clear",params:{timerId:e,timerType:"timeout"}})},setInterval:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=t.generateUniqueNumber(r);return r.set(n,(function(){e(),"function"==typeof r.get(n)&&o.postMessage({id:null,method:"set",params:{delay:i,now:performance.now(),timerId:n,timerType:"interval"}})})),o.postMessage({id:null,method:"set",params:{delay:i,now:performance.now(),timerId:n,timerType:"interval"}}),n},setTimeout:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=t.generateUniqueNumber(i);return i.set(n,e),o.postMessage({id:null,method:"set",params:{delay:r,now:performance.now(),timerId:n,timerType:"timeout"}}),n}}}},"object"==typeof e&&typeof t<"u"?n(e,vi()):"function"==typeof define&&r(704)?define(["exports","fast-unique-numbers"],n):n((i=typeof globalThis<"u"?globalThis:i||self).workerTimersBroker={},i.fastUniqueNumbers)})),_i=S(((e,t)=>{var i,n;x(),yt(),nt(),i=e,n=function(e,t){var r=function(e,t){var r=null;return function(){if(null!==r)return r;var i=new Blob([t],{type:"application/javascript; charset=utf-8"}),n=URL.createObjectURL(i);return r=e(n),setTimeout((function(){return URL.revokeObjectURL(n)})),r}}(t.load,'(()=>{var e={472:(e,t,r)=>{var o,i;void 0===(i="function"==typeof(o=function(){"use strict";var e=new Map,t=new Map,r=function(t){var r=e.get(t);if(void 0===r)throw new Error(\'There is no interval scheduled with the given id "\'.concat(t,\'".\'));clearTimeout(r),e.delete(t)},o=function(e){var r=t.get(e);if(void 0===r)throw new Error(\'There is no timeout scheduled with the given id "\'.concat(e,\'".\'));clearTimeout(r),t.delete(e)},i=function(e,t){var r,o=performance.now();return{expected:o+(r=e-Math.max(0,o-t)),remainingDelay:r}},n=function e(t,r,o,i){var n=performance.now();n>o?postMessage({id:null,method:"call",params:{timerId:r,timerType:i}}):t.set(r,setTimeout(e,o-n,t,r,o,i))},a=function(t,r,o){var a=i(t,o),s=a.expected,d=a.remainingDelay;e.set(r,setTimeout(n,d,e,r,s,"interval"))},s=function(e,r,o){var a=i(e,o),s=a.expected,d=a.remainingDelay;t.set(r,setTimeout(n,d,t,r,s,"timeout"))};addEventListener("message",(function(e){var t=e.data;try{if("clear"===t.method){var i=t.id,n=t.params,d=n.timerId,c=n.timerType;if("interval"===c)r(d),postMessage({error:null,id:i});else{if("timeout"!==c)throw new Error(\'The given type "\'.concat(c,\'" is not supported\'));o(d),postMessage({error:null,id:i})}}else{if("set"!==t.method)throw new Error(\'The given method "\'.concat(t.method,\'" is not supported\'));var u=t.params,l=u.delay,p=u.now,m=u.timerId,v=u.timerType;if("interval"===v)a(l,m,p);else{if("timeout"!==v)throw new Error(\'The given type "\'.concat(v,\'" is not supported\'));s(l,m,p)}}}catch(e){postMessage({error:{message:e.message},id:t.id,result:null})}}))})?o.call(t,r,t,e):o)||(e.exports=i)}},t={};function r(o){var i=t[o];if(void 0!==i)return i.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(472)})()})();');e.clearInterval=function(e){return r().clearInterval(e)},e.clearTimeout=function(e){return r().clearTimeout(e)},e.setInterval=function(){var e;return(e=r()).setInterval.apply(e,arguments)},e.setTimeout=function(){var e;return(e=r()).setTimeout.apply(e,arguments)}},"object"==typeof e&&typeof t<"u"?n(e,wi()):"function"==typeof define&&r(704)?define(["exports","worker-timers-broker"],n):n((i=typeof globalThis<"u"?globalThis:i||self).workerTimers={},i.workerTimersBroker)})),Oi=S((e=>{x(),yt(),nt();var t=e&&e.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=e&&e.__importStar||function(){var e=function(t){return(e=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(t)};return function(i){if(i&&i.__esModule)return i;var n={};if(null!=i)for(var o=e(i),s=0;s<o.length;s++)"default"!==o[s]&&t(n,i,o[s]);return r(n,i),n}}();Object.defineProperty(e,"__esModule",{value:!0});var n=i(yi()),o=_i(),s={set:o.setInterval,clear:o.clearInterval},a={set:(e,t)=>setInterval(e,t),clear:e=>clearInterval(e)};e.default=e=>{switch(e){case"native":return a;case"worker":return s;default:return!n.default||n.isWebWorker||n.isReactNativeBrowser?a:s}}})),ji=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var r=t(Oi());e.default=class{get keepaliveTimeoutTimestamp(){return this._keepaliveTimeoutTimestamp}get intervalEvery(){return this._intervalEvery}get keepalive(){return this._keepalive}constructor(e,t){this.destroyed=!1,this.client=e,this.timer="object"==typeof t&&"set"in t&&"clear"in t?t:(0,r.default)(t),this.setKeepalive(e.options.keepalive)}clear(){this.timerId&&(this.timer.clear(this.timerId),this.timerId=null)}setKeepalive(e){if(e*=1e3,isNaN(e)||e<=0||e>2147483647)throw new Error("Keepalive value must be an integer between 0 and 2147483647. Provided value is ".concat(e));this._keepalive=e,this.reschedule(),this.client.log("KeepaliveManager: set keepalive to ".concat(e,"ms"))}destroy(){this.clear(),this.destroyed=!0}reschedule(){if(this.destroyed)return;this.clear(),this.counter=0;let e=Math.ceil(1.5*this._keepalive);this._keepaliveTimeoutTimestamp=Date.now()+e,this._intervalEvery=Math.ceil(this._keepalive/2),this.timerId=this.timer.set((()=>{this.destroyed||(this.counter+=1,2===this.counter?this.client.sendPing():this.counter>2&&this.client.onKeepaliveTimeout())}),this._intervalEvery)}}})),Si=S((e=>{x(),yt(),nt();var t=e&&e.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=e&&e.__importStar||function(){var e=function(t){return(e=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(t)};return function(i){if(i&&i.__esModule)return i;var n={};if(null!=i)for(var o=e(i),s=0;s<o.length;s++)"default"!==o[s]&&t(n,i,o[s]);return r(n,i),n}}(),n=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(vt()),s=n(Pr()),a=n(Mr()),l=vr(),c=n(Nr()),u=i(Lr()),h=n(xr()),d=n(Ur()),f=n(gi()),p=Fr(),b=mi(),g=n(ji()),m=i(yi()),y=globalThis.setImmediate||function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let i=t.shift();(0,p.nextTick)((()=>{i(...t)}))},v={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:3e4,clean:!0,resubscribe:!0,subscribeBatchSize:null,writeCache:!0,timerVariant:"auto"},w=class e extends b.TypedEventEmitter{static defaultId(){return"mqttjs_".concat(Math.random().toString(16).substr(2,8))}constructor(t,r){super(),this.options=r||{};for(let e in v)typeof this.options[e]>"u"?this.options[e]=v[e]:this.options[e]=r[e];this.log=this.options.log||(0,h.default)("mqttjs:client"),this.noop=this._noop.bind(this),this.log("MqttClient :: version:",e.VERSION),m.isWebWorker?this.log("MqttClient :: environment","webworker"):this.log("MqttClient :: environment",m.default?"browser":"node"),this.log("MqttClient :: options.protocol",r.protocol),this.log("MqttClient :: options.protocolVersion",r.protocolVersion),this.log("MqttClient :: options.username",r.username),this.log("MqttClient :: options.keepalive",r.keepalive),this.log("MqttClient :: options.reconnectPeriod",r.reconnectPeriod),this.log("MqttClient :: options.rejectUnauthorized",r.rejectUnauthorized),this.log("MqttClient :: options.properties.topicAliasMaximum",r.properties?r.properties.topicAliasMaximum:void 0),this.options.clientId="string"==typeof r.clientId?r.clientId:e.defaultId(),this.log("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=5===r.protocolVersion&&r.customHandleAcks?r.customHandleAcks:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t[3](null,0)},this.options.writeCache||(s.default.writeToStream.cacheNumbers=!1),this.streamBuilder=t,this.messageIdProvider=typeof this.options.messageIdProvider>"u"?new a.default:this.options.messageIdProvider,this.outgoingStore=r.outgoingStore||new d.default,this.incomingStore=r.incomingStore||new d.default,this.queueQoSZero=void 0===r.queueQoSZero||r.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.keepaliveManager=null,this.connected=!1,this.disconnecting=!1,this.reconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,r.properties&&r.properties.topicAliasMaximum>0&&(r.properties.topicAliasMaximum>65535?this.log("MqttClient :: options.properties.topicAliasMaximum is out of range"):this.topicAliasRecv=new o.default(r.properties.topicAliasMaximum)),this.on("connect",(()=>{let{queue:e}=this,t=()=>{let r=e.shift();this.log("deliver :: entry %o",r);let i=null;if(!r)return void this._resubscribe();i=r.packet,this.log("deliver :: call _sendPacket for %o",i);let n=!0;i.messageId&&0!==i.messageId&&(this.messageIdProvider.register(i.messageId)||(n=!1)),n?this._sendPacket(i,(e=>{r.cb&&r.cb(e),t()})):(this.log("messageId: %d has already used. The message is skipped and removed.",i.messageId),t())};this.log("connect :: sending queued packets"),t()})),this.on("close",(()=>{this.log("close :: connected set to `false`"),this.connected=!1,this.log("close :: clearing connackTimer"),clearTimeout(this.connackTimer),this._destroyKeepaliveManager(),this.topicAliasRecv&&this.topicAliasRecv.clear(),this.log("close :: calling _setupReconnect"),this._setupReconnect()})),this.options.manualConnect||(this.log("MqttClient :: setting up stream"),this.connect())}handleAuth(e,t){t()}handleMessage(e,t){t()}_nextId(){return this.messageIdProvider.allocate()}getLastMessageId(){return this.messageIdProvider.getLastAllocated()}connect(){var e;let t=new l.Writable,r=s.default.parser(this.options),i=null,n=[];this.log("connect :: calling method to clear reconnect"),this._clearReconnect(),this.disconnected&&!this.reconnecting&&(this.incomingStore=this.options.incomingStore||new d.default,this.outgoingStore=this.options.outgoingStore||new d.default,this.disconnecting=!1,this.disconnected=!1),this.log("connect :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),r.on("packet",(e=>{this.log("parser :: on packet push to packets array."),n.push(e)}));let o=()=>{this.log("work :: getting next packet in queue");let e=n.shift();if(e)this.log("work :: packet pulled from queue"),(0,f.default)(this,e,a);else{this.log("work :: no packets in queue");let e=i;i=null,this.log("work :: done flag is %s",!!e),e&&e()}},a=()=>{if(n.length)(0,p.nextTick)(o);else{let e=i;i=null,e()}};t._write=(e,t,n)=>{i=n,this.log("writable stream :: parsing buffer"),r.parse(e),o()};this.log("connect :: pipe stream to writable stream"),this.stream.pipe(t),this.stream.on("error",(e=>{this.log("streamErrorHandler :: error",e.message),e.code?(this.log("streamErrorHandler :: emitting error"),this.emit("error",e)):this.noop(e)})),this.stream.on("close",(()=>{this.log("(%s)stream :: on close",this.options.clientId),this._flushVolatile(),this.log("stream: emit close to MqttClient"),this.emit("close")})),this.log("connect: sending packet `connect`");let c={cmd:"connect",protocolId:this.options.protocolId,protocolVersion:this.options.protocolVersion,clean:this.options.clean,clientId:this.options.clientId,keepalive:this.options.keepalive,username:this.options.username,password:this.options.password,properties:this.options.properties};if(this.options.will&&(c.will=Object.assign(Object.assign({},this.options.will),{payload:null===(e=this.options.will)||void 0===e?void 0:e.payload})),this.topicAliasRecv&&(c.properties||(c.properties={}),this.topicAliasRecv&&(c.properties.topicAliasMaximum=this.topicAliasRecv.max)),this._writePacket(c),r.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return this.end((()=>this.emit("error",new Error("Packet has no Authentication Method")))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&"object"==typeof this.options.authPacket){let e=Object.assign({cmd:"auth",reasonCode:0},this.options.authPacket);this._writePacket(e)}}return this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout((()=>{this.log("!!connectTimeout hit!! Calling _cleanUp with force `true`"),this.emit("error",new Error("connack timeout")),this._cleanUp(!0)}),this.options.connectTimeout),this}publish(e,t,r,i){this.log("publish :: message `%s` to topic `%s`",t,e);let{options:n}=this;"function"==typeof r&&(i=r,r=null),r=r||{},r=Object.assign(Object.assign({},{qos:0,retain:!1,dup:!1}),r);let{qos:o,retain:s,dup:a,properties:l,cbStorePut:c}=r;if(this._checkDisconnecting(i))return this;let u=()=>{let r=0;if((1===o||2===o)&&(r=this._nextId(),null===r))return this.log("No messageId left"),!1;let u={cmd:"publish",topic:e,payload:t,qos:o,retain:s,messageId:r,dup:a};switch(5===n.protocolVersion&&(u.properties=l),this.log("publish :: qos",o),o){case 1:case 2:this.outgoing[u.messageId]={volatile:!1,cb:i||this.noop},this.log("MqttClient:publish: packet cmd: %s",u.cmd),this._sendPacket(u,void 0,c);break;default:this.log("MqttClient:publish: packet cmd: %s",u.cmd),this._sendPacket(u,i,c)}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!u())&&this._storeProcessingQueue.push({invoke:u,cbStorePut:r.cbStorePut,callback:i}),this}publishAsync(e,t,r){return new Promise(((i,n)=>{this.publish(e,t,r,((e,t)=>{e?n(e):i(t)}))}))}subscribe(e,t,r){let i=this.options.protocolVersion;"function"==typeof t&&(r=t),r=r||this.noop;let n=!1,o=[];"string"==typeof e?o=e=[e]:Array.isArray(e)?o=e:"object"==typeof e&&(n=e.resubscribe,delete e.resubscribe,o=Object.keys(e));let s=u.validateTopics(o);if(null!==s)return y(r,new Error("Invalid topic ".concat(s))),this;if(this._checkDisconnecting(r))return this.log("subscribe: discconecting true"),this;let a={qos:0};5===i&&(a.nl=!1,a.rap=!1,a.rh=0);let l=(t=Object.assign(Object.assign({},a),t)).properties,c=[],h=(e,r)=>{if(r=r||t,!Object.prototype.hasOwnProperty.call(this._resubscribeTopics,e)||this._resubscribeTopics[e].qos<r.qos||n){let t={topic:e,qos:r.qos};5===i&&(t.nl=r.nl,t.rap=r.rap,t.rh=r.rh,t.properties=l),this.log("subscribe: pushing topic `%s` and qos `%s` to subs list",t.topic,t.qos),c.push(t)}};if(Array.isArray(e)?e.forEach((e=>{this.log("subscribe: array topic %s",e),h(e)})):Object.keys(e).forEach((t=>{this.log("subscribe: object topic %s, %o",t,e[t]),h(t,e[t])})),!c.length)return r(null,[]),this;let d=(e,t)=>{let r={cmd:"subscribe",subscriptions:e,messageId:t};if(l&&(r.properties=l),this.options.resubscribe){this.log("subscribe :: resubscribe true");let t=[];e.forEach((e=>{if(this.options.reconnectPeriod>0){let r={qos:e.qos};5===i&&(r.nl=e.nl||!1,r.rap=e.rap||!1,r.rh=e.rh||0,r.properties=e.properties),this._resubscribeTopics[e.topic]=r,t.push(e.topic)}})),this.messageIdToTopic[r.messageId]=t}let n=new Promise(((t,i)=>{this.outgoing[r.messageId]={volatile:!0,cb(r,n){if(!r){let{granted:t}=n;for(let r=0;r<t.length;r+=1)e[r].qos=t[r]}r?i(new p.ErrorWithSubackPacket(r.message,n)):t(n)}}}));return this.log("subscribe :: call _sendPacket"),this._sendPacket(r),n},f=()=>{var e;let t=null!==(e=this.options.subscribeBatchSize)&&void 0!==e?e:c.length,i=[];for(let r=0;r<c.length;r+=t){let e=c.slice(r,r+t),n=this._nextId();if(null===n)return this.log("No messageId left"),!1;i.push(d(e,n))}return Promise.all(i).then((e=>{r(null,c,e.at(-1))})).catch((e=>{r(e,c,e.packet)})),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!f())&&this._storeProcessingQueue.push({invoke:f,callback:r}),this}subscribeAsync(e,t){return new Promise(((r,i)=>{this.subscribe(e,t,((e,t)=>{e?i(e):r(t)}))}))}unsubscribe(e,t,r){"string"==typeof e&&(e=[e]),"function"==typeof t&&(r=t),r=r||this.noop;let i=u.validateTopics(e);if(null!==i)return y(r,new Error("Invalid topic ".concat(i))),this;if(this._checkDisconnecting(r))return this;let n=()=>{let i=this._nextId();if(null===i)return this.log("No messageId left"),!1;let n={cmd:"unsubscribe",messageId:i,unsubscriptions:[]};return"string"==typeof e?n.unsubscriptions=[e]:Array.isArray(e)&&(n.unsubscriptions=e),this.options.resubscribe&&n.unsubscriptions.forEach((e=>{delete this._resubscribeTopics[e]})),"object"==typeof t&&t.properties&&(n.properties=t.properties),this.outgoing[n.messageId]={volatile:!0,cb:r},this.log("unsubscribe: call _sendPacket"),this._sendPacket(n),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!n())&&this._storeProcessingQueue.push({invoke:n,callback:r}),this}unsubscribeAsync(e,t){return new Promise(((r,i)=>{this.unsubscribe(e,t,((e,t)=>{e?i(e):r(t)}))}))}end(e,t,r){this.log("end :: (%s)",this.options.clientId),(null==e||"boolean"!=typeof e)&&(r=r||t,t=e,e=!1),"object"!=typeof t&&(r=r||t,t=null),this.log("end :: cb? %s",!!r),(!r||"function"!=typeof r)&&(r=this.noop);let i=()=>{this.log("end :: closeStores: closing incoming and outgoing stores"),this.disconnected=!0,this.incomingStore.close((e=>{this.outgoingStore.close((t=>{if(this.log("end :: closeStores: emitting end"),this.emit("end"),r){let i=e||t;this.log("end :: closeStores: invoking callback with args"),r(i)}}))})),this._deferredReconnect?this._deferredReconnect():(0===this.options.reconnectPeriod||this.options.manualConnect)&&(this.disconnecting=!1)},n=()=>{this.log("end :: (%s) :: finish :: calling _cleanUp with force %s",this.options.clientId,e),this._cleanUp(e,(()=>{this.log("end :: finish :: calling process.nextTick on closeStores"),(0,p.nextTick)(i)}),t)};return this.disconnecting?(r(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(this.log("end :: (%s) :: calling finish in 10ms once outgoing is empty",this.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,n,10))):(this.log("end :: (%s) :: immediately calling finish",this.options.clientId),n()),this)}endAsync(e,t){return new Promise(((r,i)=>{this.end(e,t,(e=>{e?i(e):r()}))}))}removeOutgoingMessage(e){if(this.outgoing[e]){let{cb:t}=this.outgoing[e];this._removeOutgoingAndStoreMessage(e,(()=>{t(new Error("Message removed"))}))}return this}reconnect(e){this.log("client reconnect");let t=()=>{e?(this.options.incomingStore=e.incomingStore,this.options.outgoingStore=e.outgoingStore):(this.options.incomingStore=null,this.options.outgoingStore=null),this.incomingStore=this.options.incomingStore||new d.default,this.outgoingStore=this.options.outgoingStore||new d.default,this.disconnecting=!1,this.disconnected=!1,this._deferredReconnect=null,this._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=t:t(),this}_flushVolatile(){this.outgoing&&(this.log("_flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(this.outgoing).forEach((e=>{this.outgoing[e].volatile&&"function"==typeof this.outgoing[e].cb&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])})))}_flush(){this.outgoing&&(this.log("_flush: queue exists? %b",!!this.outgoing),Object.keys(this.outgoing).forEach((e=>{"function"==typeof this.outgoing[e].cb&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])})))}_removeTopicAliasAndRecoverTopicName(e){let t;e.properties&&(t=e.properties.topicAlias);let r=e.topic.toString();if(this.log("_removeTopicAliasAndRecoverTopicName :: alias %d, topic %o",t,r),0===r.length){if(typeof t>"u")return new Error("Unregistered Topic Alias");if(r=this.topicAliasSend.getTopicByAlias(t),typeof r>"u")return new Error("Unregistered Topic Alias");e.topic=r}t&&delete e.properties.topicAlias}_checkDisconnecting(e){return this.disconnecting&&(e&&e!==this.noop?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting}_reconnect(){this.log("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end((()=>{this.connect()})),this.log("client already connected. disconnecting first.")):(this.log("_reconnect: calling connect"),this.connect())}_setupReconnect(){!this.disconnecting&&!this.reconnectTimer&&this.options.reconnectPeriod>0?(this.reconnecting||(this.log("_setupReconnect :: emit `offline` state"),this.emit("offline"),this.log("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),this.log("_setupReconnect :: setting reconnectTimer for %d ms",this.options.reconnectPeriod),this.reconnectTimer=setInterval((()=>{this.log("reconnectTimer :: reconnect triggered!"),this._reconnect()}),this.options.reconnectPeriod)):this.log("_setupReconnect :: doing nothing...")}_clearReconnect(){this.log("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)}_cleanUp(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t&&(this.log("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",t)),this.log("_cleanUp :: forced? %s",e),e)0===this.options.reconnectPeriod&&this.options.clean&&this._flush(),this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{let e=Object.assign({cmd:"disconnect"},r);this.log("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(e,(()=>{this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),y((()=>{this.stream.end((()=>{this.log("_cleanUp :: (%s) :: stream destroyed",this.options.clientId)}))}))}))}!this.disconnecting&&!this.reconnecting&&(this.log("_cleanUp :: client not disconnecting/reconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),this._destroyKeepaliveManager(),t&&!this.connected&&(this.log("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",t),t())}_storeAndSend(e,t,r){this.log("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let i,n=e;if("publish"===n.cmd&&(n=(0,c.default)(e),i=this._removeTopicAliasAndRecoverTopicName(n),i))return t&&t(i);this.outgoingStore.put(n,(i=>{if(i)return t&&t(i);r(),this._writePacket(e,t)}))}_applyTopicAlias(e){if(5===this.options.protocolVersion&&"publish"===e.cmd){let t;e.properties&&(t=e.properties.topicAlias);let r=e.topic.toString();if(this.topicAliasSend)if(t){if(0!==r.length&&(this.log("applyTopicAlias :: register topic: %s - alias: %d",r,t),!this.topicAliasSend.put(r,t)))return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",r,t),new Error("Sending Topic Alias out of range")}else 0!==r.length&&(this.options.autoAssignTopicAlias?(t=this.topicAliasSend.getAliasByTopic(r),t?(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:t}),this.log("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",r,t)):(t=this.topicAliasSend.getLruAlias(),this.topicAliasSend.put(r,t),e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:t}),this.log("applyTopicAlias :: auto assign topic: %s - alias: %d",r,t))):this.options.autoUseTopicAlias&&(t=this.topicAliasSend.getAliasByTopic(r),t&&(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:t}),this.log("applyTopicAlias :: auto use topic: %s - alias: %d",r,t))));else if(t)return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",r,t),new Error("Sending Topic Alias out of range")}}_noop(e){this.log("noop ::",e)}_writePacket(e,t){this.log("_writePacket :: packet: %O",e),this.log("_writePacket :: emitting `packetsend`"),this.emit("packetsend",e),this.log("_writePacket :: writing to stream");let r=s.default.writeToStream(e,this.stream,this.options);this.log("_writePacket :: writeToStream result %s",r),!r&&t&&t!==this.noop?(this.log("_writePacket :: handle events on `drain` once through callback."),this.stream.once("drain",t)):t&&(this.log("_writePacket :: invoking cb"),t())}_sendPacket(e,t,r,i){this.log("_sendPacket :: (%s) ::  start",this.options.clientId),r=r||this.noop,t=t||this.noop;let n=this._applyTopicAlias(e);if(n)t(n);else{if(!this.connected)return"auth"===e.cmd?void this._writePacket(e,t):(this.log("_sendPacket :: client not connected. Storing packet offline."),void this._storePacket(e,t,r));if(i)this._writePacket(e,t);else{switch(e.cmd){case"publish":break;case"pubrel":return void this._storeAndSend(e,t,r);default:return void this._writePacket(e,t)}switch(e.qos){case 2:case 1:this._storeAndSend(e,t,r);break;default:this._writePacket(e,t)}this.log("_sendPacket :: (%s) ::  end",this.options.clientId)}}}_storePacket(e,t,r){this.log("_storePacket :: packet: %o",e),this.log("_storePacket :: cb? %s",!!t),r=r||this.noop;let i=e;if("publish"===i.cmd){i=(0,c.default)(e);let r=this._removeTopicAliasAndRecoverTopicName(i);if(r)return t&&t(r)}let n=i.qos||0;0===n&&this.queueQoSZero||"publish"!==i.cmd?this.queue.push({packet:i,cb:t}):n>0?(t=this.outgoing[i.messageId]?this.outgoing[i.messageId].cb:null,this.outgoingStore.put(i,(e=>{if(e)return t&&t(e);r()}))):t&&t(new Error("No connection to broker"))}_setupKeepaliveManager(){this.log("_setupKeepaliveManager :: keepalive %d (seconds)",this.options.keepalive),!this.keepaliveManager&&this.options.keepalive&&(this.keepaliveManager=new g.default(this,this.options.timerVariant))}_destroyKeepaliveManager(){this.keepaliveManager&&(this.log("_destroyKeepaliveManager :: destroying keepalive manager"),this.keepaliveManager.destroy(),this.keepaliveManager=null)}reschedulePing(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.keepaliveManager&&this.options.keepalive&&(e||this.options.reschedulePings)&&this._reschedulePing()}_reschedulePing(){this.log("_reschedulePing :: rescheduling ping"),this.keepaliveManager.reschedule()}sendPing(){this.log("_sendPing :: sending pingreq"),this._sendPacket({cmd:"pingreq"})}onKeepaliveTimeout(){this.emit("error",new Error("Keepalive timeout")),this.log("onKeepaliveTimeout :: calling _cleanUp with force true"),this._cleanUp(!0)}_resubscribe(){this.log("_resubscribe");let e=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||this.options.protocolVersion>=4&&!this.connackPacket.sessionPresent)&&e.length>0)if(this.options.resubscribe)if(5===this.options.protocolVersion){this.log("_resubscribe: protocolVersion 5");for(let t=0;t<e.length;t++){let r={};r[e[t]]=this._resubscribeTopics[e[t]],r.resubscribe=!0,this.subscribe(r,{properties:r[e[t]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1}_onConnect(e){if(this.disconnected)return void this.emit("connect",e);this.connackPacket=e,this.messageIdProvider.clear(),this._setupKeepaliveManager(),this.connected=!0;let t=()=>{let r=this.outgoingStore.createStream(),i=()=>{r.destroy(),r=null,this._flushStoreProcessingQueue(),n()},n=()=>{this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={}};this.once("close",i),r.on("error",(e=>{n(),this._flushStoreProcessingQueue(),this.removeListener("close",i),this.emit("error",e)}));let o=()=>{if(!r)return;let e,t=r.read(1);t?(this._storeProcessing=!0,this._packetIdsDuringStoreProcessing[t.messageId]?o():this.disconnecting||this.reconnectTimer?r.destroy&&r.destroy():(e=this.outgoing[t.messageId]?this.outgoing[t.messageId].cb:null,this.outgoing[t.messageId]={volatile:!1,cb(t,r){e&&e(t,r),o()}},this._packetIdsDuringStoreProcessing[t.messageId]=!0,this.messageIdProvider.register(t.messageId)?this._sendPacket(t,void 0,void 0,!0):this.log("messageId: %d has already used.",t.messageId))):r.once("readable",o)};r.on("end",(()=>{let r=!0;for(let e in this._packetIdsDuringStoreProcessing)if(!this._packetIdsDuringStoreProcessing[e]){r=!1;break}this.removeListener("close",i),r?(n(),this._invokeAllStoreProcessingQueue(),this.emit("connect",e)):t()})),o()};t()}_invokeStoreProcessingQueue(){if(!this._storeProcessing&&this._storeProcessingQueue.length>0){let e=this._storeProcessingQueue[0];if(e&&e.invoke())return this._storeProcessingQueue.shift(),!0}return!1}_invokeAllStoreProcessingQueue(){for(;this._invokeStoreProcessingQueue(););}_flushStoreProcessingQueue(){for(let e of this._storeProcessingQueue)e.cbStorePut&&e.cbStorePut(new Error("Connection closed")),e.callback&&e.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)}_removeOutgoingAndStoreMessage(e,t){delete this.outgoing[e],this.outgoingStore.del({messageId:e},((r,i)=>{t(r,i),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue()}))}};w.VERSION=p.MQTTJS_VERSION,e.default=w})),Ei=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t=di();e.default=class{constructor(){this.numberAllocator=new t.NumberAllocator(1,65535)}allocate(){return this.lastId=this.numberAllocator.alloc(),this.lastId}getLastAllocated(){return this.lastId}register(e){return this.numberAllocator.use(e)}deallocate(e){this.numberAllocator.free(e)}clear(){this.numberAllocator.clear()}}}));function Ai(e){throw new RangeError(Ri[e])}function xi(e,t){let r=e.split("@"),i="";return r.length>1&&(i=r[0]+"@",e=r[1]),i+function(e,t){let r=[],i=e.length;for(;i--;)r[i]=t(e[i]);return r}((e=e.replace(Ci,".")).split("."),t).join(".")}function ki(e){let t=[],r=0,i=e.length;for(;r<i;){let n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<i){let i=e.charCodeAt(r++);56320==(64512&i)?t.push(((1023&n)<<10)+(1023&i)+65536):(t.push(n),r--)}else t.push(n)}return t}var Ii,Ti,Ci,Ri,Pi,Mi,Bi,Ni,Li,Ui,Wi,Di=j((()=>{x(),yt(),nt(),Ii=/^xn--/,Ti=/[^\0-\x7E]/,Ci=/[\x2E\u3002\uFF0E\uFF61]/g,Ri={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},Pi=Math.floor,Mi=String.fromCharCode,Bi=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},Ni=function(e,t,r){let i=0;for(e=r?Pi(e/700):e>>1,e+=Pi(e/t);e>455;i+=36)e=Pi(e/35);return Pi(i+36*e/(e+38))},Ui=function(e){let t=[],r=(e=ki(e)).length,i=128,n=0,o=72;for(let l of e)l<128&&t.push(Mi(l));let s=t.length,a=s;for(s&&t.push("-");a<r;){let r=2147483647;for(let t of e)t>=i&&t<r&&(r=t);let l=a+1;r-i>Pi((2147483647-n)/l)&&Ai("overflow"),n+=(r-i)*l,i=r;for(let c of e)if(c<i&&++n>2147483647&&Ai("overflow"),c==i){let e=n;for(let r=36;;r+=36){let i=r<=o?1:r>=o+26?26:r-o;if(e<i)break;let n=e-i,s=36-i;t.push(Mi(Bi(i+n%s,0))),e=Pi(n/s)}t.push(Mi(Bi(e,0))),o=Ni(n,l,a==s),n=0,++a}++n,++i}return t.join("")},Wi={version:"2.1.0",ucs2:{decode:ki,encode:e=>String.fromCodePoint(...e)},decode:Li=function(e){let t=[],r=e.length,i=0,n=128,o=72,s=e.lastIndexOf("-");s<0&&(s=0);for(let l=0;l<s;++l)e.charCodeAt(l)>=128&&Ai("not-basic"),t.push(e.charCodeAt(l));for(let l=s>0?s+1:0;l<r;){let s=i;for(let t=1,n=36;;n+=36){l>=r&&Ai("invalid-input");let s=(a=e.charCodeAt(l++))-48<10?a-22:a-65<26?a-65:a-97<26?a-97:36;(s>=36||s>Pi((2147483647-i)/t))&&Ai("overflow"),i+=s*t;let c=n<=o?1:n>=o+26?26:n-o;if(s<c)break;let u=36-c;t>Pi(2147483647/u)&&Ai("overflow"),t*=u}let c=t.length+1;o=Ni(i-s,c,0==s),Pi(i/c)>2147483647-n&&Ai("overflow"),n+=Pi(i/c),i%=c,t.splice(i++,0,n)}var a;return String.fromCodePoint(...t)},encode:Ui,toASCII:function(e){return xi(e,(function(e){return Ti.test(e)?"xn--"+Ui(e):e}))},toUnicode:function(e){return xi(e,(function(e){return Ii.test(e)?Li(e.slice(4).toLowerCase()):e}))}},Wi.encode,Wi.toASCII,Wi.toUnicode,Wi.ucs2,Wi.version}));function Fi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var qi,Hi,Vi,zi,Gi=j((()=>{x(),yt(),nt(),qi=function(e,t,r,i){t=t||"&",r=r||"=";var n={};if("string"!=typeof e||0===e.length)return n;var o=/\+/g;e=e.split(t);var s=1e3;i&&"number"==typeof i.maxKeys&&(s=i.maxKeys);var a=e.length;s>0&&a>s&&(a=s);for(var l=0;l<a;++l){var c,u,h,d,f=e[l].replace(o,"%20"),p=f.indexOf(r);p>=0?(c=f.substr(0,p),u=f.substr(p+1)):(c=f,u=""),h=decodeURIComponent(c),d=decodeURIComponent(u),Fi(n,h)?Array.isArray(n[h])?n[h].push(d):n[h]=[n[h],d]:n[h]=d}return n},Hi=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}},Vi=function(e,t,r,i){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?Object.keys(e).map((function(i){var n=encodeURIComponent(Hi(i))+r;return Array.isArray(e[i])?e[i].map((function(e){return n+encodeURIComponent(Hi(e))})).join(t):n+encodeURIComponent(Hi(e[i]))})).join(t):i?encodeURIComponent(Hi(i))+r+encodeURIComponent(Hi(e)):""},(zi={}).decode=zi.parse=qi,zi.encode=zi.stringify=Vi,zi.decode,zi.encode,zi.parse,zi.stringify}));function Ki(){throw new Error("setTimeout has not been defined")}function Qi(){throw new Error("clearTimeout has not been defined")}function Yi(e){if(tn===setTimeout)return setTimeout(e,0);if((tn===Ki||!tn)&&setTimeout)return tn=setTimeout,setTimeout(e,0);try{return tn(e,0)}catch(t){try{return tn.call(null,e,0)}catch(r){return tn.call(this||nn,e,0)}}}function Ji(){ln&&sn&&(ln=!1,sn.length?an=sn.concat(an):cn=-1,an.length&&Xi())}function Xi(){if(!ln){var e=Yi(Ji);ln=!0;for(var t=an.length;t;){for(sn=an,an=[];++cn<t;)sn&&sn[cn].run();cn=-1,t=an.length}sn=null,ln=!1,function(e){if(rn===clearTimeout)return clearTimeout(e);if((rn===Qi||!rn)&&clearTimeout)return rn=clearTimeout,clearTimeout(e);try{rn(e)}catch(t){try{return rn.call(null,e)}catch(r){return rn.call(this||nn,e)}}}(e)}}function $i(e,t){(this||nn).fun=e,(this||nn).array=t}function Zi(){}var en,tn,rn,nn,on,sn,an,ln,cn,un,hn=j((()=>{x(),yt(),nt(),nn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,on=en={},function(){try{tn="function"==typeof setTimeout?setTimeout:Ki}catch(e){tn=Ki}try{rn="function"==typeof clearTimeout?clearTimeout:Qi}catch(t){rn=Qi}}(),an=[],ln=!1,cn=-1,on.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];an.push(new $i(e,t)),1!==an.length||ln||Yi(Xi)},$i.prototype.run=function(){(this||nn).fun.apply(null,(this||nn).array)},on.title="browser",on.browser=!0,on.env={},on.argv=[],on.version="",on.versions={},on.on=Zi,on.addListener=Zi,on.once=Zi,on.off=Zi,on.removeListener=Zi,on.removeAllListeners=Zi,on.emit=Zi,on.prependListener=Zi,on.prependOnceListener=Zi,on.listeners=function(e){return[]},on.binding=function(e){throw new Error("process.binding is not supported")},on.cwd=function(){return"/"},on.chdir=function(e){throw new Error("process.chdir is not supported")},on.umask=function(){return 0},un=en,un.argv,un.binding,un.browser,un.chdir,un.cwd,un.emit,un.env,un.listeners,un.nextTick,un.off,un.on,un.once,un.prependListener,un.prependOnceListener,un.removeAllListeners,un.removeListener,un.title,un.umask,un.version,un.versions}));function dn(){if(pn)return fn;pn=!0;var e,t,r=fn={};function i(){throw new Error("setTimeout has not been defined")}function n(){throw new Error("clearTimeout has not been defined")}function o(t){if(e===setTimeout)return setTimeout(t,0);if((e===i||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(r){try{return e.call(null,t,0)}catch(n){return e.call(this||bn,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:i}catch(r){e=i}try{t="function"==typeof clearTimeout?clearTimeout:n}catch(o){t=n}}();var s,a=[],l=!1,c=-1;function u(){!l||!s||(l=!1,s.length?a=s.concat(a):c=-1,a.length&&h())}function h(){if(!l){var e=o(u);l=!0;for(var r=a.length;r;){for(s=a,a=[];++c<r;)s&&s[c].run();c=-1,r=a.length}s=null,l=!1,function(e){if(t===clearTimeout)return clearTimeout(e);if((t===n||!t)&&clearTimeout)return t=clearTimeout,clearTimeout(e);try{t(e)}catch(r){try{return t.call(null,e)}catch(i){return t.call(this||bn,e)}}}(e)}}function d(e,t){(this||bn).fun=e,(this||bn).array=t}function f(){}return r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];a.push(new d(e,t)),1===a.length&&!l&&o(h)},d.prototype.run=function(){(this||bn).fun.apply(null,(this||bn).array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=f,r.addListener=f,r.once=f,r.off=f,r.removeListener=f,r.removeAllListeners=f,r.emit=f,r.prependListener=f,r.prependOnceListener=f,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0},fn}var fn,pn,bn,gn,mn=j((()=>{x(),yt(),nt(),fn={},pn=!1,bn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,(gn=dn()).platform="browser",gn.addListener,gn.argv,gn.binding,gn.browser,gn.chdir,gn.cwd,gn.emit,gn.env,gn.listeners,gn.nextTick,gn.off,gn.on,gn.once,gn.prependListener,gn.prependOnceListener,gn.removeAllListeners,gn.removeListener,gn.title,gn.umask,gn.version,gn.versions}));var yn,vn,wn,_n=j((()=>{x(),yt(),nt(),mn(),yn={},vn=!1,wn=function(){if(vn)return yn;vn=!0;var e=gn;function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,i="",n=0,o=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)r=e.charCodeAt(a);else{if(47===r)break;r=47}if(47===r){if(o!==a-1&&1!==s)if(o!==a-1&&2===s){if(i.length<2||2!==n||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){var l=i.lastIndexOf("/");if(l!==i.length-1){-1===l?(i="",n=0):n=(i=i.slice(0,l)).length-1-i.lastIndexOf("/"),o=a,s=0;continue}}else if(2===i.length||1===i.length){i="",n=0,o=a,s=0;continue}t&&(i.length>0?i+="/..":i="..",n=2)}else i.length>0?i+="/"+e.slice(o+1,a):i=e.slice(o+1,a),n=a-o-1;o=a,s=0}else 46===r&&-1!==s?++s:s=-1}return i}var i={resolve:function(){for(var i,n="",o=!1,s=arguments.length-1;s>=-1&&!o;s--){var a;s>=0?a=arguments[s]:(void 0===i&&(i=e.cwd()),a=i),t(a),0!==a.length&&(n=a+"/"+n,o=47===a.charCodeAt(0))}return n=r(n,!o),o?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(e){if(t(e),0===e.length)return".";var i=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0===(e=r(e,!i)).length&&!i&&(e="."),e.length>0&&n&&(e+="/"),i?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var n=arguments[r];t(n),n.length>0&&(void 0===e?e=n:e+="/"+n)}return void 0===e?".":i.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=i.resolve(e))===(r=i.resolve(r)))return"";for(var n=1;n<e.length&&47===e.charCodeAt(n);++n);for(var o=e.length,s=o-n,a=1;a<r.length&&47===r.charCodeAt(a);++a);for(var l=r.length-a,c=s<l?s:l,u=-1,h=0;h<=c;++h){if(h===c){if(l>c){if(47===r.charCodeAt(a+h))return r.slice(a+h+1);if(0===h)return r.slice(a+h)}else s>c&&(47===e.charCodeAt(n+h)?u=h:0===h&&(u=0));break}var d=e.charCodeAt(n+h);if(d!==r.charCodeAt(a+h))break;47===d&&(u=h)}var f="";for(h=n+u+1;h<=o;++h)(h===o||47===e.charCodeAt(h))&&(0===f.length?f+="..":f+="/..");return f.length>0?f+r.slice(a+u):(a+=u,47===r.charCodeAt(a)&&++a,r.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),i=47===r,n=-1,o=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!o){n=s;break}}else o=!1;return-1===n?i?"/":".":i&&1===n?"//":e.slice(0,n)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw new TypeError('"ext" argument must be a string');t(e);var i,n=0,o=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var a=r.length-1,l=-1;for(i=e.length-1;i>=0;--i){var c=e.charCodeAt(i);if(47===c){if(!s){n=i+1;break}}else-1===l&&(s=!1,l=i+1),a>=0&&(c===r.charCodeAt(a)?-1===--a&&(o=i):(a=-1,o=l))}return n===o?o=l:-1===o&&(o=e.length),e.slice(n,o)}for(i=e.length-1;i>=0;--i)if(47===e.charCodeAt(i)){if(!s){n=i+1;break}}else-1===o&&(s=!1,o=i+1);return-1===o?"":e.slice(n,o)},extname:function(e){t(e);for(var r=-1,i=0,n=-1,o=!0,s=0,a=e.length-1;a>=0;--a){var l=e.charCodeAt(a);if(47!==l)-1===n&&(o=!1,n=a+1),46===l?-1===r?r=a:1!==s&&(s=1):-1!==r&&(s=-1);else if(!o){i=a+1;break}}return-1===r||-1===n||0===s||1===s&&r===n-1&&r===i+1?"":e.slice(r,n)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var r=t.dir||t.root,i=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+i:r+e+i:i}("/",e)},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var i,n=e.charCodeAt(0),o=47===n;o?(r.root="/",i=1):i=0;for(var s=-1,a=0,l=-1,c=!0,u=e.length-1,h=0;u>=i;--u)if(47!==(n=e.charCodeAt(u)))-1===l&&(c=!1,l=u+1),46===n?-1===s?s=u:1!==h&&(h=1):-1!==s&&(h=-1);else if(!c){a=u+1;break}return-1===s||-1===l||0===h||1===h&&s===l-1&&s===a+1?-1!==l&&(r.base=r.name=0===a&&o?e.slice(1,l):e.slice(a,l)):(0===a&&o?(r.name=e.slice(1,s),r.base=e.slice(1,l)):(r.name=e.slice(a,s),r.base=e.slice(a,l)),r.ext=e.slice(s,l)),a>0?r.dir=e.slice(0,a-1):o&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};return i.posix=i,yn=i}()})),On={};function jn(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function Sn(e,t,r){if(e&&Cn.isObject(e)&&e instanceof jn)return e;var i=new jn;return i.parse(e,t,r),i}function En(e){if("string"==typeof e)e=new URL(e);else if(!(e instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if("file:"!==e.protocol)throw new Deno.errors.InvalidData("invalid url scheme");return Zn?function(e){let t=e.hostname,r=e.pathname;for(let i=0;i<r.length;i++)if("%"===r[i]){let e=r.codePointAt(i+2)||32;if("2"===r[i+1]&&102===e||"5"===r[i+1]&&99===e)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(eo,"\\"),r=decodeURIComponent(r),""!==t)return"\\\\".concat(t).concat(r);{let e=32|r.codePointAt(1),t=r[2];if(e<Xn||e>$n||":"!==t)throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}(e):function(e){if(""!==e.hostname)throw new Deno.errors.InvalidData("invalid file url hostname");let t=e.pathname;for(let r=0;r<t.length;r++)if("%"===t[r]){let e=t.codePointAt(r+2)||32;if("2"===t[r+1]&&102===e)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(t)}(e)}function An(e){let t=Kn.resolve(e),r=e.charCodeAt(e.length-1);(r===Jn||Zn&&r===Yn)&&t[t.length-1]!==Kn.sep&&(t+="/");let i=new URL("file://");return t.includes("%")&&(t=t.replace(to,"%25")),!Zn&&t.includes("\\")&&(t=t.replace(ro,"%5C")),t.includes("\n")&&(t=t.replace(io,"%0A")),t.includes("\r")&&(t=t.replace(no,"%0D")),t.includes("\t")&&(t=t.replace(oo,"%09")),i.pathname=t,i}function xn(e){if("string"==typeof e)e=new URL(e);else if(!(e instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if("file:"!==e.protocol)throw new Deno.errors.InvalidData("invalid url scheme");return yo?function(e){let t=e.hostname,r=e.pathname;for(let i=0;i<r.length;i++)if("%"===r[i]){let e=r.codePointAt(i+2)||32;if("2"===r[i+1]&&102===e||"5"===r[i+1]&&99===e)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(vo,"\\"),r=decodeURIComponent(r),""!==t)return"\\\\".concat(t).concat(r);{let e=32|r.codePointAt(1),t=r[2];if(e<go||e>mo||":"!==t)throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}(e):function(e){if(""!==e.hostname)throw new Deno.errors.InvalidData("invalid file url hostname");let t=e.pathname;for(let r=0;r<t.length;r++)if("%"===t[r]){let e=t.codePointAt(r+2)||32;if("2"===t[r+1]&&102===e)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(t)}(e)}function kn(e){let t=wn.resolve(e),r=e.charCodeAt(e.length-1);(r===bo||yo&&r===po)&&t[t.length-1]!==wn.sep&&(t+="/");let i=new URL("file://");return t.includes("%")&&(t=t.replace(wo,"%25")),!yo&&t.includes("\\")&&(t=t.replace(_o,"%5C")),t.includes("\n")&&(t=t.replace(Oo,"%0A")),t.includes("\r")&&(t=t.replace(jo,"%0D")),t.includes("\t")&&(t=t.replace(So,"%09")),i.pathname=t,i}E(On,{URL:()=>fo,Url:()=>ao,default:()=>In,fileURLToPath:()=>xn,format:()=>lo,parse:()=>ho,pathToFileURL:()=>kn,resolve:()=>co,resolveObject:()=>uo});var In,Tn,Cn,Rn,Pn,Mn,Bn,Nn,Ln,Un,Wn,Dn,Fn,qn,Hn,Vn,zn,Gn,Kn,Qn,Yn,Jn,Xn,$n,Zn,eo,to,ro,io,no,oo,so,ao,lo,co,uo,ho,fo,po,bo,go,mo,yo,vo,wo,_o,Oo,jo,So,Eo=j((()=>{x(),yt(),nt(),Di(),Gi(),hn(),_n(),mn(),Tn=Wi,Cn={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}},(In={}).parse=Sn,In.resolve=function(e,t){return Sn(e,!1,!0).resolve(t)},In.resolveObject=function(e,t){return e?Sn(e,!1,!0).resolveObject(t):t},In.format=function(e){return Cn.isString(e)&&(e=Sn(e)),e instanceof jn?e.format():jn.prototype.format.call(e)},In.Url=jn,Rn=/^([a-z0-9.+-]+:)/i,Pn=/:[0-9]*$/,Mn=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,Bn=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),Nn=["'"].concat(Bn),Ln=["%","/","?",";","#"].concat(Nn),Un=["/","?","#"],Wn=/^[+a-z0-9A-Z_-]{0,63}$/,Dn=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Fn={javascript:!0,"javascript:":!0},qn={javascript:!0,"javascript:":!0},Hn={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},Vn=zi,jn.prototype.parse=function(e,t,r){if(!Cn.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var i=e.indexOf("?"),n=-1!==i&&i<e.indexOf("#")?"?":"#",o=e.split(n);o[0]=o[0].replace(/\\/g,"/");var s=e=o.join(n);if(s=s.trim(),!r&&1===e.split("#").length){var a=Mn.exec(s);if(a)return this.path=s,this.href=s,this.pathname=a[1],a[2]?(this.search=a[2],this.query=t?Vn.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var l=Rn.exec(s);if(l){var c=(l=l[0]).toLowerCase();this.protocol=c,s=s.substr(l.length)}if(r||l||s.match(/^\/\/[^@\/]+@[^@\/]+/)){var u="//"===s.substr(0,2);!u||l&&qn[l]||(s=s.substr(2),this.slashes=!0)}if(!qn[l]&&(u||l&&!Hn[l])){for(var h,d,f=-1,p=0;p<Un.length;p++)-1!==(b=s.indexOf(Un[p]))&&(-1===f||b<f)&&(f=b);for(-1!==(d=-1===f?s.lastIndexOf("@"):s.lastIndexOf("@",f))&&(h=s.slice(0,d),s=s.slice(d+1),this.auth=decodeURIComponent(h)),f=-1,p=0;p<Ln.length;p++){var b;-1!==(b=s.indexOf(Ln[p]))&&(-1===f||b<f)&&(f=b)}-1===f&&(f=s.length),this.host=s.slice(0,f),s=s.slice(f),this.parseHost(),this.hostname=this.hostname||"";var g="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!g)for(var m=this.hostname.split(/\./),y=(p=0,m.length);p<y;p++){var v=m[p];if(v&&!v.match(Wn)){for(var w="",_=0,O=v.length;_<O;_++)v.charCodeAt(_)>127?w+="x":w+=v[_];if(!w.match(Wn)){var j=m.slice(0,p),S=m.slice(p+1),E=v.match(Dn);E&&(j.push(E[1]),S.unshift(E[2])),S.length&&(s="/"+S.join(".")+s),this.hostname=j.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),g||(this.hostname=Tn.toASCII(this.hostname));var A=this.port?":"+this.port:"",x=this.hostname||"";this.host=x+A,this.href+=this.host,g&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==s[0]&&(s="/"+s))}if(!Fn[c])for(p=0,y=Nn.length;p<y;p++){var k=Nn[p];if(-1!==s.indexOf(k)){var I=encodeURIComponent(k);I===k&&(I=escape(k)),s=s.split(k).join(I)}}var T=s.indexOf("#");-1!==T&&(this.hash=s.substr(T),s=s.slice(0,T));var C=s.indexOf("?");if(-1!==C?(this.search=s.substr(C),this.query=s.substr(C+1),t&&(this.query=Vn.parse(this.query)),s=s.slice(0,C)):t&&(this.search="",this.query={}),s&&(this.pathname=s),Hn[c]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){A=this.pathname||"";var R=this.search||"";this.path=A+R}return this.href=this.format(),this},jn.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",i=this.hash||"",n=!1,o="";this.host?n=e+this.host:this.hostname&&(n=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(n+=":"+this.port)),this.query&&Cn.isObject(this.query)&&Object.keys(this.query).length&&(o=Vn.stringify(this.query));var s=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||Hn[t])&&!1!==n?(n="//"+(n||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):n||(n=""),i&&"#"!==i.charAt(0)&&(i="#"+i),s&&"?"!==s.charAt(0)&&(s="?"+s),t+n+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+i},jn.prototype.resolve=function(e){return this.resolveObject(Sn(e,!1,!0)).format()},jn.prototype.resolveObject=function(e){if(Cn.isString(e)){var t=new jn;t.parse(e,!1,!0),e=t}for(var r=new jn,i=Object.keys(this),n=0;n<i.length;n++){var o=i[n];r[o]=this[o]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var s=Object.keys(e),a=0;a<s.length;a++){var l=s[a];"protocol"!==l&&(r[l]=e[l])}return Hn[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!Hn[e.protocol]){for(var c=Object.keys(e),u=0;u<c.length;u++){var h=c[u];r[h]=e[h]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||qn[e.protocol])r.pathname=e.pathname;else{for(var d=(e.pathname||"").split("/");d.length&&!(e.host=d.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==d[0]&&d.unshift(""),d.length<2&&d.unshift(""),r.pathname=d.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var f=r.pathname||"",p=r.search||"";r.path=f+p}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var b=r.pathname&&"/"===r.pathname.charAt(0),g=e.host||e.pathname&&"/"===e.pathname.charAt(0),m=g||b||r.host&&e.pathname,y=m,v=r.pathname&&r.pathname.split("/")||[],w=(d=e.pathname&&e.pathname.split("/")||[],r.protocol&&!Hn[r.protocol]);if(w&&(r.hostname="",r.port=null,r.host&&(""===v[0]?v[0]=r.host:v.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===d[0]?d[0]=e.host:d.unshift(e.host)),e.host=null),m=m&&(""===d[0]||""===v[0])),g)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,v=d;else if(d.length)v||(v=[]),v.pop(),v=v.concat(d),r.search=e.search,r.query=e.query;else if(!Cn.isNullOrUndefined(e.search))return w&&(r.hostname=r.host=v.shift(),(E=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=E.shift(),r.host=r.hostname=E.shift())),r.search=e.search,r.query=e.query,Cn.isNull(r.pathname)&&Cn.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r;if(!v.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var _=v.slice(-1)[0],O=(r.host||e.host||v.length>1)&&("."===_||".."===_)||""===_,j=0,S=v.length;S>=0;S--)"."===(_=v[S])?v.splice(S,1):".."===_?(v.splice(S,1),j++):j&&(v.splice(S,1),j--);if(!m&&!y)for(;j--;j)v.unshift("..");!m||""===v[0]||v[0]&&"/"===v[0].charAt(0)||v.unshift(""),O&&"/"!==v.join("/").substr(-1)&&v.push("");var E,A=""===v[0]||v[0]&&"/"===v[0].charAt(0);return w&&(r.hostname=r.host=A?"":v.length?v.shift():"",(E=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=E.shift(),r.host=r.hostname=E.shift())),(m=m||r.host&&v.length)&&!A&&v.unshift(""),v.length?r.pathname=v.join("/"):(r.pathname=null,r.path=null),Cn.isNull(r.pathname)&&Cn.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},jn.prototype.parseHost=function(){var e=this.host,t=Pn.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)},In.Url,In.format,In.resolve,In.resolveObject,zn={},Gn=!1,Kn=function(){if(Gn)return zn;Gn=!0;var e=un;function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,i="",n=0,o=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)r=e.charCodeAt(a);else{if(47===r)break;r=47}if(47===r){if(o!==a-1&&1!==s)if(o!==a-1&&2===s){if(i.length<2||2!==n||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){var l=i.lastIndexOf("/");if(l!==i.length-1){-1===l?(i="",n=0):n=(i=i.slice(0,l)).length-1-i.lastIndexOf("/"),o=a,s=0;continue}}else if(2===i.length||1===i.length){i="",n=0,o=a,s=0;continue}t&&(i.length>0?i+="/..":i="..",n=2)}else i.length>0?i+="/"+e.slice(o+1,a):i=e.slice(o+1,a),n=a-o-1;o=a,s=0}else 46===r&&-1!==s?++s:s=-1}return i}var i={resolve:function(){for(var i,n="",o=!1,s=arguments.length-1;s>=-1&&!o;s--){var a;s>=0?a=arguments[s]:(void 0===i&&(i=e.cwd()),a=i),t(a),0!==a.length&&(n=a+"/"+n,o=47===a.charCodeAt(0))}return n=r(n,!o),o?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(e){if(t(e),0===e.length)return".";var i=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0===(e=r(e,!i)).length&&!i&&(e="."),e.length>0&&n&&(e+="/"),i?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var n=arguments[r];t(n),n.length>0&&(void 0===e?e=n:e+="/"+n)}return void 0===e?".":i.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=i.resolve(e))===(r=i.resolve(r)))return"";for(var n=1;n<e.length&&47===e.charCodeAt(n);++n);for(var o=e.length,s=o-n,a=1;a<r.length&&47===r.charCodeAt(a);++a);for(var l=r.length-a,c=s<l?s:l,u=-1,h=0;h<=c;++h){if(h===c){if(l>c){if(47===r.charCodeAt(a+h))return r.slice(a+h+1);if(0===h)return r.slice(a+h)}else s>c&&(47===e.charCodeAt(n+h)?u=h:0===h&&(u=0));break}var d=e.charCodeAt(n+h);if(d!==r.charCodeAt(a+h))break;47===d&&(u=h)}var f="";for(h=n+u+1;h<=o;++h)(h===o||47===e.charCodeAt(h))&&(0===f.length?f+="..":f+="/..");return f.length>0?f+r.slice(a+u):(a+=u,47===r.charCodeAt(a)&&++a,r.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),i=47===r,n=-1,o=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!o){n=s;break}}else o=!1;return-1===n?i?"/":".":i&&1===n?"//":e.slice(0,n)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw new TypeError('"ext" argument must be a string');t(e);var i,n=0,o=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var a=r.length-1,l=-1;for(i=e.length-1;i>=0;--i){var c=e.charCodeAt(i);if(47===c){if(!s){n=i+1;break}}else-1===l&&(s=!1,l=i+1),a>=0&&(c===r.charCodeAt(a)?-1===--a&&(o=i):(a=-1,o=l))}return n===o?o=l:-1===o&&(o=e.length),e.slice(n,o)}for(i=e.length-1;i>=0;--i)if(47===e.charCodeAt(i)){if(!s){n=i+1;break}}else-1===o&&(s=!1,o=i+1);return-1===o?"":e.slice(n,o)},extname:function(e){t(e);for(var r=-1,i=0,n=-1,o=!0,s=0,a=e.length-1;a>=0;--a){var l=e.charCodeAt(a);if(47!==l)-1===n&&(o=!1,n=a+1),46===l?-1===r?r=a:1!==s&&(s=1):-1!==r&&(s=-1);else if(!o){i=a+1;break}}return-1===r||-1===n||0===s||1===s&&r===n-1&&r===i+1?"":e.slice(r,n)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var r=t.dir||t.root,i=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+i:r+e+i:i}("/",e)},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var i,n=e.charCodeAt(0),o=47===n;o?(r.root="/",i=1):i=0;for(var s=-1,a=0,l=-1,c=!0,u=e.length-1,h=0;u>=i;--u)if(47!==(n=e.charCodeAt(u)))-1===l&&(c=!1,l=u+1),46===n?-1===s?s=u:1!==h&&(h=1):-1!==s&&(h=-1);else if(!c){a=u+1;break}return-1===s||-1===l||0===h||1===h&&s===l-1&&s===a+1?-1!==l&&(r.base=r.name=0===a&&o?e.slice(1,l):e.slice(a,l)):(0===a&&o?(r.name=e.slice(1,s),r.base=e.slice(1,l)):(r.name=e.slice(a,s),r.base=e.slice(a,l)),r.ext=e.slice(s,l)),a>0?r.dir=e.slice(0,a-1):o&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};return i.posix=i,zn=i}(),Qn=typeof Deno<"u"?"windows"===Deno.build.os?"win32":Deno.build.os:void 0,In.URL=typeof URL<"u"?URL:null,In.pathToFileURL=An,In.fileURLToPath=En,In.Url,In.format,In.resolve,In.resolveObject,In.URL,Yn=92,Jn=47,Xn=97,$n=122,Zn="win32"===Qn,eo=/\//g,to=/%/g,ro=/\\/g,io=/\n/g,no=/\r/g,oo=/\t/g,so=typeof Deno<"u"?"windows"===Deno.build.os?"win32":Deno.build.os:void 0,In.URL=typeof URL<"u"?URL:null,In.pathToFileURL=kn,In.fileURLToPath=xn,ao=In.Url,lo=In.format,co=In.resolve,uo=In.resolveObject,ho=In.parse,fo=In.URL,po=92,bo=47,go=97,mo=122,yo="win32"===so,vo=/\//g,wo=/%/g,_o=/\\/g,Oo=/\n/g,jo=/\r/g,So=/\t/g})),Ao=S(((e,t)=>{x(),yt(),nt(),t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}})),xo=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0}),e.BufferedDuplex=void 0,e.writev=i;var t=vr(),r=(mt(),A(ot));function i(e,t){let i=new Array(e.length);for(let n=0;n<e.length;n++)"string"==typeof e[n].chunk?i[n]=r.Buffer.from(e[n].chunk,"utf8"):i[n]=e[n].chunk;this._write(r.Buffer.concat(i),"binary",t)}var n=class extends t.Duplex{constructor(e,t,r){super({objectMode:!0}),this.proxy=t,this.socket=r,this.writeQueue=[],e.objectMode||(this._writev=i.bind(this)),this.isSocketOpen=!1,this.proxy.on("data",(e=>{!this.destroyed&&this.readable&&this.push(e)}))}_read(e){this.proxy.read(e)}_write(e,t,r){this.isSocketOpen?this.writeToProxy(e,t,r):this.writeQueue.push({chunk:e,encoding:t,cb:r})}_final(e){this.writeQueue=[],this.proxy.end(e)}_destroy(e,t){this.writeQueue=[],this.proxy.destroy(),t(e)}socketReady(){this.emit("connect"),this.isSocketOpen=!0,this.processWriteQueue()}writeToProxy(e,t,r){!1===this.proxy.write(e,t)?this.proxy.once("drain",r):r()}processWriteQueue(){for(;this.writeQueue.length>0;){let{chunk:e,encoding:t,cb:r}=this.writeQueue.shift();this.writeToProxy(e,t,r)}}};e.BufferedDuplex=n})),ko=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.streamBuilder=e.browserStreamBuilder=void 0;var r=(mt(),A(ot)),i=t(Ao()),n=t(xr()),o=vr(),s=t(yi()),a=xo(),l=(0,n.default)("mqttjs:ws"),c=["rejectUnauthorized","ca","cert","key","pfx","passphrase"];function u(e,t){let r="".concat(e.protocol,"://").concat(e.hostname,":").concat(e.port).concat(e.path);return"function"==typeof e.transformWsUrl&&(r=e.transformWsUrl(r,e,t)),r}function h(e){let t=e;return e.port||("wss"===e.protocol?t.port=443:t.port=80),e.path||(t.path="/"),e.wsOptions||(t.wsOptions={}),!s.default&&!e.forceNativeWebSocket&&"wss"===e.protocol&&c.forEach((r=>{Object.prototype.hasOwnProperty.call(e,r)&&!Object.prototype.hasOwnProperty.call(e.wsOptions,r)&&(t.wsOptions[r]=e[r])})),t}e.streamBuilder=(e,t)=>{l("streamBuilder");let r=h(t);r.hostname=r.hostname||r.host||"localhost";let n=u(r,e),o=function(e,t,r){l("createWebSocket"),l("protocol: ".concat(r.protocolId," ").concat(r.protocolVersion));let n,o="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";return l("creating new Websocket for url: ".concat(t," and protocol: ").concat(o)),n=r.createWebsocket?r.createWebsocket(t,[o],r):new i.default(t,[o],r.wsOptions),n}(0,n,r),s=i.default.createWebSocketStream(o,r.wsOptions);return s.url=n,o.on("close",(()=>{s.destroy()})),s};e.browserStreamBuilder=(e,t)=>{l("browserStreamBuilder");let i,n=function(e){let t=h(e);if(t.hostname||(t.hostname=t.host),!t.hostname){if(typeof document>"u")throw new Error("Could not determine host. Specify host manually.");let e=new URL(document.URL);t.hostname=e.hostname,t.port||(t.port=Number(e.port))}return void 0===t.objectMode&&(t.objectMode=!(!0===t.binary||void 0===t.binary)),t}(t).browserBufferSize||524288,s=t.browserBufferTimeout||1e3,c=!t.objectMode,d=function(e,t){let r,i="MQIsdp"===t.protocolId&&3===t.protocolVersion?"mqttv3.1":"mqtt",n=u(t,e);return r=t.createWebsocket?t.createWebsocket(n,[i],t):new WebSocket(n,[i]),r.binaryType="arraybuffer",r}(e,t),f=function(e,t,r){let i=new o.Transform({objectMode:e.objectMode});return i._write=t,i._flush=r,i}(t,(function e(t,i,o){if(d.bufferedAmount>n)return void setTimeout(e,s,t,i,o);c&&"string"==typeof t&&(t=r.Buffer.from(t,"utf8"));try{d.send(t)}catch(a){return o(a)}o()}),(function(e){d.close(),e()}));t.objectMode||(f._writev=a.writev.bind(f)),f.on("close",(()=>{d.close()}));let p=typeof d.addEventListener<"u";function b(){l("WebSocket onOpen"),i instanceof a.BufferedDuplex&&i.socketReady()}function g(e){l("WebSocket onClose",e),i.end(),i.destroy()}function m(e){l("WebSocket onError",e);let t=new Error("WebSocket error");t.event=e,i.destroy(t)}async function y(e){if(!f||f.destroyed||!f.readable)return;let{data:t}=e;t=t instanceof ArrayBuffer?r.Buffer.from(t):t instanceof Blob?r.Buffer.from(await new Response(t).arrayBuffer()):r.Buffer.from(t,"utf8"),f.push(t)}return d.readyState===d.OPEN?(i=f,i.socket=d):(i=new a.BufferedDuplex(t,f,d),p?d.addEventListener("open",b):d.onopen=b),p?(d.addEventListener("close",g),d.addEventListener("error",m),d.addEventListener("message",y)):(d.onclose=g,d.onerror=m,d.onmessage=y),i}})),Io={};function To(){throw new Error("Node.js net module is not supported by JSPM core outside of Node.js")}E(Io,{Server:()=>To,Socket:()=>To,Stream:()=>To,_createServerHandle:()=>To,_normalizeArgs:()=>To,_setSimultaneousAccepts:()=>To,connect:()=>To,createConnection:()=>To,createServer:()=>To,default:()=>Co,isIP:()=>To,isIPv4:()=>To,isIPv6:()=>To});var Co,Ro=j((()=>{x(),yt(),nt(),Co={_createServerHandle:To,_normalizeArgs:To,_setSimultaneousAccepts:To,connect:To,createConnection:To,createServer:To,isIP:To,isIPv4:To,isIPv6:To,Server:To,Socket:To,Stream:To}})),Po=S(((e,t)=>{x(),yt(),nt(),t.exports={}})),Mo=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var r=t((Ro(),A(Io))),i=t(xr()),n=t(Po()),o=(0,i.default)("mqttjs:tcp");e.default=(e,t)=>{if(t.port=t.port||1883,t.hostname=t.hostname||t.host||"localhost",t.socksProxy)return(0,n.default)(t.hostname,t.port,t.socksProxy,{timeout:t.socksTimeout});let{port:i,path:s}=t,a=t.hostname;return o("port %d and host %s",i,a),r.default.createConnection({port:i,host:a,path:s})}})),Bo={};E(Bo,{default:()=>No});var No,Lo=j((()=>{x(),yt(),nt(),No={}})),Uo=S((e=>{x(),yt(),nt();var t=e&&e.__rest||function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]])}return r},r=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0});var i=r((Lo(),A(Bo))),n=r((Ro(),A(Io))),o=r(xr()),s=r(Po()),a=(0,o.default)("mqttjs:tls");e.default=(e,r)=>{r.port=r.port||8883,r.host=r.hostname||r.host||"localhost",0===n.default.isIP(r.host)&&(r.servername=r.host),r.rejectUnauthorized=!1!==r.rejectUnauthorized,delete r.path,a("port %d host %s rejectUnauthorized %b",r.port,r.host,r.rejectUnauthorized);let o=function(e){let{host:r,port:n,socksProxy:o}=e,a=t(e,["host","port","socksProxy"]);return i.default.connect(o?Object.assign(Object.assign({},a),{socket:(0,s.default)(r,n,o,{timeout:e.socksTimeout})}):e)}(r);function l(t){r.rejectUnauthorized&&e.emit("error",t),o.end()}return o.on("secureConnect",(()=>{r.rejectUnauthorized&&!o.authorized?o.emit("error",new Error("TLS not authorized")):o.removeListener("error",l)})),o.on("error",l),o}})),Wo=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t,r,i,n=(mt(),A(ot)),o=vr(),s=xo();e.default=(e,a)=>{if(a.hostname=a.hostname||a.host,!a.hostname)throw new Error("Could not determine host. Specify host manually.");let l="MQIsdp"===a.protocolId&&3===a.protocolVersion?"mqttv3.1":"mqtt";!function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})}(a);let c=function(e,t){let r="wxs"===e.protocol?"wss":"ws",i="".concat(r,"://").concat(e.hostname).concat(e.path);return e.port&&80!==e.port&&443!==e.port&&(i="".concat(r,"://").concat(e.hostname,":").concat(e.port).concat(e.path)),"function"==typeof e.transformWsUrl&&(i=e.transformWsUrl(i,e,t)),i}(a,e);t=wx.connectSocket({url:c,protocols:[l]}),r=function(){let e=new o.Transform;return e._write=(e,r,i)=>{t.send({data:e.buffer,success(){i()},fail(e){i(new Error(e))}})},e._flush=e=>{t.close({success(){e()}})},e}(),(i=new s.BufferedDuplex(a,r,t))._destroy=(e,r)=>{t.close({success(){r&&r(e)}})};let u=i.destroy;return i.destroy=(e,r)=>(i.destroy=u,setTimeout((()=>{t.close({fail(){i._destroy(e,r)}})}),0),i),t.onOpen((()=>{i.socketReady()})),t.onMessage((e=>{let{data:t}=e;t=t instanceof ArrayBuffer?n.Buffer.from(t):n.Buffer.from(t,"utf8"),r.push(t)})),t.onClose((()=>{i.emit("close"),i.end(),i.destroy()})),t.onError((e=>{let t=new Error(e.errMsg);i.destroy(t)})),i}})),Do=S((e=>{x(),yt(),nt(),Object.defineProperty(e,"__esModule",{value:!0});var t,r,i,n=(mt(),A(ot)),o=vr(),s=xo(),a=!1;e.default=(e,l)=>{if(l.hostname=l.hostname||l.host,!l.hostname)throw new Error("Could not determine host. Specify host manually.");let c="MQIsdp"===l.protocolId&&3===l.protocolVersion?"mqttv3.1":"mqtt";!function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})}(l);let u=function(e,t){let r="alis"===e.protocol?"wss":"ws",i="".concat(r,"://").concat(e.hostname).concat(e.path);return e.port&&80!==e.port&&443!==e.port&&(i="".concat(r,"://").concat(e.hostname,":").concat(e.port).concat(e.path)),"function"==typeof e.transformWsUrl&&(i=e.transformWsUrl(i,e,t)),i}(l,e);return(t=l.my).connectSocket({url:u,protocols:c}),r=function(){let e=new o.Transform;return e._write=(e,r,i)=>{t.sendSocketMessage({data:e.buffer,success(){i()},fail(){i(new Error)}})},e._flush=e=>{t.closeSocket({success(){e()}})},e}(),i=new s.BufferedDuplex(l,r,t),a||(a=!0,t.onSocketOpen((()=>{i.socketReady()})),t.onSocketMessage((e=>{if("string"==typeof e.data){let t=n.Buffer.from(e.data,"base64");r.push(t)}else{let t=new FileReader;t.addEventListener("load",(()=>{let e=t.result;e=e instanceof ArrayBuffer?n.Buffer.from(e):n.Buffer.from(e,"utf8"),r.push(e)})),t.readAsArrayBuffer(e.data)}})),t.onSocketClose((()=>{i.end(),i.destroy()})),t.onSocketError((e=>{i.destroy(e)}))),i}})),Fo=S((e=>{x(),yt(),nt();var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.connectAsync=function(e,t){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return new Promise(((i,n)=>{let o=c(e,t),s={connect:e=>{a(),i(o)},end:()=>{a(),i(o)},error:e=>{a(),o.end(),n(e)}};function a(){Object.keys(s).forEach((e=>{o.off(e,s[e])}))}!1===r&&(s.close=()=>{s.error(new Error("Couldn't connect to server"))}),Object.keys(s).forEach((e=>{o.on(e,s[e])}))}))};var r=t(xr()),n=t((Eo(),A(On))),o=t(Si()),s=t(yi());"function"!=typeof(null===k||void 0===k?void 0:k.nextTick)&&(k.nextTick=i);var a=(0,r.default)("mqttjs"),l=null;function c(e,t){var r,i,c;if(a("connecting to an MQTT broker..."),"object"==typeof e&&!t&&(t=e,e=""),t=t||{},e&&"string"==typeof e){let r=n.default.parse(e,!0),i={};if(null!=r.port&&(i.port=Number(r.port)),i.host=r.hostname,i.query=r.query,i.auth=r.auth,i.protocol=r.protocol,i.path=r.path,!(t=Object.assign(Object.assign({},i),t)).protocol)throw new Error("Missing protocol");t.protocol=t.protocol.replace(/:$/,"")}if(t.unixSocket=t.unixSocket||(null===(r=t.protocol)||void 0===r?void 0:r.includes("+unix")),t.unixSocket?t.protocol=t.protocol.replace("+unix",""):!(null!==(i=t.protocol)&&void 0!==i&&i.startsWith("ws"))&&!(null!==(c=t.protocol)&&void 0!==c&&c.startsWith("wx"))&&delete t.path,function(e){let t;e.auth&&(t=e.auth.match(/^(.+):(.+)$/),t?(e.username=t[1],e.password=t[2]):e.username=e.auth)}(t),t.query&&"string"==typeof t.query.clientId&&(t.clientId=t.query.clientId),s.default||t.unixSocket?t.socksProxy=void 0:void 0===t.socksProxy&&typeof k<"u"&&(t.socksProxy=k.env.MQTTJS_SOCKS_PROXY),t.cert&&t.key){if(!t.protocol)throw new Error("Missing secure protocol key");if(-1===["mqtts","wss","wxs","alis"].indexOf(t.protocol))switch(t.protocol){case"mqtt":t.protocol="mqtts";break;case"ws":t.protocol="wss";break;case"wx":t.protocol="wxs";break;case"ali":t.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'.concat(t.protocol,'"!'))}}if(l||(l={},s.default||t.forceNativeWebSocket?(l.ws=ko().browserStreamBuilder,l.wss=ko().browserStreamBuilder,l.wx=Wo().default,l.wxs=Wo().default,l.ali=Do().default,l.alis=Do().default):(l.ws=ko().streamBuilder,l.wss=ko().streamBuilder,l.mqtt=Mo().default,l.tcp=Mo().default,l.ssl=Uo().default,l.tls=l.ssl,l.mqtts=Uo().default)),!l[t.protocol]){let e=-1!==["mqtts","wss"].indexOf(t.protocol);t.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter(((t,r)=>(!e||r%2!==0)&&"function"==typeof l[t]))[0]}if(!1===t.clean&&!t.clientId)throw new Error("Missing clientId for unclean clients");t.protocol&&(t.defaultProtocol=t.protocol);let u=new o.default((function(e){return t.servers&&((!e._reconnectCount||e._reconnectCount===t.servers.length)&&(e._reconnectCount=0),t.host=t.servers[e._reconnectCount].host,t.port=t.servers[e._reconnectCount].port,t.protocol=t.servers[e._reconnectCount].protocol?t.servers[e._reconnectCount].protocol:t.defaultProtocol,t.hostname=t.host,e._reconnectCount++),a("calling streambuilder for",t.protocol),l[t.protocol](e,t)}),t);return u.on("error",(()=>{})),u}e.default=c})),qo=S((e=>{x(),yt(),nt();var t=e&&e.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=e&&e.__importStar||function(){var e=function(t){return(e=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(t)};return function(i){if(i&&i.__esModule)return i;var n={};if(null!=i)for(var o=e(i),s=0;s<o.length;s++)"default"!==o[s]&&t(n,i,o[s]);return r(n,i),n}}(),n=e&&e.__exportStar||function(e,r){for(var i in e)"default"!==i&&!Object.prototype.hasOwnProperty.call(r,i)&&t(r,e,i)},o=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.ReasonCodes=e.KeepaliveManager=e.UniqueMessageIdProvider=e.DefaultMessageIdProvider=e.Store=e.MqttClient=e.connectAsync=e.connect=e.Client=void 0;var s=o(Si());e.MqttClient=s.default;var a=o(Mr());e.DefaultMessageIdProvider=a.default;var l=o(Ei());e.UniqueMessageIdProvider=l.default;var c=o(Ur());e.Store=c.default;var u=i(Fo());e.connect=u.default,Object.defineProperty(e,"connectAsync",{enumerable:!0,get:function(){return u.connectAsync}});var h=o(ji());e.KeepaliveManager=h.default,e.Client=s.default,n(Si(),e),n(Fr(),e);var d=qr();Object.defineProperty(e,"ReasonCodes",{enumerable:!0,get:function(){return d.ReasonCodes}})})),Ho=S((e=>{x(),yt(),nt();var t=e&&e.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=e&&e.__importStar||function(){var e=function(t){return(e=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(t)};return function(i){if(i&&i.__esModule)return i;var n={};if(null!=i)for(var o=e(i),s=0;s<o.length;s++)"default"!==o[s]&&t(n,i,o[s]);return r(n,i),n}}(),n=e&&e.__exportStar||function(e,r){for(var i in e)"default"!==i&&!Object.prototype.hasOwnProperty.call(r,i)&&t(r,e,i)};Object.defineProperty(e,"__esModule",{value:!0});var o=i(qo());e.default=o,n(qo(),e)}));t.a=Ho()}).call(this,r(28),r(349).setImmediate)},829:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(765);function n(e,t){Object(i.a)(e,t),t.add(e)}},830:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(765);function n(e,t,r){Object(i.a)(e,t),t.set(e,r)}},831:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var i=r(766),n=r(767);function o(e,t,r){var o=Object(n.a)(t,e);return Object(i.a)(e,o,r),r}},832:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(663);function n(e,t,r){return Object(i.a)(t,e),r}},833:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var i=r(768),n=r(767);function o(e,t){var r=Object(n.a)(t,e);return Object(i.a)(e,r)}},834:function(e,t,r){"use strict";r.d(t,"a",(function(){return s}));var i=r(768),n=r(663),o=r(769);function s(e,t,r){return Object(n.a)(t,e),Object(o.a)(r,"get"),Object(i.a)(e,r)}},835:function(e,t,r){"use strict";r.d(t,"a",(function(){return s}));var i=r(766),n=r(663),o=r(769);function s(e,t,r,s){return Object(n.a)(t,e),Object(o.a)(r,"set"),Object(i.a)(e,r,s),s}},836:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(703);function n(e){var t={},r=!1;function n(t,n){return r=!0,n=new Promise((function(r){r(e[t](n))})),{done:!1,value:new i.a(n,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return r?(r=!1,e):n("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(r)throw r=!1,e;return n("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return r?(r=!1,e):n("return",e)}),t}},837:function(e,t,r){"use strict";function i(e){var t,r,i,o=2;for("undefined"!=typeof Symbol&&(r=Symbol.asyncIterator,i=Symbol.iterator);o--;){if(r&&null!=(t=e[r]))return t.call(e);if(i&&null!=(t=e[i]))return new n(t.call(e));r="@@asyncIterator",i="@@iterator"}throw new TypeError("Object is not async iterable")}function n(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return n=function(e){this.s=e,this.n=e.next},n.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var r=this.s.return;return void 0===r?Promise.resolve({value:e,done:!0}):t(r.apply(this.s,arguments))},throw:function(e){var r=this.s.return;return void 0===r?Promise.reject(e):t(r.apply(this.s,arguments))}},new n(e)}r.d(t,"a",(function(){return i}))},838:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(703);function n(e){return new i.a(e,0)}},839:function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));var i=r(703);function n(e){return function(){return new o(e.apply(this,arguments))}}function o(e){var t,r;function n(t,r){try{var s=e[t](r),a=s.value,l=a instanceof i.a;Promise.resolve(l?a.v:a).then((function(r){if(l){var i="return"===t?"return":"next";if(!a.k||r.done)return n(i,r);r=e[i](r).value}o(s.done?"return":"normal",r)}),(function(e){n("throw",e)}))}catch(e){o("throw",e)}}function o(e,i){switch(e){case"return":t.resolve({value:i,done:!0});break;case"throw":t.reject(i);break;default:t.resolve({value:i,done:!1})}(t=t.next)?n(t.key,t.arg):r=null}this._invoke=function(e,i){return new Promise((function(o,s){var a={key:e,arg:i,resolve:o,reject:s,next:null};r?r=r.next=a:(t=r=a,n(e,i))}))},"function"!=typeof e.return&&(this.return=void 0)}o.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},o.prototype.next=function(e){return this._invoke("next",e)},o.prototype.throw=function(e){return this._invoke("throw",e)},o.prototype.return=function(e){return this._invoke("return",e)}},895:function(e,t,r){"use strict";r.d(t,"b",(function(){return o}));var i=r(559),n=r(525);function o(e){return Object(n.a)("MuiTabs",e)}const s=Object(i.a)("MuiTabs",["root","vertical","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]);t.a=s}}]);
//# sourceMappingURL=32.ff3ff726.chunk.js.map