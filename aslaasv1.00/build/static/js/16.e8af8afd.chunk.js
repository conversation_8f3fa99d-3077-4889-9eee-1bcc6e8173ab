/*! For license information please see 16.e8af8afd.chunk.js.LICENSE.txt */
(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[16],{1020:function(t,e,r){"use strict";var n=this&&this.__awaiter||function(t,e,r,n){return new(r||(r=Promise))((function(o,i){function a(t){try{s(n.next(t))}catch(e){i(e)}}function u(t){try{s(n.throw(t))}catch(e){i(e)}}function s(t){t.done?o(t.value):new r((function(e){e(t.value)})).then(a,u)}s((n=n.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(u){i=[6,u],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},i=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var a=r(1097),u=r(1150),s=r(824),f=r(1151),l=r(692),d=r(642),c=r(1154),h=r(1155),p=function(){function t(t,e,r){void 0===e&&(e=500),this.reader=t,this.timeBetweenScansMillis=e,this._hints=r,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(t.prototype,"hasNavigator",{get:function(){return"undefined"!==typeof navigator},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"canEnumerateDevices",{get:function(){return!(!this.isMediaDevicesSuported||!navigator.mediaDevices.enumerateDevices)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(t){this._timeBetweenDecodingAttempts=t<0?0:t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hints",{get:function(){return this._hints},set:function(t){this._hints=t||null},enumerable:!0,configurable:!0}),t.prototype.listVideoInputDevices=function(){return n(this,void 0,void 0,(function(){var t,e,r,n,a,u,s,f,l,d,c,h;return o(this,(function(o){switch(o.label){case 0:if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:r=o.sent(),n=[];try{for(a=i(r),u=a.next();!u.done;u=a.next())s=u.value,"videoinput"===(f="video"===s.kind?"videoinput":s.kind)&&(l=s.deviceId||s.id,d=s.label||"Video device "+(n.length+1),c=s.groupId,h={deviceId:l,label:d,kind:f,groupId:c},n.push(h))}catch(p){t={error:p}}finally{try{u&&!u.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return[2,n]}}))}))},t.prototype.getVideoInputDevices=function(){return n(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return[2,t.sent().map((function(t){return new h.VideoInputDevice(t.deviceId,t.label)}))]}}))}))},t.prototype.findDeviceById=function(t){return n(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.listVideoInputDevices()];case 1:return(e=r.sent())?[2,e.find((function(e){return e.deviceId===t}))]:[2,null]}}))}))},t.prototype.decodeFromInputVideoDevice=function(t,e){return n(this,void 0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,this.decodeOnceFromVideoDevice(t,e)];case 1:return[2,r.sent()]}}))}))},t.prototype.decodeOnceFromVideoDevice=function(t,e){return n(this,void 0,void 0,(function(){var r;return o(this,(function(n){switch(n.label){case 0:return this.reset(),r={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,e)];case 1:return[2,n.sent()]}}))}))},t.prototype.decodeOnceFromConstraints=function(t,e){return n(this,void 0,void 0,(function(){var r;return o(this,(function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return r=n.sent(),[4,this.decodeOnceFromStream(r,e)];case 2:return[2,n.sent()]}}))}))},t.prototype.decodeOnceFromStream=function(t,e){return n(this,void 0,void 0,(function(){var r;return o(this,(function(n){switch(n.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return r=n.sent(),[4,this.decodeOnce(r)];case 2:return[2,n.sent()]}}))}))},t.prototype.decodeFromInputVideoDeviceContinuously=function(t,e,r){return n(this,void 0,void 0,(function(){return o(this,(function(n){switch(n.label){case 0:return[4,this.decodeFromVideoDevice(t,e,r)];case 1:return[2,n.sent()]}}))}))},t.prototype.decodeFromVideoDevice=function(t,e,r){return n(this,void 0,void 0,(function(){var n;return o(this,(function(o){switch(o.label){case 0:return n={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(n,e,r)];case 1:return[2,o.sent()]}}))}))},t.prototype.decodeFromConstraints=function(t,e,r){return n(this,void 0,void 0,(function(){var n;return o(this,(function(o){switch(o.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=o.sent(),[4,this.decodeFromStream(n,e,r)];case 2:return[2,o.sent()]}}))}))},t.prototype.decodeFromStream=function(t,e,r){return n(this,void 0,void 0,(function(){var n;return o(this,(function(o){switch(o.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return n=o.sent(),[4,this.decodeContinuously(n,r)];case 2:return[2,o.sent()]}}))}))},t.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},t.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},t.prototype.attachStreamToVideo=function(t,e){return n(this,void 0,void 0,(function(){var r;return o(this,(function(n){switch(n.label){case 0:return r=this.prepareVideoElement(e),this.addVideoSource(r,t),this.videoElement=r,this.stream=t,[4,this.playVideoOnLoadAsync(r)];case 1:return n.sent(),[2,r]}}))}))},t.prototype.playVideoOnLoadAsync=function(t){var e=this;return new Promise((function(r,n){return e.playVideoOnLoad(t,(function(){return r()}))}))},t.prototype.playVideoOnLoad=function(t,e){var r=this;this.videoEndedListener=function(){return r.stopStreams()},this.videoCanPlayListener=function(){return r.tryPlayVideo(t)},t.addEventListener("ended",this.videoEndedListener),t.addEventListener("canplay",this.videoCanPlayListener),t.addEventListener("playing",e),this.tryPlayVideo(t)},t.prototype.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&!t.ended&&t.readyState>2},t.prototype.tryPlayVideo=function(t){return n(this,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:if(this.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,t.play()];case 2:return e.sent(),[3,4];case 3:return e.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}}))}))},t.prototype.getMediaElement=function(t,e){var r=document.getElementById(t);if(!r)throw new a.default("element with id '"+t+"' not found");if(r.nodeName.toLowerCase()!==e.toLowerCase())throw new a.default("element with id '"+t+"' must be an "+e+" element");return r},t.prototype.decodeFromImage=function(t,e){if(!t&&!e)throw new a.default("either imageElement with a src set or an url must be provided");return e&&!t?this.decodeFromImageUrl(e):this.decodeFromImageElement(t)},t.prototype.decodeFromVideo=function(t,e){if(!t&&!e)throw new a.default("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrl(e):this.decodeFromVideoElement(t)},t.prototype.decodeFromVideoContinuously=function(t,e,r){if(void 0===t&&void 0===e)throw new a.default("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrlContinuously(e,r):this.decodeFromVideoElementContinuously(t,r)},t.prototype.decodeFromImageElement=function(t){if(!t)throw new a.default("An image element must be provided.");this.reset();var e=this.prepareImageElement(t);return this.imageElement=e,this.isImageLoaded(e)?this.decodeOnce(e,!1,!0):this._decodeOnLoadImage(e)},t.prototype.decodeFromVideoElement=function(t){var e=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideo(e)},t.prototype.decodeFromVideoElementContinuously=function(t,e){var r=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideoContinuously(r,e)},t.prototype._decodeFromVideoElementSetup=function(t){if(!t)throw new a.default("A video element must be provided.");this.reset();var e=this.prepareVideoElement(t);return this.videoElement=e,e},t.prototype.decodeFromImageUrl=function(t){if(!t)throw new a.default("An URL must be provided.");this.reset();var e=this.prepareImageElement();this.imageElement=e;var r=this._decodeOnLoadImage(e);return e.src=t,r},t.prototype.decodeFromVideoUrl=function(t){if(!t)throw new a.default("An URL must be provided.");this.reset();var e=this.prepareVideoElement(),r=this.decodeFromVideoElement(e);return e.src=t,r},t.prototype.decodeFromVideoUrlContinuously=function(t,e){if(!t)throw new a.default("An URL must be provided.");this.reset();var r=this.prepareVideoElement(),n=this.decodeFromVideoElementContinuously(r,e);return r.src=t,n},t.prototype._decodeOnLoadImage=function(t){var e=this;return new Promise((function(r,n){e.imageLoadedListener=function(){return e.decodeOnce(t,!1,!0).then(r,n)},t.addEventListener("load",e.imageLoadedListener)}))},t.prototype._decodeOnLoadVideo=function(t){return n(this,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return e.sent(),[4,this.decodeOnce(t)];case 2:return[2,e.sent()]}}))}))},t.prototype._decodeOnLoadVideoContinuously=function(t,e){return n(this,void 0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return r.sent(),this.decodeContinuously(t,e),[2]}}))}))},t.prototype.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.prototype.prepareImageElement=function(t){var e;return"undefined"===typeof t&&((e=document.createElement("img")).width=200,e.height=200),"string"===typeof t&&(e=this.getMediaElement(t,"img")),t instanceof HTMLImageElement&&(e=t),e},t.prototype.prepareVideoElement=function(t){var e;return t||"undefined"===typeof document||((e=document.createElement("video")).width=200,e.height=200),"string"===typeof t&&(e=this.getMediaElement(t,"video")),t instanceof HTMLVideoElement&&(e=t),e.setAttribute("autoplay","true"),e.setAttribute("muted","true"),e.setAttribute("playsinline","true"),e},t.prototype.decodeOnce=function(t,e,r){var n=this;void 0===e&&(e=!0),void 0===r&&(r=!0),this._stopAsyncDecode=!1;var o=function(i,a){if(n._stopAsyncDecode)return a(new d.default("Video stream has ended before any code could be detected.")),void(n._stopAsyncDecode=void 0);try{i(n.decode(t))}catch(c){var u=e&&c instanceof d.default,f=c instanceof s.default||c instanceof l.default;if(u||f&&r)return setTimeout(o,n._timeBetweenDecodingAttempts,i,a);a(c)}};return new Promise((function(t,e){return o(t,e)}))},t.prototype.decodeContinuously=function(t,e){var r=this;this._stopContinuousDecode=!1;var n=function(){if(r._stopContinuousDecode)r._stopContinuousDecode=void 0;else try{var o=r.decode(t);e(o,null),setTimeout(n,r.timeBetweenScansMillis)}catch(u){e(null,u);var i=u instanceof s.default||u instanceof l.default,a=u instanceof d.default;(i||a)&&setTimeout(n,r._timeBetweenDecodingAttempts)}};n()},t.prototype.decode=function(t){var e=this.createBinaryBitmap(t);return this.decodeBitmap(e)},t.prototype.createBinaryBitmap=function(t){var e=this.getCaptureCanvasContext(t);this.drawImageOnCanvas(e,t);var r=this.getCaptureCanvas(t),n=new c.HTMLCanvasElementLuminanceSource(r),o=new f.default(n);return new u.default(o)},t.prototype.getCaptureCanvasContext=function(t){if(!this.captureCanvasContext){var e=this.getCaptureCanvas(t).getContext("2d");this.captureCanvasContext=e}return this.captureCanvasContext},t.prototype.getCaptureCanvas=function(t){if(!this.captureCanvas){var e=this.createCaptureCanvas(t);this.captureCanvas=e}return this.captureCanvas},t.prototype.drawImageOnCanvas=function(t,e){t.drawImage(e,0,0)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this._hints)},t.prototype.createCaptureCanvas=function(t){if("undefined"===typeof document)return this._destroyCaptureCanvas(),null;var e,r,n=document.createElement("canvas");return"undefined"!==typeof t&&(t instanceof HTMLVideoElement?(e=t.videoWidth,r=t.videoHeight):t instanceof HTMLImageElement&&(e=t.naturalWidth||t.width,r=t.naturalHeight||t.height)),n.style.width=e+"px",n.style.height=r+"px",n.width=e,n.height=r,n},t.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach((function(t){return t.stop()})),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()},t.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},t.prototype._destroyVideoElement=function(){this.videoElement&&("undefined"!==typeof this.videoEndedListener&&this.videoElement.removeEventListener("ended",this.videoEndedListener),"undefined"!==typeof this.videoPlayingEventListener&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),"undefined"!==typeof this.videoCanPlayListener&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},t.prototype._destroyImageElement=function(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},t.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},t.prototype.addVideoSource=function(t,e){try{t.srcObject=e}catch(r){t.src=URL.createObjectURL(e)}},t.prototype.cleanVideoSource=function(t){try{t.srcObject=null}catch(e){t.src=""}this.videoElement.removeAttribute("src")},t}();e.BrowserCodeReader=p},1021:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(752),o=r(898),i=r(1022),a=r(654),u=function(){function t(e,r){void 0===e?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,this.bits=void 0===r||null===r?t.makeArray(e):r)}return t.prototype.getSize=function(){return this.size},t.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},t.prototype.ensureCapacity=function(e){if(e>32*this.bits.length){var r=t.makeArray(e);n.default.arraycopy(this.bits,0,r,0,this.bits.length),this.bits=r}},t.prototype.get=function(t){return 0!==(this.bits[Math.floor(t/32)]&1<<(31&t))},t.prototype.set=function(t){this.bits[Math.floor(t/32)]|=1<<(31&t)},t.prototype.flip=function(t){this.bits[Math.floor(t/32)]^=1<<(31&t)},t.prototype.getNextSet=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),i=r[n];i&=~((1<<(31&t))-1);for(var a=r.length;0===i;){if(++n===a)return e;i=r[n]}var u=32*n+o.default.numberOfTrailingZeros(i);return u>e?e:u},t.prototype.getNextUnset=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),i=~r[n];i&=~((1<<(31&t))-1);for(var a=r.length;0===i;){if(++n===a)return e;i=~r[n]}var u=32*n+o.default.numberOfTrailingZeros(i);return u>e?e:u},t.prototype.setBulk=function(t,e){this.bits[Math.floor(t/32)]=e},t.prototype.setRange=function(t,e){if(e<t||t<0||e>this.size)throw new a.default;if(e!==t){e--;for(var r=Math.floor(t/32),n=Math.floor(e/32),o=this.bits,i=r;i<=n;i++){var u=(2<<(i<n?31:31&e))-(1<<(i>r?0:31&t));o[i]|=u}}},t.prototype.clear=function(){for(var t=this.bits.length,e=this.bits,r=0;r<t;r++)e[r]=0},t.prototype.isRange=function(t,e,r){if(e<t||t<0||e>this.size)throw new a.default;if(e===t)return!0;e--;for(var n=Math.floor(t/32),o=Math.floor(e/32),i=this.bits,u=n;u<=o;u++){var s=(2<<(u<o?31:31&e))-(1<<(u>n?0:31&t))&4294967295;if((i[u]&s)!==(r?s:0))return!1}return!0},t.prototype.appendBit=function(t){this.ensureCapacity(this.size+1),t&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++},t.prototype.appendBits=function(t,e){if(e<0||e>32)throw new a.default("Num bits must be between 0 and 32");this.ensureCapacity(this.size+e);this.appendBit;for(var r=e;r>0;r--)this.appendBit(1===(t>>r-1&1))},t.prototype.appendBitArray=function(t){var e=t.size;this.ensureCapacity(this.size+e);this.appendBit;for(var r=0;r<e;r++)this.appendBit(t.get(r))},t.prototype.xor=function(t){if(this.size!==t.size)throw new a.default("Sizes don't match");for(var e=this.bits,r=0,n=e.length;r<n;r++)e[r]^=t.bits[r]},t.prototype.toBytes=function(t,e,r,n){for(var o=0;o<n;o++){for(var i=0,a=0;a<8;a++)this.get(t)&&(i|=1<<7-a),t++;e[r+o]=i}},t.prototype.getBitArray=function(){return this.bits},t.prototype.reverse=function(){for(var t=new Int32Array(this.bits.length),e=Math.floor((this.size-1)/32),r=e+1,n=this.bits,o=0;o<r;o++){var i=n[o];i=(i=(i=(i=(i=i>>1&1431655765|(1431655765&i)<<1)>>2&858993459|(858993459&i)<<2)>>4&252645135|(252645135&i)<<4)>>8&16711935|(16711935&i)<<8)>>16&65535|(65535&i)<<16,t[e-o]=i}if(this.size!==32*r){var a=32*r-this.size,u=t[0]>>>a;for(o=1;o<r;o++){var s=t[o];u|=s<<32-a,t[o-1]=u,u=s>>>a}t[r-1]=u}this.bits=t},t.makeArray=function(t){return new Int32Array(Math.floor((t+31)/32))},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.size===r.size&&i.default.equals(this.bits,r.bits)},t.prototype.hashCode=function(){return 31*this.size+i.default.hashCode(this.bits)},t.prototype.toString=function(){for(var t="",e=0,r=this.size;e<r;e++)0===(7&e)&&(t+=" "),t+=this.get(e)?"X":".";return t},t.prototype.clone=function(){return new t(this.size,this.bits.slice())},t}();e.default=u},1022:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(752),i=r(654),a=r(1265),u=function(){function t(){}return t.fill=function(t,e){for(var r=0,n=t.length;r<n;r++)t[r]=e},t.fillWithin=function(e,r,n,o){t.rangeCheck(e.length,r,n);for(var i=r;i<n;i++)e[i]=o},t.rangeCheck=function(t,e,r){if(e>r)throw new i.default("fromIndex("+e+") > toIndex("+r+")");if(e<0)throw new a.default(e);if(r>t)throw new a.default(r)},t.asList=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t},t.create=function(t,e,r){return Array.from({length:t}).map((function(t){return Array.from({length:e}).fill(r)}))},t.createInt32Array=function(t,e,r){return Array.from({length:t}).map((function(t){return Int32Array.from({length:e}).fill(r)}))},t.equals=function(t,e){if(!t)return!1;if(!e)return!1;if(!t.length)return!1;if(!e.length)return!1;if(t.length!==e.length)return!1;for(var r=0,n=t.length;r<n;r++)if(t[r]!==e[r])return!1;return!0},t.hashCode=function(t){var e,r;if(null===t)return 0;var o=1;try{for(var i=n(t),a=i.next();!a.done;a=i.next()){o=31*o+a.value}}catch(u){e={error:u}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return o},t.fillUint8Array=function(t,e){for(var r=0;r!==t.length;r++)t[r]=e},t.copyOf=function(t,e){return t.slice(0,e)},t.copyOfUint8Array=function(t,e){if(t.length<=e){var r=new Uint8Array(e);return r.set(t),r}return t.slice(0,e)},t.copyOfRange=function(t,e,r){var n=r-e,i=new Int32Array(n);return o.default.arraycopy(t,e,i,0,n),i},t.binarySearch=function(e,r,n){void 0===n&&(n=t.numberComparator);for(var o=0,i=e.length-1;o<=i;){var a=i+o>>1,u=n(r,e[a]);if(u>0)o=a+1;else{if(!(u<0))return a;i=a-1}}return-o-1},t.numberComparator=function(t,e){return t-e},t}();e.default=u},1023:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1070),i=r(1156),a=r(898),u=r(654),s=r(1101),f=function(t){function e(e,r,n){var i=t.call(this)||this;i.primitive=e,i.size=r,i.generatorBase=n;for(var a=new Int32Array(r),u=1,s=0;s<r;s++)a[s]=u,(u*=2)>=r&&(u^=e,u&=r-1);i.expTable=a;var f=new Int32Array(r);for(s=0;s<r-1;s++)f[a[s]]=s;return i.logTable=f,i.zero=new o.default(i,Int32Array.from([0])),i.one=new o.default(i,Int32Array.from([1])),i}return n(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new u.default;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new o.default(this,r)},e.prototype.inverse=function(t){if(0===t)throw new s.default;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+a.default.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(i.default);e.default=f},1024:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1022),i=r(820),a=function(){function t(){}return t.prototype.PDF417Common=function(){},t.getBitCountSum=function(t){return i.default.sum(t)},t.toIntArray=function(e){var r,o;if(null==e||!e.length)return t.EMPTY_INT_ARRAY;var i=new Int32Array(e.length),a=0;try{for(var u=n(e),s=u.next();!s.done;s=u.next()){var f=s.value;i[a++]=f}}catch(l){r={error:l}}finally{try{s&&!s.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}return i},t.getCodeword=function(e){var r=o.default.binarySearch(t.SYMBOL_TABLE,262143&e);return r<0?-1:(t.CODEWORD_TABLE[r]-1)%t.NUMBER_OF_CODEWORDS},t.NUMBER_OF_CODEWORDS=929,t.MAX_CODEWORDS_IN_BARCODE=t.NUMBER_OF_CODEWORDS-1,t.MIN_ROWS_IN_BARCODE=3,t.MAX_ROWS_IN_BARCODE=90,t.MODULES_IN_CODEWORD=17,t.MODULES_IN_STOP_PATTERN=18,t.BARS_IN_MODULE=8,t.EMPTY_INT_ARRAY=new Int32Array([]),t.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),t.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),t}();e.default=a},1032:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o,i=r(692);!function(t){t[t.Cp437=0]="Cp437",t[t.ISO8859_1=1]="ISO8859_1",t[t.ISO8859_2=2]="ISO8859_2",t[t.ISO8859_3=3]="ISO8859_3",t[t.ISO8859_4=4]="ISO8859_4",t[t.ISO8859_5=5]="ISO8859_5",t[t.ISO8859_6=6]="ISO8859_6",t[t.ISO8859_7=7]="ISO8859_7",t[t.ISO8859_8=8]="ISO8859_8",t[t.ISO8859_9=9]="ISO8859_9",t[t.ISO8859_10=10]="ISO8859_10",t[t.ISO8859_11=11]="ISO8859_11",t[t.ISO8859_13=12]="ISO8859_13",t[t.ISO8859_14=13]="ISO8859_14",t[t.ISO8859_15=14]="ISO8859_15",t[t.ISO8859_16=15]="ISO8859_16",t[t.SJIS=16]="SJIS",t[t.Cp1250=17]="Cp1250",t[t.Cp1251=18]="Cp1251",t[t.Cp1252=19]="Cp1252",t[t.Cp1256=20]="Cp1256",t[t.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",t[t.UTF8=22]="UTF8",t[t.ASCII=23]="ASCII",t[t.Big5=24]="Big5",t[t.GB18030=25]="GB18030",t[t.EUC_KR=26]="EUC_KR"}(o=e.CharacterSetValueIdentifiers||(e.CharacterSetValueIdentifiers={}));var a=function(){function t(e,r,o){for(var i,a,u=[],s=3;s<arguments.length;s++)u[s-3]=arguments[s];this.valueIdentifier=e,this.name=o,this.values="number"===typeof r?Int32Array.from([r]):r,this.otherEncodingNames=u,t.VALUE_IDENTIFIER_TO_ECI.set(e,this),t.NAME_TO_ECI.set(o,this);for(var f=this.values,l=0,d=f.length;l!==d;l++){var c=f[l];t.VALUES_TO_ECI.set(c,this)}try{for(var h=n(u),p=h.next();!p.done;p=h.next()){var _=p.value;t.NAME_TO_ECI.set(_,this)}}catch(g){i={error:g}}finally{try{p&&!p.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}}return t.prototype.getValueIdentifier=function(){return this.valueIdentifier},t.prototype.getName=function(){return this.name},t.prototype.getValue=function(){return this.values[0]},t.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new i.default("incorect value");var r=t.VALUES_TO_ECI.get(e);if(void 0===r)throw new i.default("incorect value");return r},t.getCharacterSetECIByName=function(e){var r=t.NAME_TO_ECI.get(e);if(void 0===r)throw new i.default("incorect value");return r},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.getName()===r.getName()},t.VALUE_IDENTIFIER_TO_ECI=new Map,t.VALUES_TO_ECI=new Map,t.NAME_TO_ECI=new Map,t.Cp437=new t(o.Cp437,Int32Array.from([0,2]),"Cp437"),t.ISO8859_1=new t(o.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),t.ISO8859_2=new t(o.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),t.ISO8859_3=new t(o.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),t.ISO8859_4=new t(o.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),t.ISO8859_5=new t(o.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),t.ISO8859_6=new t(o.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),t.ISO8859_7=new t(o.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),t.ISO8859_8=new t(o.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),t.ISO8859_9=new t(o.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),t.ISO8859_10=new t(o.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),t.ISO8859_11=new t(o.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),t.ISO8859_13=new t(o.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),t.ISO8859_14=new t(o.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),t.ISO8859_15=new t(o.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),t.ISO8859_16=new t(o.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),t.SJIS=new t(o.SJIS,20,"SJIS","Shift_JIS"),t.Cp1250=new t(o.Cp1250,21,"Cp1250","windows-1250"),t.Cp1251=new t(o.Cp1251,22,"Cp1251","windows-1251"),t.Cp1252=new t(o.Cp1252,23,"Cp1252","windows-1252"),t.Cp1256=new t(o.Cp1256,24,"Cp1256","windows-1256"),t.UnicodeBigUnmarked=new t(o.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),t.UTF8=new t(o.UTF8,26,"UTF8","UTF-8"),t.ASCII=new t(o.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),t.Big5=new t(o.Big5,28,"Big5"),t.GB18030=new t(o.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),t.EUC_KR=new t(o.EUC_KR,30,"EUC_KR","EUC-KR"),t}();e.default=a},1051:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1099),o=r(1032),i=function(){function t(){}return t.decode=function(t,e){var r=this.encodingName(e);return this.customDecoder?this.customDecoder(t,r):"undefined"===typeof TextDecoder||this.shouldDecodeOnFallback(r)?this.decodeFallback(t,r):new TextDecoder(r).decode(t)},t.shouldDecodeOnFallback=function(e){return!t.isBrowser()&&"ISO-8859-1"===e},t.encode=function(t,e){var r=this.encodingName(e);return this.customEncoder?this.customEncoder(t,r):"undefined"===typeof TextEncoder?this.encodeFallback(t):(new TextEncoder).encode(t)},t.isBrowser=function(){return"undefined"!==typeof window&&"[object Window]"==={}.toString.call(window)},t.encodingName=function(t){return"string"===typeof t?t:t.getName()},t.encodingCharacterSet=function(t){return t instanceof o.default?t:o.default.getCharacterSetECIByName(t)},t.decodeFallback=function(e,r){var i=this.encodingCharacterSet(r);if(t.isDecodeFallbackSupported(i)){for(var a="",u=0,s=e.length;u<s;u++){var f=e[u].toString(16);f.length<2&&(f="0"+f),a+="%"+f}return decodeURIComponent(a)}if(i.equals(o.default.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new n.default("Encoding "+this.encodingName(r)+" not supported by fallback.")},t.isDecodeFallbackSupported=function(t){return t.equals(o.default.UTF8)||t.equals(o.default.ISO8859_1)||t.equals(o.default.ASCII)},t.encodeFallback=function(t){for(var e=btoa(unescape(encodeURIComponent(t))).split(""),r=[],n=0;n<e.length;n++)r.push(e[n].charCodeAt(0));return new Uint8Array(r)},t}();e.default=i},1052:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){var r=t.call(this,e.getWidth(),e.getHeight())||this;return r.delegate=e,r}return n(e,t),e.prototype.getRow=function(t,e){for(var r=this.delegate.getRow(t,e),n=this.getWidth(),o=0;o<n;o++)r[o]=255-(255&r[o]);return r},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),e=this.getWidth()*this.getHeight(),r=new Uint8ClampedArray(e),n=0;n<e;n++)r[n]=255-(255&t[n]);return r},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,r,n,o){return new e(this.delegate.crop(t,r,n,o))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e}(r(1053).default);e.default=o},1053:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(749),o=r(1099),i=function(){function t(t,e){this.width=t,this.height=e}return t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.isCropSupported=function(){return!1},t.prototype.crop=function(t,e,r,n){throw new o.default("This luminance source does not support cropping.")},t.prototype.isRotateSupported=function(){return!1},t.prototype.rotateCounterClockwise=function(){throw new o.default("This luminance source does not support rotation by 90 degrees.")},t.prototype.rotateCounterClockwise45=function(){throw new o.default("This luminance source does not support rotation by 45 degrees.")},t.prototype.toString=function(){for(var t=new Uint8ClampedArray(this.width),e=new n.default,r=0;r<this.height;r++){for(var o=this.getRow(r,t),i=0;i<this.width;i++){var a=255&o[i],u=void 0;u=a<64?"#":a<128?"+":a<192?".":" ",e.append(u)}e.append("\n")}return e.toString()},t}();e.default=i},1054:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r,n,o,i){void 0===o&&(o=-1),void 0===i&&(i=-1),this.rawBytes=t,this.text=e,this.byteSegments=r,this.ecLevel=n,this.structuredAppendSequenceNumber=o,this.structuredAppendParity=i,this.numBits=void 0===t||null===t?0:8*t.length}return t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.setNumBits=function(t){this.numBits=t},t.prototype.getText=function(){return this.text},t.prototype.getByteSegments=function(){return this.byteSegments},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getErrorsCorrected=function(){return this.errorsCorrected},t.prototype.setErrorsCorrected=function(t){this.errorsCorrected=t},t.prototype.getErasures=function(){return this.erasures},t.prototype.setErasures=function(t){this.erasures=t},t.prototype.getOther=function(){return this.other},t.prototype.setOther=function(t){this.other=t},t.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},t.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},t.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},t}();e.default=n},1055:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1023),o=r(1070),i=r(1157),a=r(904),u=function(){function t(t){this.field=t}return t.prototype.decode=function(t,e){for(var r=this.field,a=new o.default(r,t),u=new Int32Array(e),s=!0,f=0;f<e;f++){var l=a.evaluateAt(r.exp(f+r.getGeneratorBase()));u[u.length-1-f]=l,0!==l&&(s=!1)}if(!s){var d=new o.default(r,u),c=this.runEuclideanAlgorithm(r.buildMonomial(e,1),d,e),h=c[0],p=c[1],_=this.findErrorLocations(h),g=this.findErrorMagnitudes(p,_);for(f=0;f<_.length;f++){var v=t.length-1-r.log(_[f]);if(v<0)throw new i.default("Bad error location");t[v]=n.default.addOrSubtract(t[v],g[f])}}},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var n=t;t=e,e=n}for(var o=this.field,u=t,s=e,f=o.getZero(),l=o.getOne();s.getDegree()>=(r/2|0);){var d=u,c=f;if(f=l,(u=s).isZero())throw new i.default("r_{i-1} was zero");s=d;for(var h=o.getZero(),p=u.getCoefficient(u.getDegree()),_=o.inverse(p);s.getDegree()>=u.getDegree()&&!s.isZero();){var g=s.getDegree()-u.getDegree(),v=o.multiply(s.getCoefficient(s.getDegree()),_);h=h.addOrSubtract(o.buildMonomial(g,v)),s=s.addOrSubtract(u.multiplyByMonomial(g,v))}if(l=h.multiply(f).addOrSubtract(c),s.getDegree()>=u.getDegree())throw new a.default("Division algorithm failed to reduce polynomial?")}var w=l.getCoefficient(0);if(0===w)throw new i.default("sigmaTilde(0) was zero");var y=o.inverse(w);return[l.multiplyScalar(y),s.multiplyScalar(y)]},t.prototype.findErrorLocations=function(t){var e=t.getDegree();if(1===e)return Int32Array.from([t.getCoefficient(1)]);for(var r=new Int32Array(e),n=0,o=this.field,a=1;a<o.getSize()&&n<e;a++)0===t.evaluateAt(a)&&(r[n]=o.inverse(a),n++);if(n!==e)throw new i.default("Error locator degree does not match number of roots");return r},t.prototype.findErrorMagnitudes=function(t,e){for(var r=e.length,n=new Int32Array(r),o=this.field,i=0;i<r;i++){for(var a=o.inverse(e[i]),u=1,s=0;s<r;s++)if(i!==s){var f=o.multiply(e[s],a),l=0===(1&f)?1|f:-2&f;u=o.multiply(u,l)}n[i]=o.multiply(t.evaluateAt(a),o.inverse(u)),0!==o.getGeneratorBase()&&(n[i]=o.multiply(n[i],a))}return n},t}();e.default=u},1069:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(764),o=r(1032),i=r(1051),a=function(){function t(){}return t.castAsNonUtf8Char=function(t,e){void 0===e&&(e=null);var r=e?e.getName():this.ISO88591;return i.default.decode(new Uint8Array([t]),r)},t.guessEncoding=function(e,r){if(null!==r&&void 0!==r&&void 0!==r.get(n.default.CHARACTER_SET))return r.get(n.default.CHARACTER_SET).toString();for(var o=e.length,i=!0,a=!0,u=!0,s=0,f=0,l=0,d=0,c=0,h=0,p=0,_=0,g=0,v=0,w=0,y=e.length>3&&239===e[0]&&187===e[1]&&191===e[2],E=0;E<o&&(i||a||u);E++){var A=255&e[E];u&&(s>0?0===(128&A)?u=!1:s--:0!==(128&A)&&(0===(64&A)?u=!1:(s++,0===(32&A)?f++:(s++,0===(16&A)?l++:(s++,0===(8&A)?d++:u=!1))))),i&&(A>127&&A<160?i=!1:A>159&&(A<192||215===A||247===A)&&w++),a&&(c>0?A<64||127===A||A>252?a=!1:c--:128===A||160===A||A>239?a=!1:A>160&&A<224?(h++,_=0,++p>g&&(g=p)):A>127?(c++,p=0,++_>v&&(v=_)):(p=0,_=0))}return u&&s>0&&(u=!1),a&&c>0&&(a=!1),u&&(y||f+l+d>0)?t.UTF8:a&&(t.ASSUME_SHIFT_JIS||g>=3||v>=3)?t.SHIFT_JIS:i&&a?2===g&&2===h||10*w>=o?t.SHIFT_JIS:t.ISO88591:i?t.ISO88591:a?t.SHIFT_JIS:u?t.UTF8:t.PLATFORM_DEFAULT_ENCODING},t.format=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=-1;function o(t,r,o,i,a,u){if("%%"===t)return"%";if(void 0!==e[++n]){t=i?parseInt(i.substr(1)):void 0;var s,f=a?parseInt(a.substr(1)):void 0;switch(u){case"s":s=e[n];break;case"c":s=e[n][0];break;case"f":s=parseFloat(e[n]).toFixed(t);break;case"p":s=parseFloat(e[n]).toPrecision(t);break;case"e":s=parseFloat(e[n]).toExponential(t);break;case"x":s=parseInt(e[n]).toString(f||16);break;case"d":s=parseFloat(parseInt(e[n],f||10).toPrecision(t)).toFixed(0)}s="object"===typeof s?JSON.stringify(s):(+s).toString(f);for(var l=parseInt(o),d=o&&o[0]+""==="0"?"0":" ";s.length<l;)s=void 0!==r?s+d:d+s;return s}}var i=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return t.replace(i,o)},t.getBytes=function(t,e){return i.default.encode(t,e)},t.getCharCode=function(t,e){return void 0===e&&(e=0),t.charCodeAt(e)},t.getCharAt=function(t){return String.fromCharCode(t)},t.SHIFT_JIS=o.default.SJIS.getName(),t.GB2312="GB2312",t.ISO88591=o.default.ISO8859_1.getName(),t.EUC_JP="EUC_JP",t.UTF8=o.default.UTF8.getName(),t.PLATFORM_DEFAULT_ENCODING=t.UTF8,t.ASSUME_SHIFT_JIS=!1,t}();e.default=a},1070:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1156),o=r(752),i=r(654),a=function(){function t(t,e){if(0===e.length)throw new i.default;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(r-n),o.default.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);var e,r=this.coefficients;if(1===t){e=0;for(var o=0,i=r.length;o!==i;o++){var a=r[o];e=n.default.addOrSubtract(e,a)}return e}e=r[0];var u=r.length,s=this.field;for(o=1;o<u;o++)e=n.default.addOrSubtract(s.multiply(t,e),r[o]);return e},t.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new i.default("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,a=e.coefficients;if(r.length>a.length){var u=r;r=a,a=u}var s=new Int32Array(a.length),f=a.length-r.length;o.default.arraycopy(a,0,s,0,f);for(var l=f;l<a.length;l++)s[l]=n.default.addOrSubtract(r[l-f],a[l]);return new t(this.field,s)},t.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new i.default("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var r=this.coefficients,o=r.length,a=e.coefficients,u=a.length,s=new Int32Array(o+u-1),f=this.field,l=0;l<o;l++)for(var d=r[l],c=0;c<u;c++)s[l+c]=n.default.addOrSubtract(s[l+c],f.multiply(d,a[c]));return new t(f,s)},t.prototype.multiplyScalar=function(e){if(0===e)return this.field.getZero();if(1===e)return this;for(var r=this.coefficients.length,n=this.field,o=new Int32Array(r),i=this.coefficients,a=0;a<r;a++)o[a]=n.multiply(i[a],e);return new t(n,o)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new i.default;if(0===r)return this.field.getZero();for(var n=this.coefficients,o=n.length,a=new Int32Array(o+e),u=this.field,s=0;s<o;s++)a[s]=u.multiply(n[s],r);return new t(u,a)},t.prototype.divide=function(t){if(!this.field.equals(t.field))throw new i.default("GenericGFPolys do not have same GenericGF field");if(t.isZero())throw new i.default("Divide by 0");for(var e=this.field,r=e.getZero(),n=this,o=t.getCoefficient(t.getDegree()),a=e.inverse(o);n.getDegree()>=t.getDegree()&&!n.isZero();){var u=n.getDegree()-t.getDegree(),s=e.multiply(n.getCoefficient(n.getDegree()),a),f=t.multiplyByMonomial(u,s),l=e.buildMonomial(u,s);r=r.addOrSubtract(l),n=n.addOrSubtract(f)}return[r,n]},t.prototype.toString=function(){for(var t="",e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);if(0!==r){if(r<0?(t+=" - ",r=-r):t.length>0&&(t+=" + "),0===e||1!==r){var n=this.field.log(r);0===n?t+="1":1===n?t+="a":(t+="a^",t+=n)}0!==e&&(1===e?t+="x":(t+="x^",t+=e))}}return t},t}();e.default=a},1071:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}();e.default=n},1072:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1159),o=function(){function t(){}return t.setGridSampler=function(e){t.gridSampler=e},t.getInstance=function(){return t.gridSampler},t.gridSampler=new n.default,t}();e.default=o},1073:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(900),i=r(642),a=r(692),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.decodeRowStringBuffer="",e}return n(e,t),e.findStartGuardPattern=function(t){for(var r=!1,n=null,o=0,i=[0,0,0];!r;){i=[0,0,0];var a=(n=e.findGuardPattern(t,o,!1,this.START_END_PATTERN,i))[0],u=a-((o=n[1])-a);u>=0&&(r=t.isRange(u,a,!1))}return n},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new a.default;r+=o}r*=3;for(n=e-2;n>=0;n-=2){var o;if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new a.default;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPattern=function(t,r,n,a,u){for(var s=t.getSize(),f=0,l=r=n?t.getNextUnset(r):t.getNextSet(r),d=a.length,c=n,h=r;h<s;h++)if(t.get(h)!==c)u[f]++;else{if(f===d-1){if(o.default.patternMatchVariance(u,a,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[l,h];l+=u[0]+u[1];for(var p=u.slice(2,u.length),_=0;_<f-1;_++)u[_]=p[_];u[f-1]=0,u[f]=0,f--}else f++;u[f]=1,c=!c}throw new i.default},e.decodeDigit=function(t,r,n,a){this.recordPattern(t,n,r);for(var u=this.MAX_AVG_VARIANCE,s=-1,f=a.length,l=0;l<f;l++){var d=a[l],c=o.default.patternMatchVariance(r,d,e.MAX_INDIVIDUAL_VARIANCE);c<u&&(u=c,s=l)}if(s>=0)return s;throw new i.default},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=[1,1,1],e.MIDDLE_PATTERN=[1,1,1,1,1],e.END_PATTERN=[1,1,1,1,1,1],e.L_PATTERNS=[[3,2,1,1],[2,2,2,1],[2,1,2,2],[1,4,1,1],[1,1,3,2],[1,2,3,1],[1,1,1,4],[1,3,1,2],[1,2,1,3],[3,1,1,2]],e}(o.default);e.default=u},1074:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=r(1097),i=r(654);!function(t){t[t.L=0]="L",t[t.M=1]="M",t[t.Q=2]="Q",t[t.H=3]="H"}(n=e.ErrorCorrectionLevelValues||(e.ErrorCorrectionLevelValues={}));var a=function(){function t(e,r,n){this.value=e,this.stringValue=r,this.bits=n,t.FOR_BITS.set(n,this),t.FOR_VALUE.set(e,this)}return t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.fromString=function(e){switch(e){case"L":return t.L;case"M":return t.M;case"Q":return t.Q;case"H":return t.H;default:throw new o.default(e+"not available")}},t.prototype.toString=function(){return this.stringValue},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.forBits=function(e){if(e<0||e>=t.FOR_BITS.size)throw new i.default;return t.FOR_BITS.get(e)},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.L=new t(n.L,"L",1),t.M=new t(n.M,"M",0),t.Q=new t(n.Q,"Q",3),t.H=new t(n.H,"H",2),t}();e.default=a},1075:function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.ERROR_CORRECTION=0]="ERROR_CORRECTION",t[t.CHARACTER_SET=1]="CHARACTER_SET",t[t.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",t[t.MIN_SIZE=3]="MIN_SIZE",t[t.MAX_SIZE=4]="MAX_SIZE",t[t.MARGIN=5]="MARGIN",t[t.PDF417_COMPACT=6]="PDF417_COMPACT",t[t.PDF417_COMPACTION=7]="PDF417_COMPACTION",t[t.PDF417_DIMENSIONS=8]="PDF417_DIMENSIONS",t[t.AZTEC_LAYERS=9]="AZTEC_LAYERS",t[t.QR_VERSION=10]="QR_VERSION"}(n||(n={})),e.default=n},1076:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.encodeCompressedGtin=function(t,e){t.append("(01)");var r=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,e,r)},e.prototype.encodeCompressedGtinWithoutAI=function(t,r,n){for(var o=0;o<4;++o){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(r+10*o,10);i/100==0&&t.append("0"),i/10==0&&t.append("0"),t.append(i)}e.appendCheckDigit(t,n)},e.appendCheckDigit=function(t,e){for(var r=0,n=0;n<13;n++){var o=t.charAt(n+e).charCodeAt(0)-"0".charCodeAt(0);r+=0==(1&n)?3*o:o}10==(r=10-r%10)&&(r=0),t.append(r)},e.GTIN_SIZE=40,e}(r(1184).default);e.default=o},1097:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1098:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1099:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1100:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(791),o=r(693),i=r(764),a=r(899),u=r(752),s=r(1266),f=r(1267),l=function(){function t(){}return t.prototype.decode=function(t,e){void 0===e&&(e=null);var r=null,i=new f.default(t.getBlackMatrix()),l=null,d=null;try{l=(c=i.detectMirror(!1)).getPoints(),this.reportFoundResultPoints(e,l),d=(new s.default).decode(c)}catch(g){r=g}if(null==d)try{var c;l=(c=i.detectMirror(!0)).getPoints(),this.reportFoundResultPoints(e,l),d=(new s.default).decode(c)}catch(g){if(null!=r)throw r;throw g}var h=new n.default(d.getText(),d.getRawBytes(),d.getNumBits(),l,o.default.AZTEC,u.default.currentTimeMillis()),p=d.getByteSegments();null!=p&&h.putMetadata(a.default.BYTE_SEGMENTS,p);var _=d.getECLevel();return null!=_&&h.putMetadata(a.default.ERROR_CORRECTION_LEVEL,_),h},t.prototype.reportFoundResultPoints=function(t,e){if(null!=t){var r=t.get(i.default.NEED_RESULT_POINT_CALLBACK);null!=r&&e.forEach((function(t,e,n){r.foundPossibleResultPoint(t)}))}},t.prototype.reset=function(){},t}();e.default=l},1101:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1102:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(736),o=r(820),i=r(642),a=function(){function t(e,r,n,o){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),void 0!==r&&null!==r||(r=t.INIT_SIZE),void 0!==n&&null!==n||(n=e.getWidth()/2|0),void 0!==o&&null!==o||(o=e.getHeight()/2|0);var a=r/2|0;if(this.leftInit=n-a,this.rightInit=n+a,this.upInit=o-a,this.downInit=o+a,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new i.default}return t.prototype.detect=function(){for(var t=this.leftInit,e=this.rightInit,r=this.upInit,n=this.downInit,o=!1,a=!0,u=!1,s=!1,f=!1,l=!1,d=!1,c=this.width,h=this.height;a;){a=!1;for(var p=!0;(p||!s)&&e<c;)(p=this.containsBlackPoint(r,n,e,!1))?(e++,a=!0,s=!0):s||e++;if(e>=c){o=!0;break}for(var _=!0;(_||!f)&&n<h;)(_=this.containsBlackPoint(t,e,n,!0))?(n++,a=!0,f=!0):f||n++;if(n>=h){o=!0;break}for(var g=!0;(g||!l)&&t>=0;)(g=this.containsBlackPoint(r,n,t,!1))?(t--,a=!0,l=!0):l||t--;if(t<0){o=!0;break}for(var v=!0;(v||!d)&&r>=0;)(v=this.containsBlackPoint(t,e,r,!0))?(r--,a=!0,d=!0):d||r--;if(r<0){o=!0;break}a&&(u=!0)}if(!o&&u){for(var w=e-t,y=null,E=1;null===y&&E<w;E++)y=this.getBlackPointOnSegment(t,n-E,t+E,n);if(null==y)throw new i.default;var A=null;for(E=1;null===A&&E<w;E++)A=this.getBlackPointOnSegment(t,r+E,t+E,r);if(null==A)throw new i.default;var C=null;for(E=1;null===C&&E<w;E++)C=this.getBlackPointOnSegment(e,r+E,e-E,r);if(null==C)throw new i.default;var m=null;for(E=1;null===m&&E<w;E++)m=this.getBlackPointOnSegment(e,n-E,e-E,n);if(null==m)throw new i.default;return this.centerEdges(m,y,C,A)}throw new i.default},t.prototype.getBlackPointOnSegment=function(t,e,r,i){for(var a=o.default.round(o.default.distance(t,e,r,i)),u=(r-t)/a,s=(i-e)/a,f=this.image,l=0;l<a;l++){var d=o.default.round(t+l*u),c=o.default.round(e+l*s);if(f.get(d,c))return new n.default(d,c)}return null},t.prototype.centerEdges=function(e,r,o,i){var a=e.getX(),u=e.getY(),s=r.getX(),f=r.getY(),l=o.getX(),d=o.getY(),c=i.getX(),h=i.getY(),p=t.CORR;return a<this.width/2?[new n.default(c-p,h+p),new n.default(s+p,f+p),new n.default(l-p,d-p),new n.default(a+p,u-p)]:[new n.default(c+p,h+p),new n.default(s+p,f-p),new n.default(l-p,d+p),new n.default(a-p,u-p)]},t.prototype.containsBlackPoint=function(t,e,r,n){var o=this.image;if(n){for(var i=t;i<=e;i++)if(o.get(i,r))return!0}else for(var a=t;a<=e;a++)if(o.get(r,a))return!0;return!1},t.INIT_SIZE=10,t.CORR=1,t}();e.default=a},1103:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r,n,o,i,a,u,s){this.a11=t,this.a21=e,this.a31=r,this.a12=n,this.a22=o,this.a32=i,this.a13=a,this.a23=u,this.a33=s}return t.quadrilateralToQuadrilateral=function(e,r,n,o,i,a,u,s,f,l,d,c,h,p,_,g){var v=t.quadrilateralToSquare(e,r,n,o,i,a,u,s);return t.squareToQuadrilateral(f,l,d,c,h,p,_,g).times(v)},t.prototype.transformPoints=function(t){for(var e=t.length,r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,u=this.a23,s=this.a31,f=this.a32,l=this.a33,d=0;d<e;d+=2){var c=t[d],h=t[d+1],p=o*c+u*h+l;t[d]=(r*c+i*h+s)/p,t[d+1]=(n*c+a*h+f)/p}},t.prototype.transformPointsWithValues=function(t,e){for(var r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,u=this.a23,s=this.a31,f=this.a32,l=this.a33,d=t.length,c=0;c<d;c++){var h=t[c],p=e[c],_=o*h+u*p+l;t[c]=(r*h+i*p+s)/_,e[c]=(n*h+a*p+f)/_}},t.squareToQuadrilateral=function(e,r,n,o,i,a,u,s){var f=e-n+i-u,l=r-o+a-s;if(0===f&&0===l)return new t(n-e,i-n,e,o-r,a-o,r,0,0,1);var d=n-i,c=u-i,h=o-a,p=s-a,_=d*p-c*h,g=(f*p-c*l)/_,v=(d*l-f*h)/_;return new t(n-e+g*n,u-e+v*u,e,o-r+g*o,s-r+v*s,r,g,v,1)},t.quadrilateralToSquare=function(e,r,n,o,i,a,u,s){return t.squareToQuadrilateral(e,r,n,o,i,a,u,s).buildAdjoint()},t.prototype.buildAdjoint=function(){return new t(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},t.prototype.times=function(e){return new t(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},t}();e.default=n},1104:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(1161),a=r(1162),u=r(1163),s=r(764),f=r(1167),l=r(1271),d=r(642),c=function(t){function e(e){var r=t.call(this)||this;r.readers=[];var n=e?e.get(s.default.POSSIBLE_FORMATS):null,d=e&&void 0!==e.get(s.default.ASSUME_CODE_39_CHECK_DIGIT);return n&&((n.includes(o.default.EAN_13)||n.includes(o.default.EAN_8))&&r.readers.push(new l.default(e)),n.includes(o.default.CODE_39)&&r.readers.push(new i.default(d)),n.includes(o.default.CODE_128)&&r.readers.push(new a.default),n.includes(o.default.ITF)&&r.readers.push(new f.default),n.includes(o.default.RSS_14)&&r.readers.push(new u.default)),0===r.readers.length&&(r.readers.push(new i.default),r.readers.push(new l.default(e)),r.readers.push(new a.default),r.readers.push(new f.default),r.readers.push(new u.default)),r}return n(e,t),e.prototype.decodeRow=function(t,e,r){for(var n=0;n<this.readers.length;n++)try{return this.readers[n].decodeRow(t,e,r)}catch(o){}throw new d.default},e.prototype.reset=function(){this.readers.forEach((function(t){return t.reset()}))},e}(r(900).default);e.default=c},1105:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){this.value=t,this.checksumPortion=e}return t.prototype.getValue=function(){return this.value},t.prototype.getChecksumPortion=function(){return this.checksumPortion},t.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value&&this.checksumPortion===r.checksumPortion},t.prototype.hashCode=function(){return this.value^this.checksumPortion},t}();e.default=n},1106:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1277),o=r(791),i=r(693),a=r(764),u=r(899),s=r(825),f=r(1282),l=r(752),d=r(642),c=function(){function t(){this.decoder=new n.default}return t.prototype.decode=function(e,r){var n,s;if(void 0===r&&(r=null),null!=r&&r.has(a.default.PURE_BARCODE)){var d=t.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(d),s=t.NO_POINTS}else{var c=new f.default(e.getBlackMatrix()).detect();n=this.decoder.decode(c.getBits()),s=c.getPoints()}var h=n.getRawBytes(),p=new o.default(n.getText(),h,8*h.length,s,i.default.DATA_MATRIX,l.default.currentTimeMillis()),_=n.getByteSegments();null!=_&&p.putMetadata(u.default.BYTE_SEGMENTS,_);var g=n.getECLevel();return null!=g&&p.putMetadata(u.default.ERROR_CORRECTION_LEVEL,g),p},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null==e||null==r)throw new d.default;var n=this.moduleSize(e,t),o=e[1],i=r[1],a=e[0],u=(r[0]-a+1)/n,f=(i-o+1)/n;if(u<=0||f<=0)throw new d.default;var l=n/2;o+=l,a+=l;for(var c=new s.default(u,f),h=0;h<f;h++)for(var p=o+h*n,_=0;_<u;_++)t.get(a+_*n,p)&&c.set(_,h);return c},t.moduleSize=function(t,e){for(var r=e.getWidth(),n=t[0],o=t[1];n<r&&e.get(n,o);)n++;if(n===r)throw new d.default;var i=n-t[0];if(0===i)throw new d.default;return i},t.NO_POINTS=[],t}();e.default=c},1107:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(654),o=function(){function t(t){this.bytes=t,this.byteOffset=0,this.bitOffset=0}return t.prototype.getBitOffset=function(){return this.bitOffset},t.prototype.getByteOffset=function(){return this.byteOffset},t.prototype.readBits=function(t){if(t<1||t>32||t>this.available())throw new n.default(""+t);var e=0,r=this.bitOffset,o=this.byteOffset,i=this.bytes;if(r>0){var a=8-r,u=t<a?t:a,s=255>>8-u<<(f=a-u);e=(i[o]&s)>>f,t-=u,8===(r+=u)&&(r=0,o++)}if(t>0){for(;t>=8;)e=e<<8|255&i[o],o++,t-=8;if(t>0){var f;s=255>>(f=8-t)<<f;e=e<<t|(i[o]&s)>>f,r+=t}}return this.bitOffset=r,this.byteOffset=o,e},t.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},t}();e.default=o},1108:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(693),o=r(764),i=r(791),a=r(899),u=r(825),s=r(1284),f=r(1172),l=r(1291),d=r(642),c=function(){function t(){this.decoder=new s.default}return t.prototype.getDecoder=function(){return this.decoder},t.prototype.decode=function(e,r){var u,s;if(void 0!==r&&null!==r&&void 0!==r.get(o.default.PURE_BARCODE)){var d=t.extractPureBits(e.getBlackMatrix());u=this.decoder.decodeBitMatrix(d,r),s=t.NO_POINTS}else{var c=new l.default(e.getBlackMatrix()).detect(r);u=this.decoder.decodeBitMatrix(c.getBits(),r),s=c.getPoints()}u.getOther()instanceof f.default&&u.getOther().applyMirroredCorrection(s);var h=new i.default(u.getText(),u.getRawBytes(),void 0,s,n.default.QR_CODE,void 0),p=u.getByteSegments();null!==p&&h.putMetadata(a.default.BYTE_SEGMENTS,p);var _=u.getECLevel();return null!==_&&h.putMetadata(a.default.ERROR_CORRECTION_LEVEL,_),u.hasStructuredAppend()&&(h.putMetadata(a.default.STRUCTURED_APPEND_SEQUENCE,u.getStructuredAppendSequenceNumber()),h.putMetadata(a.default.STRUCTURED_APPEND_PARITY,u.getStructuredAppendParity())),h},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null===e||null===r)throw new d.default;var n=this.moduleSize(e,t),o=e[1],i=r[1],a=e[0],s=r[0];if(a>=s||o>=i)throw new d.default;if(i-o!==s-a&&(s=a+(i-o))>=t.getWidth())throw new d.default;var f=Math.round((s-a+1)/n),l=Math.round((i-o+1)/n);if(f<=0||l<=0)throw new d.default;if(l!==f)throw new d.default;var c=Math.floor(n/2);o+=c;var h=(a+=c)+Math.floor((f-1)*n)-s;if(h>0){if(h>c)throw new d.default;a-=h}var p=o+Math.floor((l-1)*n)-i;if(p>0){if(p>c)throw new d.default;o-=p}for(var _=new u.default(f,l),g=0;g<l;g++)for(var v=o+Math.floor(g*n),w=0;w<f;w++)t.get(a+Math.floor(w*n),v)&&_.set(w,g);return _},t.moduleSize=function(t,e){for(var r=e.getHeight(),n=e.getWidth(),o=t[0],i=t[1],a=!0,u=0;o<n&&i<r;){if(a!==e.get(o,i)){if(5===++u)break;a=!a}o++,i++}if(o===n||i===r)throw new d.default;return(o-t[0])/7},t.NO_POINTS=new Array,t}();e.default=c},1109:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(825),i=r(1171),a=r(1286),u=r(1287),s=r(692),f=r(654),l=function(){function t(t,e){for(var r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];this.versionNumber=t,this.alignmentPatternCenters=e,this.ecBlocks=i;var u=0,s=i[0].getECCodewordsPerBlock(),f=i[0].getECBlocks();try{for(var l=n(f),d=l.next();!d.done;d=l.next()){var c=d.value;u+=c.getCount()*(c.getDataCodewords()+s)}}catch(h){r={error:h}}finally{try{d&&!d.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}this.totalCodewords=u}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},t.prototype.getECBlocksForLevel=function(t){return this.ecBlocks[t.getValue()]},t.getProvisionalVersionForDimension=function(t){if(t%4!==1)throw new s.default;try{return this.getVersionForNumber((t-17)/4)}catch(e){throw new s.default}},t.getVersionForNumber=function(e){if(e<1||e>40)throw new f.default;return t.VERSIONS[e-1]},t.decodeVersionInformation=function(e){for(var r=Number.MAX_SAFE_INTEGER,n=0,o=0;o<t.VERSION_DECODE_INFO.length;o++){var a=t.VERSION_DECODE_INFO[o];if(a===e)return t.getVersionForNumber(o+7);var u=i.default.numBitsDiffering(e,a);u<r&&(n=o+7,r=u)}return r<=3?t.getVersionForNumber(n):null},t.prototype.buildFunctionPattern=function(){var t=this.getDimensionForVersion(),e=new o.default(t);e.setRegion(0,0,9,9),e.setRegion(t-8,0,8,9),e.setRegion(0,t-8,9,8);for(var r=this.alignmentPatternCenters.length,n=0;n<r;n++)for(var i=this.alignmentPatternCenters[n]-2,a=0;a<r;a++)0===n&&(0===a||a===r-1)||n===r-1&&0===a||e.setRegion(this.alignmentPatternCenters[a]-2,i,5,5);return e.setRegion(6,9,1,t-17),e.setRegion(9,6,t-17,1),this.versionNumber>6&&(e.setRegion(t-11,0,3,6),e.setRegion(0,t-11,6,3)),e},t.prototype.toString=function(){return""+this.versionNumber},t.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),t.VERSIONS=[new t(1,new Int32Array(0),new a.default(7,new u.default(1,19)),new a.default(10,new u.default(1,16)),new a.default(13,new u.default(1,13)),new a.default(17,new u.default(1,9))),new t(2,Int32Array.from([6,18]),new a.default(10,new u.default(1,34)),new a.default(16,new u.default(1,28)),new a.default(22,new u.default(1,22)),new a.default(28,new u.default(1,16))),new t(3,Int32Array.from([6,22]),new a.default(15,new u.default(1,55)),new a.default(26,new u.default(1,44)),new a.default(18,new u.default(2,17)),new a.default(22,new u.default(2,13))),new t(4,Int32Array.from([6,26]),new a.default(20,new u.default(1,80)),new a.default(18,new u.default(2,32)),new a.default(26,new u.default(2,24)),new a.default(16,new u.default(4,9))),new t(5,Int32Array.from([6,30]),new a.default(26,new u.default(1,108)),new a.default(24,new u.default(2,43)),new a.default(18,new u.default(2,15),new u.default(2,16)),new a.default(22,new u.default(2,11),new u.default(2,12))),new t(6,Int32Array.from([6,34]),new a.default(18,new u.default(2,68)),new a.default(16,new u.default(4,27)),new a.default(24,new u.default(4,19)),new a.default(28,new u.default(4,15))),new t(7,Int32Array.from([6,22,38]),new a.default(20,new u.default(2,78)),new a.default(18,new u.default(4,31)),new a.default(18,new u.default(2,14),new u.default(4,15)),new a.default(26,new u.default(4,13),new u.default(1,14))),new t(8,Int32Array.from([6,24,42]),new a.default(24,new u.default(2,97)),new a.default(22,new u.default(2,38),new u.default(2,39)),new a.default(22,new u.default(4,18),new u.default(2,19)),new a.default(26,new u.default(4,14),new u.default(2,15))),new t(9,Int32Array.from([6,26,46]),new a.default(30,new u.default(2,116)),new a.default(22,new u.default(3,36),new u.default(2,37)),new a.default(20,new u.default(4,16),new u.default(4,17)),new a.default(24,new u.default(4,12),new u.default(4,13))),new t(10,Int32Array.from([6,28,50]),new a.default(18,new u.default(2,68),new u.default(2,69)),new a.default(26,new u.default(4,43),new u.default(1,44)),new a.default(24,new u.default(6,19),new u.default(2,20)),new a.default(28,new u.default(6,15),new u.default(2,16))),new t(11,Int32Array.from([6,30,54]),new a.default(20,new u.default(4,81)),new a.default(30,new u.default(1,50),new u.default(4,51)),new a.default(28,new u.default(4,22),new u.default(4,23)),new a.default(24,new u.default(3,12),new u.default(8,13))),new t(12,Int32Array.from([6,32,58]),new a.default(24,new u.default(2,92),new u.default(2,93)),new a.default(22,new u.default(6,36),new u.default(2,37)),new a.default(26,new u.default(4,20),new u.default(6,21)),new a.default(28,new u.default(7,14),new u.default(4,15))),new t(13,Int32Array.from([6,34,62]),new a.default(26,new u.default(4,107)),new a.default(22,new u.default(8,37),new u.default(1,38)),new a.default(24,new u.default(8,20),new u.default(4,21)),new a.default(22,new u.default(12,11),new u.default(4,12))),new t(14,Int32Array.from([6,26,46,66]),new a.default(30,new u.default(3,115),new u.default(1,116)),new a.default(24,new u.default(4,40),new u.default(5,41)),new a.default(20,new u.default(11,16),new u.default(5,17)),new a.default(24,new u.default(11,12),new u.default(5,13))),new t(15,Int32Array.from([6,26,48,70]),new a.default(22,new u.default(5,87),new u.default(1,88)),new a.default(24,new u.default(5,41),new u.default(5,42)),new a.default(30,new u.default(5,24),new u.default(7,25)),new a.default(24,new u.default(11,12),new u.default(7,13))),new t(16,Int32Array.from([6,26,50,74]),new a.default(24,new u.default(5,98),new u.default(1,99)),new a.default(28,new u.default(7,45),new u.default(3,46)),new a.default(24,new u.default(15,19),new u.default(2,20)),new a.default(30,new u.default(3,15),new u.default(13,16))),new t(17,Int32Array.from([6,30,54,78]),new a.default(28,new u.default(1,107),new u.default(5,108)),new a.default(28,new u.default(10,46),new u.default(1,47)),new a.default(28,new u.default(1,22),new u.default(15,23)),new a.default(28,new u.default(2,14),new u.default(17,15))),new t(18,Int32Array.from([6,30,56,82]),new a.default(30,new u.default(5,120),new u.default(1,121)),new a.default(26,new u.default(9,43),new u.default(4,44)),new a.default(28,new u.default(17,22),new u.default(1,23)),new a.default(28,new u.default(2,14),new u.default(19,15))),new t(19,Int32Array.from([6,30,58,86]),new a.default(28,new u.default(3,113),new u.default(4,114)),new a.default(26,new u.default(3,44),new u.default(11,45)),new a.default(26,new u.default(17,21),new u.default(4,22)),new a.default(26,new u.default(9,13),new u.default(16,14))),new t(20,Int32Array.from([6,34,62,90]),new a.default(28,new u.default(3,107),new u.default(5,108)),new a.default(26,new u.default(3,41),new u.default(13,42)),new a.default(30,new u.default(15,24),new u.default(5,25)),new a.default(28,new u.default(15,15),new u.default(10,16))),new t(21,Int32Array.from([6,28,50,72,94]),new a.default(28,new u.default(4,116),new u.default(4,117)),new a.default(26,new u.default(17,42)),new a.default(28,new u.default(17,22),new u.default(6,23)),new a.default(30,new u.default(19,16),new u.default(6,17))),new t(22,Int32Array.from([6,26,50,74,98]),new a.default(28,new u.default(2,111),new u.default(7,112)),new a.default(28,new u.default(17,46)),new a.default(30,new u.default(7,24),new u.default(16,25)),new a.default(24,new u.default(34,13))),new t(23,Int32Array.from([6,30,54,78,102]),new a.default(30,new u.default(4,121),new u.default(5,122)),new a.default(28,new u.default(4,47),new u.default(14,48)),new a.default(30,new u.default(11,24),new u.default(14,25)),new a.default(30,new u.default(16,15),new u.default(14,16))),new t(24,Int32Array.from([6,28,54,80,106]),new a.default(30,new u.default(6,117),new u.default(4,118)),new a.default(28,new u.default(6,45),new u.default(14,46)),new a.default(30,new u.default(11,24),new u.default(16,25)),new a.default(30,new u.default(30,16),new u.default(2,17))),new t(25,Int32Array.from([6,32,58,84,110]),new a.default(26,new u.default(8,106),new u.default(4,107)),new a.default(28,new u.default(8,47),new u.default(13,48)),new a.default(30,new u.default(7,24),new u.default(22,25)),new a.default(30,new u.default(22,15),new u.default(13,16))),new t(26,Int32Array.from([6,30,58,86,114]),new a.default(28,new u.default(10,114),new u.default(2,115)),new a.default(28,new u.default(19,46),new u.default(4,47)),new a.default(28,new u.default(28,22),new u.default(6,23)),new a.default(30,new u.default(33,16),new u.default(4,17))),new t(27,Int32Array.from([6,34,62,90,118]),new a.default(30,new u.default(8,122),new u.default(4,123)),new a.default(28,new u.default(22,45),new u.default(3,46)),new a.default(30,new u.default(8,23),new u.default(26,24)),new a.default(30,new u.default(12,15),new u.default(28,16))),new t(28,Int32Array.from([6,26,50,74,98,122]),new a.default(30,new u.default(3,117),new u.default(10,118)),new a.default(28,new u.default(3,45),new u.default(23,46)),new a.default(30,new u.default(4,24),new u.default(31,25)),new a.default(30,new u.default(11,15),new u.default(31,16))),new t(29,Int32Array.from([6,30,54,78,102,126]),new a.default(30,new u.default(7,116),new u.default(7,117)),new a.default(28,new u.default(21,45),new u.default(7,46)),new a.default(30,new u.default(1,23),new u.default(37,24)),new a.default(30,new u.default(19,15),new u.default(26,16))),new t(30,Int32Array.from([6,26,52,78,104,130]),new a.default(30,new u.default(5,115),new u.default(10,116)),new a.default(28,new u.default(19,47),new u.default(10,48)),new a.default(30,new u.default(15,24),new u.default(25,25)),new a.default(30,new u.default(23,15),new u.default(25,16))),new t(31,Int32Array.from([6,30,56,82,108,134]),new a.default(30,new u.default(13,115),new u.default(3,116)),new a.default(28,new u.default(2,46),new u.default(29,47)),new a.default(30,new u.default(42,24),new u.default(1,25)),new a.default(30,new u.default(23,15),new u.default(28,16))),new t(32,Int32Array.from([6,34,60,86,112,138]),new a.default(30,new u.default(17,115)),new a.default(28,new u.default(10,46),new u.default(23,47)),new a.default(30,new u.default(10,24),new u.default(35,25)),new a.default(30,new u.default(19,15),new u.default(35,16))),new t(33,Int32Array.from([6,30,58,86,114,142]),new a.default(30,new u.default(17,115),new u.default(1,116)),new a.default(28,new u.default(14,46),new u.default(21,47)),new a.default(30,new u.default(29,24),new u.default(19,25)),new a.default(30,new u.default(11,15),new u.default(46,16))),new t(34,Int32Array.from([6,34,62,90,118,146]),new a.default(30,new u.default(13,115),new u.default(6,116)),new a.default(28,new u.default(14,46),new u.default(23,47)),new a.default(30,new u.default(44,24),new u.default(7,25)),new a.default(30,new u.default(59,16),new u.default(1,17))),new t(35,Int32Array.from([6,30,54,78,102,126,150]),new a.default(30,new u.default(12,121),new u.default(7,122)),new a.default(28,new u.default(12,47),new u.default(26,48)),new a.default(30,new u.default(39,24),new u.default(14,25)),new a.default(30,new u.default(22,15),new u.default(41,16))),new t(36,Int32Array.from([6,24,50,76,102,128,154]),new a.default(30,new u.default(6,121),new u.default(14,122)),new a.default(28,new u.default(6,47),new u.default(34,48)),new a.default(30,new u.default(46,24),new u.default(10,25)),new a.default(30,new u.default(2,15),new u.default(64,16))),new t(37,Int32Array.from([6,28,54,80,106,132,158]),new a.default(30,new u.default(17,122),new u.default(4,123)),new a.default(28,new u.default(29,46),new u.default(14,47)),new a.default(30,new u.default(49,24),new u.default(10,25)),new a.default(30,new u.default(24,15),new u.default(46,16))),new t(38,Int32Array.from([6,32,58,84,110,136,162]),new a.default(30,new u.default(4,122),new u.default(18,123)),new a.default(28,new u.default(13,46),new u.default(32,47)),new a.default(30,new u.default(48,24),new u.default(14,25)),new a.default(30,new u.default(42,15),new u.default(32,16))),new t(39,Int32Array.from([6,26,54,82,110,138,166]),new a.default(30,new u.default(20,117),new u.default(4,118)),new a.default(28,new u.default(40,47),new u.default(7,48)),new a.default(30,new u.default(43,24),new u.default(22,25)),new a.default(30,new u.default(10,15),new u.default(67,16))),new t(40,Int32Array.from([6,30,58,86,114,142,170]),new a.default(30,new u.default(19,118),new u.default(6,119)),new a.default(28,new u.default(18,47),new u.default(31,48)),new a.default(30,new u.default(34,24),new u.default(34,25)),new a.default(30,new u.default(20,15),new u.default(61,16)))],t}();e.default=l},1110:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){this.buffer=""}return t.form=function(t,e){var r=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,(function(t,n,o,i,a,u){if("%%"===t)return"%";if(void 0!==e[++r]){t=i?parseInt(i.substr(1)):void 0;var s,f=a?parseInt(a.substr(1)):void 0;switch(u){case"s":s=e[r];break;case"c":s=e[r][0];break;case"f":s=parseFloat(e[r]).toFixed(t);break;case"p":s=parseFloat(e[r]).toPrecision(t);break;case"e":s=parseFloat(e[r]).toExponential(t);break;case"x":s=parseInt(e[r]).toString(f||16);break;case"d":s=parseFloat(parseInt(e[r],f||10).toPrecision(t)).toFixed(0)}s="object"===typeof s?JSON.stringify(s):(+s).toString(f);for(var l=parseInt(o),d=o&&o[0]+""==="0"?"0":" ";s.length<l;)s=void 0!==n?s+d:d+s;return s}}))},t.prototype.format=function(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];this.buffer+=t.form(e,r)},t.prototype.toString=function(){return this.buffer},t}();e.default=n},1111:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1075),i=r(1021),a=r(1032),u=r(1023),s=r(1180),f=r(1173),l=r(1109),d=r(1181),c=r(1318),h=r(1112),p=r(1319),_=r(1051),g=r(1320),v=r(1113),w=function(){function t(){}return t.calculateMaskPenalty=function(t){return d.default.applyMaskPenaltyRule1(t)+d.default.applyMaskPenaltyRule2(t)+d.default.applyMaskPenaltyRule3(t)+d.default.applyMaskPenaltyRule4(t)},t.encode=function(e,r,n){void 0===n&&(n=null);var u=t.DEFAULT_BYTE_MODE_ENCODING,s=null!==n&&void 0!==n.get(o.default.CHARACTER_SET);s&&(u=n.get(o.default.CHARACTER_SET).toString());var d=this.chooseMode(e,u),_=new i.default;if(d===f.default.BYTE&&(s||t.DEFAULT_BYTE_MODE_ENCODING!==u)){var g=a.default.getCharacterSetECIByName(u);void 0!==g&&this.appendECI(g,_)}this.appendModeInfo(d,_);var w,y=new i.default;if(this.appendBytes(e,d,y,u),null!==n&&void 0!==n.get(o.default.QR_VERSION)){var E=Number.parseInt(n.get(o.default.QR_VERSION).toString(),10);w=l.default.getVersionForNumber(E);var A=this.calculateBitsNeeded(d,_,y,w);if(!this.willFit(A,w,r))throw new v.default("Data too big for requested version")}else w=this.recommendVersion(r,d,_,y);var C=new i.default;C.appendBitArray(_);var m=d===f.default.BYTE?y.getSizeInBytes():e.length;this.appendLengthInfo(m,w,d,C),C.appendBitArray(y);var I=w.getECBlocksForLevel(r),S=w.getTotalCodewords()-I.getTotalECCodewords();this.terminateBits(S,C);var O=this.interleaveWithECBytes(C,w.getTotalCodewords(),S,I.getNumBlocks()),T=new h.default;T.setECLevel(r),T.setMode(d),T.setVersion(w);var R=w.getDimensionForVersion(),b=new c.default(R,R),N=this.chooseMaskPattern(O,r,w,b);return T.setMaskPattern(N),p.default.buildMatrix(O,r,w,N,b),T.setMatrix(b),T},t.recommendVersion=function(t,e,r,n){var o=this.calculateBitsNeeded(e,r,n,l.default.getVersionForNumber(1)),i=this.chooseVersion(o,t),a=this.calculateBitsNeeded(e,r,n,i);return this.chooseVersion(a,t)},t.calculateBitsNeeded=function(t,e,r,n){return e.getSize()+t.getCharacterCountBits(n)+r.getSize()},t.getAlphanumericCode=function(e){return e<t.ALPHANUMERIC_TABLE.length?t.ALPHANUMERIC_TABLE[e]:-1},t.chooseMode=function(e,r){if(void 0===r&&(r=null),a.default.SJIS.getName()===r&&this.isOnlyDoubleByteKanji(e))return f.default.KANJI;for(var n=!1,o=!1,i=0,u=e.length;i<u;++i){var s=e.charAt(i);if(t.isDigit(s))n=!0;else{if(-1===this.getAlphanumericCode(s.charCodeAt(0)))return f.default.BYTE;o=!0}}return o?f.default.ALPHANUMERIC:n?f.default.NUMERIC:f.default.BYTE},t.isOnlyDoubleByteKanji=function(t){var e;try{e=_.default.encode(t,a.default.SJIS)}catch(i){return!1}var r=e.length;if(r%2!==0)return!1;for(var n=0;n<r;n+=2){var o=255&e[n];if((o<129||o>159)&&(o<224||o>235))return!1}return!0},t.chooseMaskPattern=function(t,e,r,n){for(var o=Number.MAX_SAFE_INTEGER,i=-1,a=0;a<h.default.NUM_MASK_PATTERNS;a++){p.default.buildMatrix(t,e,r,a,n);var u=this.calculateMaskPenalty(n);u<o&&(o=u,i=a)}return i},t.chooseVersion=function(e,r){for(var n=1;n<=40;n++){var o=l.default.getVersionForNumber(n);if(t.willFit(e,o,r))return o}throw new v.default("Data too big")},t.willFit=function(t,e,r){return e.getTotalCodewords()-e.getECBlocksForLevel(r).getTotalECCodewords()>=(t+7)/8},t.terminateBits=function(t,e){var r=8*t;if(e.getSize()>r)throw new v.default("data bits cannot fit in the QR Code"+e.getSize()+" > "+r);for(var n=0;n<4&&e.getSize()<r;++n)e.appendBit(!1);var o=7&e.getSize();if(o>0)for(n=o;n<8;n++)e.appendBit(!1);var i=t-e.getSizeInBytes();for(n=0;n<i;++n)e.appendBits(0===(1&n)?236:17,8);if(e.getSize()!==r)throw new v.default("Bits size does not equal capacity")},t.getNumDataBytesAndNumECBytesForBlockID=function(t,e,r,n,o,i){if(n>=r)throw new v.default("Block ID too large");var a=t%r,u=r-a,s=Math.floor(t/r),f=s+1,l=Math.floor(e/r),d=l+1,c=s-l,h=f-d;if(c!==h)throw new v.default("EC bytes mismatch");if(r!==u+a)throw new v.default("RS blocks mismatch");if(t!==(l+c)*u+(d+h)*a)throw new v.default("Total bytes mismatch");n<u?(o[0]=l,i[0]=c):(o[0]=d,i[0]=h)},t.interleaveWithECBytes=function(e,r,o,a){var u,s,f,l;if(e.getSizeInBytes()!==o)throw new v.default("Number of bits and data bytes does not match");for(var d=0,c=0,h=0,p=new Array,_=0;_<a;++_){var w=new Int32Array(1),y=new Int32Array(1);t.getNumDataBytesAndNumECBytesForBlockID(r,o,a,_,w,y);var E=w[0],A=new Uint8Array(E);e.toBytes(8*d,A,0,E);var C=t.generateECBytes(A,y[0]);p.push(new g.default(A,C)),c=Math.max(c,E),h=Math.max(h,C.length),d+=w[0]}if(o!==d)throw new v.default("Data bytes does not match offset");var m=new i.default;for(_=0;_<c;++_)try{for(var I=n(p),S=I.next();!S.done;S=I.next()){_<(A=S.value.getDataBytes()).length&&m.appendBits(A[_],8)}}catch(R){u={error:R}}finally{try{S&&!S.done&&(s=I.return)&&s.call(I)}finally{if(u)throw u.error}}for(_=0;_<h;++_)try{for(var O=n(p),T=O.next();!T.done;T=O.next()){_<(C=T.value.getErrorCorrectionBytes()).length&&m.appendBits(C[_],8)}}catch(b){f={error:b}}finally{try{T&&!T.done&&(l=O.return)&&l.call(O)}finally{if(f)throw f.error}}if(r!==m.getSizeInBytes())throw new v.default("Interleaving error: "+r+" and "+m.getSizeInBytes()+" differ.");return m},t.generateECBytes=function(t,e){for(var r=t.length,n=new Int32Array(r+e),o=0;o<r;o++)n[o]=255&t[o];new s.default(u.default.QR_CODE_FIELD_256).encode(n,e);var i=new Uint8Array(e);for(o=0;o<e;o++)i[o]=n[r+o];return i},t.appendModeInfo=function(t,e){e.appendBits(t.getBits(),4)},t.appendLengthInfo=function(t,e,r,n){var o=r.getCharacterCountBits(e);if(t>=1<<o)throw new v.default(t+" is bigger than "+((1<<o)-1));n.appendBits(t,o)},t.appendBytes=function(e,r,n,o){switch(r){case f.default.NUMERIC:t.appendNumericBytes(e,n);break;case f.default.ALPHANUMERIC:t.appendAlphanumericBytes(e,n);break;case f.default.BYTE:t.append8BitBytes(e,n,o);break;case f.default.KANJI:t.appendKanjiBytes(e,n);break;default:throw new v.default("Invalid mode: "+r)}},t.getDigit=function(t){return t.charCodeAt(0)-48},t.isDigit=function(e){var r=t.getDigit(e);return r>=0&&r<=9},t.appendNumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getDigit(e.charAt(o));if(o+2<n){var a=t.getDigit(e.charAt(o+1)),u=t.getDigit(e.charAt(o+2));r.appendBits(100*i+10*a+u,10),o+=3}else if(o+1<n){a=t.getDigit(e.charAt(o+1));r.appendBits(10*i+a,7),o+=2}else r.appendBits(i,4),o++}},t.appendAlphanumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getAlphanumericCode(e.charCodeAt(o));if(-1===i)throw new v.default;if(o+1<n){var a=t.getAlphanumericCode(e.charCodeAt(o+1));if(-1===a)throw new v.default;r.appendBits(45*i+a,11),o+=2}else r.appendBits(i,6),o++}},t.append8BitBytes=function(t,e,r){var n;try{n=_.default.encode(t,r)}catch(u){throw new v.default(u)}for(var o=0,i=n.length;o!==i;o++){var a=n[o];e.appendBits(a,8)}},t.appendKanjiBytes=function(t,e){var r;try{r=_.default.encode(t,a.default.SJIS)}catch(f){throw new v.default(f)}for(var n=r.length,o=0;o<n;o+=2){var i=(255&r[o])<<8&4294967295|255&r[o+1],u=-1;if(i>=33088&&i<=40956?u=i-33088:i>=57408&&i<=60351&&(u=i-49472),-1===u)throw new v.default("Invalid byte sequence");var s=192*(u>>8)+(255&u);e.appendBits(s,13)}},t.appendECI=function(t,e){e.appendBits(f.default.ECI.getBits(),4),e.appendBits(t.getValue(),8)},t.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),t.DEFAULT_BYTE_MODE_ENCODING=a.default.UTF8.getName(),t}();e.default=w},1112:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(749),o=function(){function t(){this.maskPattern=-1}return t.prototype.getMode=function(){return this.mode},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getVersion=function(){return this.version},t.prototype.getMaskPattern=function(){return this.maskPattern},t.prototype.getMatrix=function(){return this.matrix},t.prototype.toString=function(){var t=new n.default;return t.append("<<\n"),t.append(" mode: "),t.append(this.mode?this.mode.toString():"null"),t.append("\n ecLevel: "),t.append(this.ecLevel?this.ecLevel.toString():"null"),t.append("\n version: "),t.append(this.version?this.version.toString():"null"),t.append("\n maskPattern: "),t.append(this.maskPattern.toString()),this.matrix?(t.append("\n matrix:\n"),t.append(this.matrix.toString())):t.append("\n matrix: null\n"),t.append(">>\n"),t.toString()},t.prototype.setMode=function(t){this.mode=t},t.prototype.setECLevel=function(t){this.ecLevel=t},t.prototype.setVersion=function(t){this.version=t},t.prototype.setMaskPattern=function(t){this.maskPattern=t},t.prototype.setMatrix=function(t){this.matrix=t},t.isValidMaskPattern=function(e){return e>=0&&e<t.NUM_MASK_PATTERNS},t.NUM_MASK_PATTERNS=8,t}();e.default=o},1113:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1114:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t){this.newPosition=t}return t.prototype.getNewPosition=function(){return this.newPosition},t}();e.default=n},1149:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}(r(1262));var n=r(1097);e.ArgumentException=n.default;var o=r(1101);e.ArithmeticException=o.default;var i=r(824);e.ChecksumException=i.default;var a=r(763);e.Exception=a.default;var u=r(692);e.FormatException=u.default;var s=r(654);e.IllegalArgumentException=s.default;var f=r(904);e.IllegalStateException=f.default;var l=r(642);e.NotFoundException=l.default;var d=r(1179);e.ReaderException=d.default;var c=r(1157);e.ReedSolomonException=c.default;var h=r(1099);e.UnsupportedOperationException=h.default;var p=r(1113);e.WriterException=p.default;var _=r(693);e.BarcodeFormat=_.default;var g=r(1153);e.Binarizer=g.default;var v=r(1150);e.BinaryBitmap=v.default;var w=r(764);e.DecodeHintType=w.default;var y=r(1052);e.InvertedLuminanceSource=y.default;var E=r(1053);e.LuminanceSource=E.default;var A=r(1170);e.MultiFormatReader=A.default;var C=r(1321);e.MultiFormatWriter=C.default;var m=r(1322);e.PlanarYUVLuminanceSource=m.default;var I=r(791);e.Result=I.default;var S=r(899);e.ResultMetadataType=S.default;var O=r(1323);e.RGBLuminanceSource=O.default;var T=r(1021);e.BitArray=T.default;var R=r(825);e.BitMatrix=R.default;var b=r(1107);e.BitSource=b.default;var N=r(1032);e.CharacterSetECI=N.default;var M=r(1054);e.DecoderResult=M.default;var P=r(1159);e.DefaultGridSampler=P.default;var D=r(1071);e.DetectorResult=D.default;var B=r(1075);e.EncodeHintType=B.default;var L=r(1152);e.GlobalHistogramBinarizer=L.default;var F=r(1160);e.GridSampler=F.default;var x=r(1072);e.GridSamplerInstance=x.default;var k=r(1151);e.HybridBinarizer=k.default;var V=r(1103);e.PerspectiveTransform=V.default;var U=r(1069);e.StringUtils=U.default;var H=r(820);e.MathUtils=H.default;var j=r(1102);e.WhiteRectangleDetector=j.default;var G=r(1023);e.GenericGF=G.default;var X=r(1070);e.GenericGFPoly=X.default;var W=r(1055);e.ReedSolomonDecoder=W.default;var z=r(1180);e.ReedSolomonEncoder=z.default;var Y=r(1106);e.DataMatrixReader=Y.default;var Z=r(1108);e.QRCodeReader=Z.default;var K=r(1182);e.QRCodeWriter=K.default;var q=r(1074);e.QRCodeDecoderErrorCorrectionLevel=q.default;var Q=r(1111);e.QRCodeEncoder=Q.default;var J=r(1112);e.QRCodeEncoderQRCode=J.default;var $=r(1100);e.AztecCodeReader=$.default;var tt=r(900);e.OneDReader=tt.default;var et=r(1168);e.EAN13Reader=et.default;var rt=r(1162);e.Code128Reader=rt.default;var nt=r(1167);e.ITFReader=nt.default;var ot=r(1161);e.Code39Reader=ot.default;var it=r(1163);e.RSS14Reader=it.default;var at=r(1324);e.RSSExpandedReader=at.default;var ut=r(1104);e.MultiformatReader=ut.default},1150:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(654),o=function(){function t(t){if(this.binarizer=t,null===t)throw new n.default("Binarizer must be non-null.")}return t.prototype.getWidth=function(){return this.binarizer.getWidth()},t.prototype.getHeight=function(){return this.binarizer.getHeight()},t.prototype.getBlackRow=function(t,e){return this.binarizer.getBlackRow(t,e)},t.prototype.getBlackMatrix=function(){return null!==this.matrix&&void 0!==this.matrix||(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},t.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},t.prototype.crop=function(e,r,n,o){var i=this.binarizer.getLuminanceSource().crop(e,r,n,o);return new t(this.binarizer.createBinarizer(i))},t.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},t.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new t(this.binarizer.createBinarizer(e))},t.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new t(this.binarizer.createBinarizer(e))},t.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch(t){return""}},t}();e.default=o},1151:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1152),i=r(825),a=function(t){function e(e){var r=t.call(this,e)||this;return r.matrix=null,r}return n(e,t),e.prototype.getBlackMatrix=function(){if(null!==this.matrix)return this.matrix;var r=this.getLuminanceSource(),n=r.getWidth(),o=r.getHeight();if(n>=e.MINIMUM_DIMENSION&&o>=e.MINIMUM_DIMENSION){var a=r.getMatrix(),u=n>>e.BLOCK_SIZE_POWER;0!==(n&e.BLOCK_SIZE_MASK)&&u++;var s=o>>e.BLOCK_SIZE_POWER;0!==(o&e.BLOCK_SIZE_MASK)&&s++;var f=e.calculateBlackPoints(a,u,s,n,o),l=new i.default(n,o);e.calculateThresholdForBlock(a,u,s,n,o,f,l),this.matrix=l}else this.matrix=t.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,r,n,o,i,a,u){for(var s=i-e.BLOCK_SIZE,f=o-e.BLOCK_SIZE,l=0;l<n;l++){var d=l<<e.BLOCK_SIZE_POWER;d>s&&(d=s);for(var c=e.cap(l,2,n-3),h=0;h<r;h++){var p=h<<e.BLOCK_SIZE_POWER;p>f&&(p=f);for(var _=e.cap(h,2,r-3),g=0,v=-2;v<=2;v++){var w=a[c+v];g+=w[_-2]+w[_-1]+w[_]+w[_+1]+w[_+2]}var y=g/25;e.thresholdBlock(t,p,d,y,o,u)}}},e.cap=function(t,e,r){return t<e?e:t>r?r:t},e.thresholdBlock=function(t,r,n,o,i,a){for(var u=0,s=n*i+r;u<e.BLOCK_SIZE;u++,s+=i)for(var f=0;f<e.BLOCK_SIZE;f++)(255&t[s+f])<=o&&a.set(r+f,n+u)},e.calculateBlackPoints=function(t,r,n,o,i){for(var a=i-e.BLOCK_SIZE,u=o-e.BLOCK_SIZE,s=new Array(n),f=0;f<n;f++){s[f]=new Int32Array(r);var l=f<<e.BLOCK_SIZE_POWER;l>a&&(l=a);for(var d=0;d<r;d++){var c=d<<e.BLOCK_SIZE_POWER;c>u&&(c=u);for(var h=0,p=255,_=0,g=0,v=l*o+c;g<e.BLOCK_SIZE;g++,v+=o){for(var w=0;w<e.BLOCK_SIZE;w++){var y=255&t[v+w];h+=y,y<p&&(p=y),y>_&&(_=y)}if(_-p>e.MIN_DYNAMIC_RANGE)for(g++,v+=o;g<e.BLOCK_SIZE;g++,v+=o)for(w=0;w<e.BLOCK_SIZE;w++)h+=255&t[v+w]}var E=h>>2*e.BLOCK_SIZE_POWER;if(_-p<=e.MIN_DYNAMIC_RANGE&&(E=p/2,f>0&&d>0)){var A=(s[f-1][d]+2*s[f][d-1]+s[f-1][d-1])/4;p<A&&(E=A)}s[f][d]=E}}return s},e.BLOCK_SIZE_POWER=3,e.BLOCK_SIZE=1<<e.BLOCK_SIZE_POWER,e.BLOCK_SIZE_MASK=e.BLOCK_SIZE-1,e.MINIMUM_DIMENSION=5*e.BLOCK_SIZE,e.MIN_DYNAMIC_RANGE=24,e}(o.default);e.default=a},1152:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1153),i=r(1021),a=r(825),u=r(642),s=function(t){function e(r){var n=t.call(this,r)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return n(e,t),e.prototype.getBlackRow=function(t,r){var n=this.getLuminanceSource(),o=n.getWidth();void 0===r||null===r||r.getSize()<o?r=new i.default(o):r.clear(),this.initArrays(o);for(var a=n.getRow(t,this.luminances),u=this.buckets,s=0;s<o;s++)u[(255&a[s])>>e.LUMINANCE_SHIFT]++;var f=e.estimateBlackPoint(u);if(o<3)for(s=0;s<o;s++)(255&a[s])<f&&r.set(s);else{var l=255&a[0],d=255&a[1];for(s=1;s<o-1;s++){var c=255&a[s+1];(4*d-l-c)/2<f&&r.set(s),l=d,d=c}}return r},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),r=t.getWidth(),n=t.getHeight(),o=new a.default(r,n);this.initArrays(r);for(var i=this.buckets,u=1;u<5;u++)for(var s=Math.floor(n*u/5),f=t.getRow(s,this.luminances),l=Math.floor(4*r/5),d=Math.floor(r/5);d<l;d++){i[(255&f[d])>>e.LUMINANCE_SHIFT]++}var c=e.estimateBlackPoint(i),h=t.getMatrix();for(u=0;u<n;u++){var p=u*r;for(d=0;d<r;d++){(255&h[p+d])<c&&o.set(d,u)}}return o},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var r=this.buckets,n=0;n<e.LUMINANCE_BUCKETS;n++)r[n]=0},e.estimateBlackPoint=function(t){for(var r=t.length,n=0,o=0,i=0,a=0;a<r;a++)t[a]>i&&(o=a,i=t[a]),t[a]>n&&(n=t[a]);var s=0,f=0;for(a=0;a<r;a++){var l=a-o;(p=t[a]*l*l)>f&&(s=a,f=p)}if(o>s){var d=o;o=s,s=d}if(s-o<=r/16)throw new u.default;var c=s-1,h=-1;for(a=s-1;a>o;a--){var p,_=a-o;(p=_*_*(s-a)*(n-t[a]))>h&&(c=a,h=p)}return c<<e.LUMINANCE_SHIFT},e.LUMINANCE_BITS=5,e.LUMINANCE_SHIFT=8-e.LUMINANCE_BITS,e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e}(o.default);e.default=s},1153:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t){this.source=t}return t.prototype.getLuminanceSource=function(){return this.source},t.prototype.getWidth=function(){return this.source.getWidth()},t.prototype.getHeight=function(){return this.source.getHeight()},t}();e.default=n},1154:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1052),i=r(1053),a=r(654),u=function(t){function e(r){var n=t.call(this,r.width,r.height)||this;return n.canvas=r,n.tempCanvasElement=null,n.buffer=e.makeBufferFromCanvasImageData(r),n}return n(e,t),e.makeBufferFromCanvasImageData=function(t){var r=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(r.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,e,r){for(var n=new Uint8ClampedArray(e*r),o=0,i=0,a=t.length;o<a;o+=4,i++){var u=void 0;if(0===t[o+3])u=255;else u=306*t[o]+601*t[o+1]+117*t[o+2]+512>>10;n[i]=u}return n},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new a.default("Requested row is outside the image: "+t);var r=this.getWidth(),n=t*r;return null===e?e=this.buffer.slice(n,n+r):(e.length<r&&(e=new Uint8ClampedArray(r)),e.set(this.buffer.slice(n,n+r))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,r,n,o){return t.prototype.crop.call(this,e,r,n,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var r=this.getTempCanvasElement(),n=r.getContext("2d"),o=t*e.DEGREE_TO_RADIANS,i=this.canvas.width,a=this.canvas.height,u=Math.ceil(Math.abs(Math.cos(o))*i+Math.abs(Math.sin(o))*a),s=Math.ceil(Math.abs(Math.sin(o))*i+Math.abs(Math.cos(o))*a);return r.width=u,r.height=s,n.translate(u/2,s/2),n.rotate(o),n.drawImage(this.canvas,i/-2,a/-2),this.buffer=e.makeBufferFromCanvasImageData(r),this},e.prototype.invert=function(){return new o.default(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e}(i.default);e.HTMLCanvasElementLuminanceSource=u},1155:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r){this.deviceId=t,this.label=e,this.kind="videoinput",this.groupId=r||void 0}return t.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},t}();e.VideoInputDevice=n},1156:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(654),o=function(){function t(){}return t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new n.default;return this.logTable[t]},t.addOrSubtract=function(t,e){return t^e},t}();e.default=o},1157:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1158:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.floatToIntBits=function(t){return t},t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}();e.default=n},1159:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1160),i=r(825),a=r(1103),u=r(642),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.sampleGrid=function(t,e,r,n,o,i,u,s,f,l,d,c,h,p,_,g,v,w,y){var E=a.default.quadrilateralToQuadrilateral(n,o,i,u,s,f,l,d,c,h,p,_,g,v,w,y);return this.sampleGridWithTransform(t,e,r,E)},e.prototype.sampleGridWithTransform=function(t,e,r,n){if(e<=0||r<=0)throw new u.default;for(var a=new i.default(e,r),s=new Float32Array(2*e),f=0;f<r;f++){for(var l=s.length,d=f+.5,c=0;c<l;c+=2)s[c]=c/2+.5,s[c+1]=d;n.transformPoints(s),o.default.checkAndNudgePoints(t,s);try{for(c=0;c<l;c+=2)t.get(Math.floor(s[c]),Math.floor(s[c+1]))&&a.set(c/2,f)}catch(h){throw new u.default}}return a},e}(o.default);e.default=s},1160:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(642),o=function(){function t(){}return t.checkAndNudgePoints=function(t,e){for(var r=t.getWidth(),o=t.getHeight(),i=!0,a=0;a<e.length&&i;a+=2){var u=Math.floor(e[a]),s=Math.floor(e[a+1]);if(u<-1||u>r||s<-1||s>o)throw new n.default;i=!1,-1===u?(e[a]=0,i=!0):u===r&&(e[a]=r-1,i=!0),-1===s?(e[a+1]=0,i=!0):s===o&&(e[a+1]=o-1,i=!0)}i=!0;for(a=e.length-2;a>=0&&i;a-=2){u=Math.floor(e[a]),s=Math.floor(e[a+1]);if(u<-1||u>r||s<-1||s>o)throw new n.default;i=!1,-1===u?(e[a]=0,i=!0):u===r&&(e[a]=r-1,i=!0),-1===s?(e[a+1]=0,i=!0):s===o&&(e[a+1]=o-1,i=!0)}},t}();e.default=o},1161:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(824),u=r(692),s=r(642),f=r(900),l=r(791),d=r(736),c=function(t){function e(e,r){void 0===e&&(e=!1),void 0===r&&(r=!1);var n=t.call(this)||this;return n.usingCheckDigit=e,n.extendedMode=r,n.decodeRowResult="",n.counters=new Array(9),n}return n(e,t),e.prototype.decodeRow=function(t,r,n){var u,f,c,h,p=this.counters;p.fill(0),this.decodeRowResult="";var _,g,v=e.findAsteriskPattern(r,p),w=r.getNextSet(v[1]),y=r.getSize();do{e.recordPattern(r,w,p);var E=e.toNarrowWidePattern(p);if(E<0)throw new s.default;_=e.patternToChar(E),this.decodeRowResult+=_,g=w;try{for(var A=o(p),C=A.next();!C.done;C=A.next()){w+=C.value}}catch(P){u={error:P}}finally{try{C&&!C.done&&(f=A.return)&&f.call(A)}finally{if(u)throw u.error}}w=r.getNextSet(w)}while("*"!==_);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var m,I=0;try{for(var S=o(p),O=S.next();!O.done;O=S.next()){I+=O.value}}catch(D){c={error:D}}finally{try{O&&!O.done&&(h=S.return)&&h.call(S)}finally{if(c)throw c.error}}if(w!==y&&2*(w-g-I)<I)throw new s.default;if(this.usingCheckDigit){for(var T=this.decodeRowResult.length-1,R=0,b=0;b<T;b++)R+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(b));if(this.decodeRowResult.charAt(T)!==e.ALPHABET_STRING.charAt(R%43))throw new a.default;this.decodeRowResult=this.decodeRowResult.substring(0,T)}if(0===this.decodeRowResult.length)throw new s.default;m=this.extendedMode?e.decodeExtended(this.decodeRowResult):this.decodeRowResult;var N=(v[1]+v[0])/2,M=g+I/2;return new l.default(m,null,0,[new d.default(N,t),new d.default(M,t)],i.default.CODE_39,(new Date).getTime())},e.findAsteriskPattern=function(t,r){for(var n=t.getSize(),o=t.getNextSet(0),i=0,a=o,u=!1,f=r.length,l=o;l<n;l++)if(t.get(l)!==u)r[i]++;else{if(i===f-1){if(this.toNarrowWidePattern(r)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,a-Math.floor((l-a)/2)),a,!1))return[a,l];a+=r[0]+r[1],r.copyWithin(0,2,2+i-1),r[i-1]=0,r[i]=0,i--}else i++;r[i]=1,u=!u}throw new s.default},e.toNarrowWidePattern=function(t){var e,r,n,i=t.length,a=0;do{var u=2147483647;try{for(var s=o(t),f=s.next();!f.done;f=s.next()){(h=f.value)<u&&h>a&&(u=h)}}catch(p){e={error:p}}finally{try{f&&!f.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}a=u,n=0;for(var l=0,d=0,c=0;c<i;c++){(h=t[c])>a&&(d|=1<<i-1-c,n++,l+=h)}if(3===n){for(c=0;c<i&&n>0;c++){var h;if((h=t[c])>a&&(n--,2*h>=l))return-1}return d}}while(n>3);return-1},e.patternToChar=function(t){for(var r=0;r<e.CHARACTER_ENCODINGS.length;r++)if(e.CHARACTER_ENCODINGS[r]===t)return e.ALPHABET_STRING.charAt(r);if(t===e.ASTERISK_ENCODING)return"*";throw new s.default},e.decodeExtended=function(t){for(var e=t.length,r="",n=0;n<e;n++){var o=t.charAt(n);if("+"===o||"$"===o||"%"===o||"/"===o){var i=t.charAt(n+1),a="\0";switch(o){case"+":if(!(i>="A"&&i<="Z"))throw new u.default;a=String.fromCharCode(i.charCodeAt(0)+32);break;case"$":if(!(i>="A"&&i<="Z"))throw new u.default;a=String.fromCharCode(i.charCodeAt(0)-64);break;case"%":if(i>="A"&&i<="E")a=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")a=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")a=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)a="\0";else if("V"===i)a="@";else if("W"===i)a="`";else{if("X"!==i&&"Y"!==i&&"Z"!==i)throw new u.default;a="\x7f"}break;case"/":if(i>="A"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)-32);else{if("Z"!==i)throw new u.default;a=":"}}r+=a,n++}else r+=o}return r},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e}(f.default);e.default=c},1162:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(764),a=r(791),u=r(736),s=r(900),f=r(642),l=r(692),d=r(824),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.findStartPattern=function(t){for(var r=t.getSize(),n=t.getNextSet(0),o=0,i=[0,0,0,0,0,0],a=n,u=!1,l=n;l<r;l++)if(t.get(l)!==u)i[o]++;else{if(5===o){for(var d=e.MAX_AVG_VARIANCE,c=-1,h=e.CODE_START_A;h<=e.CODE_START_C;h++){var p=s.default.patternMatchVariance(i,e.CODE_PATTERNS[h],e.MAX_INDIVIDUAL_VARIANCE);p<d&&(d=p,c=h)}if(c>=0&&t.isRange(Math.max(0,a-(l-a)/2),a,!1))return[a,l,c];a+=i[0]+i[1],i.splice(0,2),i[o-1]=0,i[o]=0,o--}else o++;i[o]=1,u=!u}throw new f.default},e.decodeCode=function(t,r,n){s.default.recordPattern(t,n,r);for(var o=e.MAX_AVG_VARIANCE,i=-1,a=0;a<e.CODE_PATTERNS.length;a++){var u=e.CODE_PATTERNS[a],l=this.patternMatchVariance(r,u,e.MAX_INDIVIDUAL_VARIANCE);l<o&&(o=l,i=a)}if(i>=0)return i;throw new f.default},e.prototype.decodeRow=function(t,r,n){var s,c=n&&!0===n.get(i.default.ASSUME_GS1),h=e.findStartPattern(r),p=h[2],_=0,g=new Uint8Array(20);switch(g[_++]=p,p){case e.CODE_START_A:s=e.CODE_CODE_A;break;case e.CODE_START_B:s=e.CODE_CODE_B;break;case e.CODE_START_C:s=e.CODE_CODE_C;break;default:throw new l.default}for(var v=!1,w=!1,y="",E=h[0],A=h[1],C=[0,0,0,0,0,0],m=0,I=0,S=p,O=0,T=!0,R=!1,b=!1;!v;){var N=w;switch(w=!1,m=I,I=e.decodeCode(r,C,A),g[_++]=I,I!==e.CODE_STOP&&(T=!0),I!==e.CODE_STOP&&(S+=++O*I),E=A,A+=C.reduce((function(t,e){return t+e}),0),I){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new l.default}switch(s){case e.CODE_CODE_A:if(I<64)y+=b===R?String.fromCharCode(" ".charCodeAt(0)+I):String.fromCharCode(" ".charCodeAt(0)+I+128),b=!1;else if(I<96)y+=b===R?String.fromCharCode(I-64):String.fromCharCode(I+64),b=!1;else switch(I!==e.CODE_STOP&&(T=!1),I){case e.CODE_FNC_1:c&&(0===y.length?y+="]C1":y+=String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!R&&b?(R=!0,b=!1):R&&b?(R=!1,b=!1):b=!0;break;case e.CODE_SHIFT:w=!0,s=e.CODE_CODE_B;break;case e.CODE_CODE_B:s=e.CODE_CODE_B;break;case e.CODE_CODE_C:s=e.CODE_CODE_C;break;case e.CODE_STOP:v=!0}break;case e.CODE_CODE_B:if(I<96)y+=b===R?String.fromCharCode(" ".charCodeAt(0)+I):String.fromCharCode(" ".charCodeAt(0)+I+128),b=!1;else switch(I!==e.CODE_STOP&&(T=!1),I){case e.CODE_FNC_1:c&&(0===y.length?y+="]C1":y+=String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!R&&b?(R=!0,b=!1):R&&b?(R=!1,b=!1):b=!0;break;case e.CODE_SHIFT:w=!0,s=e.CODE_CODE_A;break;case e.CODE_CODE_A:s=e.CODE_CODE_A;break;case e.CODE_CODE_C:s=e.CODE_CODE_C;break;case e.CODE_STOP:v=!0}break;case e.CODE_CODE_C:if(I<100)I<10&&(y+="0"),y+=I;else switch(I!==e.CODE_STOP&&(T=!1),I){case e.CODE_FNC_1:c&&(0===y.length?y+="]C1":y+=String.fromCharCode(29));break;case e.CODE_CODE_A:s=e.CODE_CODE_A;break;case e.CODE_CODE_B:s=e.CODE_CODE_B;break;case e.CODE_STOP:v=!0}}N&&(s=s===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var M=A-E;if(A=r.getNextUnset(A),!r.isRange(A,Math.min(r.getSize(),A+(A-E)/2),!1))throw new f.default;if((S-=O*m)%103!==m)throw new d.default;var P=y.length;if(0===P)throw new f.default;P>0&&T&&(y=s===e.CODE_CODE_C?y.substring(0,P-2):y.substring(0,P-1));for(var D=(h[1]+h[0])/2,B=E+M/2,L=g.length,F=new Uint8Array(L),x=0;x<L;x++)F[x]=g[x];var k=[new u.default(D,t),new u.default(B,t)];return new a.default(y,F,0,k,o.default.CODE_128,(new Date).getTime())},e.CODE_PATTERNS=[[2,1,2,2,2,2],[2,2,2,1,2,2],[2,2,2,2,2,1],[1,2,1,2,2,3],[1,2,1,3,2,2],[1,3,1,2,2,2],[1,2,2,2,1,3],[1,2,2,3,1,2],[1,3,2,2,1,2],[2,2,1,2,1,3],[2,2,1,3,1,2],[2,3,1,2,1,2],[1,1,2,2,3,2],[1,2,2,1,3,2],[1,2,2,2,3,1],[1,1,3,2,2,2],[1,2,3,1,2,2],[1,2,3,2,2,1],[2,2,3,2,1,1],[2,2,1,1,3,2],[2,2,1,2,3,1],[2,1,3,2,1,2],[2,2,3,1,1,2],[3,1,2,1,3,1],[3,1,1,2,2,2],[3,2,1,1,2,2],[3,2,1,2,2,1],[3,1,2,2,1,2],[3,2,2,1,1,2],[3,2,2,2,1,1],[2,1,2,1,2,3],[2,1,2,3,2,1],[2,3,2,1,2,1],[1,1,1,3,2,3],[1,3,1,1,2,3],[1,3,1,3,2,1],[1,1,2,3,1,3],[1,3,2,1,1,3],[1,3,2,3,1,1],[2,1,1,3,1,3],[2,3,1,1,1,3],[2,3,1,3,1,1],[1,1,2,1,3,3],[1,1,2,3,3,1],[1,3,2,1,3,1],[1,1,3,1,2,3],[1,1,3,3,2,1],[1,3,3,1,2,1],[3,1,3,1,2,1],[2,1,1,3,3,1],[2,3,1,1,3,1],[2,1,3,1,1,3],[2,1,3,3,1,1],[2,1,3,1,3,1],[3,1,1,1,2,3],[3,1,1,3,2,1],[3,3,1,1,2,1],[3,1,2,1,1,3],[3,1,2,3,1,1],[3,3,2,1,1,1],[3,1,4,1,1,1],[2,2,1,4,1,1],[4,3,1,1,1,1],[1,1,1,2,2,4],[1,1,1,4,2,2],[1,2,1,1,2,4],[1,2,1,4,2,1],[1,4,1,1,2,2],[1,4,1,2,2,1],[1,1,2,2,1,4],[1,1,2,4,1,2],[1,2,2,1,1,4],[1,2,2,4,1,1],[1,4,2,1,1,2],[1,4,2,2,1,1],[2,4,1,2,1,1],[2,2,1,1,1,4],[4,1,3,1,1,1],[2,4,1,1,1,2],[1,3,4,1,1,1],[1,1,1,2,4,2],[1,2,1,1,4,2],[1,2,1,2,4,1],[1,1,4,2,1,2],[1,2,4,1,1,2],[1,2,4,2,1,1],[4,1,1,2,1,2],[4,2,1,1,1,2],[4,2,1,2,1,1],[2,1,2,1,4,1],[2,1,4,1,2,1],[4,1,2,1,2,1],[1,1,1,1,4,3],[1,1,1,3,4,1],[1,3,1,1,4,1],[1,1,4,1,1,3],[1,1,4,3,1,1],[4,1,1,1,1,3],[4,1,1,3,1,1],[1,1,3,1,4,1],[1,1,4,1,3,1],[3,1,1,1,4,1],[4,1,1,1,3,1],[2,1,1,4,1,2],[2,1,1,2,1,4],[2,1,1,2,3,2],[2,3,3,1,1,1,2]],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e}(s.default);e.default=c},1163:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(1164),a=r(1270),u=r(791),s=r(764),f=r(642),l=r(749),d=r(693),c=r(736),h=r(1165),p=r(1105),_=r(820),g=r(1166),v=r(752),w=r(900),y=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.possibleLeftPairs=[],e.possibleRightPairs=[],e}return n(e,t),e.prototype.decodeRow=function(t,r,n){var i,a,u,s,l=this.decodePair(r,!1,t,n);e.addOrTally(this.possibleLeftPairs,l),r.reverse();var d=this.decodePair(r,!0,t,n);e.addOrTally(this.possibleRightPairs,d),r.reverse();try{for(var c=o(this.possibleLeftPairs),h=c.next();!h.done;h=c.next()){var p=h.value;if(p.getCount()>1)try{for(var _=o(this.possibleRightPairs),g=_.next();!g.done;g=_.next()){var v=g.value;if(v.getCount()>1&&e.checkChecksum(p,v))return e.constructResult(p,v)}}catch(w){u={error:w}}finally{try{g&&!g.done&&(s=_.return)&&s.call(_)}finally{if(u)throw u.error}}}}catch(y){i={error:y}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}throw new f.default},e.addOrTally=function(t,e){var r,n;if(null!=e){var i=!1;try{for(var a=o(t),u=a.next();!u.done;u=a.next()){var s=u.value;if(s.getValue()===e.getValue()){s.incrementCount(),i=!0;break}}}catch(f){r={error:f}}finally{try{u&&!u.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}i||t.push(e)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,e){for(var r=4537077*t.getValue()+e.getValue(),n=new String(r).toString(),o=new l.default,i=13-n.length;i>0;i--)o.append("0");o.append(n);var a=0;for(i=0;i<13;i++){var s=o.charAt(i).charCodeAt(0)-"0".charCodeAt(0);a+=0===(1&i)?3*s:s}10===(a=10-a%10)&&(a=0),o.append(a.toString());var f=t.getFinderPattern().getResultPoints(),c=e.getFinderPattern().getResultPoints();return new u.default(o.toString(),null,0,[f[0],f[1],c[0],c[1]],d.default.RSS_14,(new Date).getTime())},e.checkChecksum=function(t,e){var r=(t.getChecksumPortion()+16*e.getChecksumPortion())%79,n=9*t.getFinderPattern().getValue()+e.getFinderPattern().getValue();return n>72&&n--,n>8&&n--,r===n},e.prototype.decodePair=function(t,e,r,n){try{var o=this.findFinderPattern(t,e),i=this.parseFoundFinderPattern(t,r,e,o),u=null==n?null:n.get(s.default.NEED_RESULT_POINT_CALLBACK);if(null!=u){var f=(o[0]+o[1])/2;e&&(f=t.getSize()-1-f),u.foundPossibleResultPoint(new c.default(f,r))}var l=this.decodeDataCharacter(t,i,!0),d=this.decodeDataCharacter(t,i,!1);return new a.default(1597*l.getValue()+d.getValue(),l.getChecksumPortion()+4*d.getChecksumPortion(),i)}catch(h){return null}},e.prototype.decodeDataCharacter=function(t,r,n){for(var o=this.getDataCharacterCounters(),i=0;i<o.length;i++)o[i]=0;if(n)w.default.recordPatternInReverse(t,r.getStartEnd()[0],o);else{w.default.recordPattern(t,r.getStartEnd()[1]+1,o);for(var a=0,u=o.length-1;a<u;a++,u--){var s=o[a];o[a]=o[u],o[u]=s}}var l=n?16:15,d=_.default.sum(new Int32Array(o))/l,c=this.getOddCounts(),h=this.getEvenCounts(),v=this.getOddRoundingErrors(),y=this.getEvenRoundingErrors();for(a=0;a<o.length;a++){var E=o[a]/d,A=Math.floor(E+.5);A<1?A=1:A>8&&(A=8);var C=Math.floor(a/2);0===(1&a)?(c[C]=A,v[C]=E-A):(h[C]=A,y[C]=E-A)}this.adjustOddEvenCounts(n,l);var m=0,I=0;for(a=c.length-1;a>=0;a--)I*=9,I+=c[a],m+=c[a];var S=0,O=0;for(a=h.length-1;a>=0;a--)S*=9,S+=h[a],O+=h[a];var T=I+3*S;if(n){if(0!==(1&m)||m>12||m<4)throw new f.default;var R=(12-m)/2,b=9-(B=e.OUTSIDE_ODD_WIDEST[R]),N=g.default.getRSSvalue(c,B,!1),M=g.default.getRSSvalue(h,b,!0),P=e.OUTSIDE_EVEN_TOTAL_SUBSET[R],D=e.OUTSIDE_GSUM[R];return new p.default(N*P+M+D,T)}if(0!==(1&O)||O>10||O<4)throw new f.default;R=(10-O)/2,b=9-(B=e.INSIDE_ODD_WIDEST[R]),N=g.default.getRSSvalue(c,B,!0),M=g.default.getRSSvalue(h,b,!1);var B,L=e.INSIDE_ODD_TOTAL_SUBSET[R];D=e.INSIDE_GSUM[R];return new p.default(M*L+N+D,T)},e.prototype.findFinderPattern=function(t,e){var r=this.getDecodeFinderCounters();r[0]=0,r[1]=0,r[2]=0,r[3]=0;for(var n=t.getSize(),o=!1,a=0;a<n&&e!==(o=!t.get(a));)a++;for(var u=0,s=a,l=a;l<n;l++)if(t.get(l)!==o)r[u]++;else{if(3===u){if(i.default.isFinderPattern(r))return[s,l];s+=r[0]+r[1],r[0]=r[2],r[1]=r[3],r[2]=0,r[3]=0,u--}else u++;r[u]=1,o=!o}throw new f.default},e.prototype.parseFoundFinderPattern=function(t,r,n,o){for(var i=t.get(o[0]),a=o[0]-1;a>=0&&i!==t.get(a);)a--;a++;var u=o[0]-a,s=this.getDecodeFinderCounters(),f=new Array(s.length);v.default.arraycopy(s,0,f,1,s.length-1),f[0]=u;var l=this.parseFinderValue(f,e.FINDER_PATTERNS),d=a,c=o[1];return n&&(d=t.getSize()-1-d,c=t.getSize()-1-c),new h.default(l,[a,o[1]],d,c,r)},e.prototype.adjustOddEvenCounts=function(t,e){var r=_.default.sum(new Int32Array(this.getOddCounts())),n=_.default.sum(new Int32Array(this.getEvenCounts())),o=!1,a=!1,u=!1,s=!1;t?(r>12?a=!0:r<4&&(o=!0),n>12?s=!0:n<4&&(u=!0)):(r>11?a=!0:r<5&&(o=!0),n>10?s=!0:n<4&&(u=!0));var l=r+n-e,d=(1&r)===(t?1:0),c=1===(1&n);if(1===l)if(d){if(c)throw new f.default;a=!0}else{if(!c)throw new f.default;s=!0}else if(-1===l)if(d){if(c)throw new f.default;o=!0}else{if(!c)throw new f.default;u=!0}else{if(0!==l)throw new f.default;if(d){if(!c)throw new f.default;r<n?(o=!0,s=!0):(a=!0,u=!0)}else if(c)throw new f.default}if(o){if(a)throw new f.default;i.default.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(a&&i.default.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(s)throw new f.default;i.default.increment(this.getEvenCounts(),this.getOddRoundingErrors())}s&&i.default.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[[3,8,2,1],[3,5,5,1],[3,3,7,1],[3,1,9,1],[2,7,4,1],[2,5,6,1],[2,3,8,1],[1,5,7,1],[1,3,9,1]],e}(i.default);e.default=y},1164:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(900),a=r(642),u=r(820),s=function(t){function e(){var e=t.call(this)||this;return e.decodeFinderCounters=new Array(4),e.dataCharacterCounters=new Array(8),e.oddRoundingErrors=new Array(4),e.evenRoundingErrors=new Array(4),e.oddCounts=new Array(e.dataCharacterCounters.length/2),e.evenCounts=new Array(e.dataCharacterCounters.length/2),e}return n(e,t),e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,r){for(var n=0;n<r.length;n++)if(i.default.patternMatchVariance(t,r[n],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return n;throw new a.default},e.count=function(t){return u.default.sum(new Int32Array(t))},e.increment=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]>n&&(n=e[o],r=o);t[r]++},e.decrement=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]<n&&(n=e[o],r=o);t[r]--},e.isFinderPattern=function(t){var r,n,i=t[0]+t[1],a=i/(i+t[2]+t[3]);if(a>=e.MIN_FINDER_PATTERN_RATIO&&a<=e.MAX_FINDER_PATTERN_RATIO){var u=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;try{for(var f=o(t),l=f.next();!l.done;l=f.next()){var d=l.value;d>s&&(s=d),d<u&&(u=d)}}catch(c){r={error:c}}finally{try{l&&!l.done&&(n=f.return)&&n.call(f)}finally{if(r)throw r.error}}return s<10*u}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e}(i.default);e.default=s},1165:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(736),o=function(){function t(t,e,r,o,i){this.value=t,this.startEnd=e,this.value=t,this.startEnd=e,this.resultPoints=new Array,this.resultPoints.push(new n.default(r,i)),this.resultPoints.push(new n.default(o,i))}return t.prototype.getValue=function(){return this.value},t.prototype.getStartEnd=function(){return this.startEnd},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.prototype.hashCode=function(){return this.value},t}();e.default=o},1166:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=function(){function t(){}return t.prototype.RSSUtils=function(){},t.getRSSvalue=function(e,r,o){var i,a,u=0;try{for(var s=n(e),f=s.next();!f.done;f=s.next()){u+=f.value}}catch(w){i={error:w}}finally{try{f&&!f.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}for(var l=0,d=0,c=e.length,h=0;h<c-1;h++){var p=void 0;for(p=1,d|=1<<h;p<e[h];p++,d&=~(1<<h)){var _=t.combins(u-p-1,c-h-2);if(o&&0===d&&u-p-(c-h-1)>=c-h-1&&(_-=t.combins(u-p-(c-h),c-h-2)),c-h-1>1){for(var g=0,v=u-p-(c-h-2);v>r;v--)g+=t.combins(u-p-v-1,c-h-3);_-=g*(c-1-h)}else u-p>r&&_--;l+=_}u-=p}return l},t.combins=function(t,e){var r,n;t-e>e?(n=e,r=t-e):(n=t-e,r=e);for(var o=1,i=1,a=t;a>r;a--)o*=a,i<=n&&(o/=i,i++);for(;i<=n;)o/=i,i++;return o},t}();e.default=o},1167:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(764),u=r(791),s=r(736),f=r(900),l=r(749),d=r(752),c=r(692),h=r(642),p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.narrowLineWidth=-1,e}return n(e,t),e.prototype.decodeRow=function(t,r,n){var f,d,h=this.decodeStart(r),p=this.decodeEnd(r),_=new l.default;e.decodeMiddle(r,h[1],p[0],_);var g=_.toString(),v=null;null!=n&&(v=n.get(a.default.ALLOWED_LENGTHS)),null==v&&(v=e.DEFAULT_ALLOWED_LENGTHS);var w=g.length,y=!1,E=0;try{for(var A=o(v),C=A.next();!C.done;C=A.next()){var m=C.value;if(w===m){y=!0;break}m>E&&(E=m)}}catch(S){f={error:S}}finally{try{C&&!C.done&&(d=A.return)&&d.call(A)}finally{if(f)throw f.error}}if(!y&&w>E&&(y=!0),!y)throw new c.default;var I=[new s.default(h[1],t),new s.default(p[0],t)];return new u.default(g,null,0,I,i.default.ITF,(new Date).getTime())},e.decodeMiddle=function(t,r,n,o){var i=new Array(10),a=new Array(5),u=new Array(5);for(i.fill(0),a.fill(0),u.fill(0);r<n;){f.default.recordPattern(t,r,i);for(var s=0;s<5;s++){var l=2*s;a[s]=i[l],u[s]=i[l+1]}var d=e.decodeDigit(a);o.append(d.toString()),d=this.decodeDigit(u),o.append(d.toString()),i.forEach((function(t){r+=t}))}},e.prototype.decodeStart=function(t){var r=e.skipWhiteSpace(t),n=e.findGuardPattern(t,r,e.START_PATTERN);return this.narrowLineWidth=(n[1]-n[0])/4,this.validateQuietZone(t,n[0]),n},e.prototype.validateQuietZone=function(t,e){var r=10*this.narrowLineWidth;r=r<e?r:e;for(var n=e-1;r>0&&n>=0&&!t.get(n);n--)r--;if(0!==r)throw new h.default},e.skipWhiteSpace=function(t){var e=t.getSize(),r=t.getNextSet(0);if(r===e)throw new h.default;return r},e.prototype.decodeEnd=function(t){t.reverse();try{var r=e.skipWhiteSpace(t),n=void 0;try{n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[0])}catch(i){n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[1])}this.validateQuietZone(t,n[0]);var o=n[0];return n[0]=t.getSize()-n[1],n[1]=t.getSize()-o,n}finally{t.reverse()}},e.findGuardPattern=function(t,r,n){var o=n.length,i=new Array(o),a=t.getSize(),u=!1,s=0,l=r;i.fill(0);for(var c=r;c<a;c++)if(t.get(c)!==u)i[s]++;else{if(s===o-1){if(f.default.patternMatchVariance(i,n,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[l,c];l+=i[0]+i[1],d.default.arraycopy(i,2,i,0,s-1),i[s-1]=0,i[s]=0,s--}else s++;i[s]=1,u=!u}throw new h.default},e.decodeDigit=function(t){for(var r=e.MAX_AVG_VARIANCE,n=-1,o=e.PATTERNS.length,i=0;i<o;i++){var a=e.PATTERNS[i],u=f.default.patternMatchVariance(t,a,e.MAX_INDIVIDUAL_VARIANCE);u<r?(r=u,n=i):u===r&&(n=-1)}if(n>=0)return n%10;throw new h.default},e.W=3,e.w=2,e.N=1,e.PATTERNS=[[1,1,2,2,1],[2,1,1,1,2],[1,2,1,1,2],[2,2,1,1,1],[1,1,2,1,2],[2,1,2,1,1],[1,2,2,1,1],[1,1,1,2,2],[2,1,1,2,1],[1,2,1,2,1],[1,1,3,3,1],[3,1,1,1,3],[1,3,1,1,3],[3,3,1,1,1],[1,1,3,1,3],[3,1,3,1,1],[1,3,3,1,1],[1,1,1,3,3],[3,1,1,3,1],[1,3,1,3,1]],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=[1,1,1,1],e.END_PATTERN_REVERSED=[[1,1,2],[1,1,3]],e}(f.default);e.default=p},1168:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(1169),u=r(642),s=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=[0,0,0,0],e}return n(e,t),e.prototype.decodeMiddle=function(t,r,n){var i,u,s,f,l=this.decodeMiddleCounters;l[0]=0,l[1]=0,l[2]=0,l[3]=0;for(var d=t.getSize(),c=r[1],h=0,p=0;p<6&&c<d;p++){var _=a.default.decodeDigit(t,l,c,a.default.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+_%10);try{for(var g=o(l),v=g.next();!v.done;v=g.next()){c+=v.value}}catch(E){i={error:E}}finally{try{v&&!v.done&&(u=g.return)&&u.call(g)}finally{if(i)throw i.error}}_>=10&&(h|=1<<5-p)}n=e.determineFirstDigit(n,h),c=a.default.findGuardPattern(t,c,!0,a.default.MIDDLE_PATTERN,new Array(a.default.MIDDLE_PATTERN.length).fill(0))[1];for(p=0;p<6&&c<d;p++){_=a.default.decodeDigit(t,l,c,a.default.L_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+_);try{for(var w=o(l),y=w.next();!y.done;y=w.next()){c+=y.value}}catch(A){s={error:A}}finally{try{y&&!y.done&&(f=w.return)&&f.call(w)}finally{if(s)throw s.error}}}return{rowOffset:c,resultString:n}},e.prototype.getBarcodeFormat=function(){return i.default.EAN_13},e.determineFirstDigit=function(t,e){for(var r=0;r<10;r++)if(e===this.FIRST_DIGIT_ENCODINGS[r])return t=String.fromCharCode("0".charCodeAt(0)+r)+t;throw new u.default},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e}(a.default);e.default=s},1169:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(764),a=r(791),u=r(899),s=r(736),f=r(1272),l=r(1073),d=r(642),c=r(692),h=r(824),p=function(t){function e(){var r=t.call(this)||this;r.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map((function(t){return t.slice()}));for(var n=10;n<20;n++){for(var o=e.L_PATTERNS[n-10],i=new Array(o.length),a=0;a<o.length;a++)i[a]=o[o.length-a-1];e.L_AND_G_PATTERNS[n]=i}return r}return n(e,t),e.prototype.decodeRow=function(t,r,n){var l=e.findStartGuardPattern(r),p=null==n?null:n.get(i.default.NEED_RESULT_POINT_CALLBACK);if(null!=p){var _=new s.default((l[0]+l[1])/2,t);p.foundPossibleResultPoint(_)}var g=this.decodeMiddle(r,l,this.decodeRowStringBuffer),v=g.rowOffset,w=g.resultString;if(null!=p){var y=new s.default(v,t);p.foundPossibleResultPoint(y)}var E=e.decodeEnd(r,v);if(null!=p){var A=new s.default((E[0]+E[1])/2,t);p.foundPossibleResultPoint(A)}var C=E[1],m=C+(C-E[0]);if(m>=r.getSize()||!r.isRange(C,m,!1))throw new d.default;var I=w.toString();if(I.length<8)throw new c.default;if(!e.checkChecksum(I))throw new h.default;var S=(l[1]+l[0])/2,O=(E[1]+E[0])/2,T=this.getBarcodeFormat(),R=[new s.default(S,t),new s.default(O,t)],b=new a.default(I,null,0,R,T,(new Date).getTime()),N=0;try{var M=f.default.decodeRow(t,r,E[1]);b.putMetadata(u.default.UPC_EAN_EXTENSION,M.getText()),b.putAllMetadata(M.getResultMetadata()),b.addResultPoints(M.getResultPoints()),N=M.getText().length}catch(L){}var P=null==n?null:n.get(i.default.ALLOWED_EAN_EXTENSIONS);if(null!=P){var D=!1;for(var B in P)if(N.toString()===B){D=!0;break}if(!D)throw new d.default}return T===o.default.EAN_13||o.default.UPC_A,b},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new c.default;r+=o}r*=3;for(n=e-2;n>=0;n-=2){var o;if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new c.default;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Array(e.START_END_PATTERN.length).fill(0))},e}(l.default);e.default=p},1170:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(764),i=r(693),a=r(1108),u=r(1100),s=r(1104),f=r(1106),l=r(642),d=r(1174),c=r(1179),h=function(){function t(){}return t.prototype.decode=function(t,e){return this.setHints(e),this.decodeInternal(t)},t.prototype.decodeWithState=function(t){return null!==this.readers&&void 0!==this.readers||this.setHints(null),this.decodeInternal(t)},t.prototype.setHints=function(t){this.hints=t;var e=null!==t&&void 0!==t&&void 0!==t.get(o.default.TRY_HARDER),r=null===t||void 0===t?null:t.get(o.default.POSSIBLE_FORMATS),n=new Array;if(null!==r&&void 0!==r){var l=r.some((function(t){return t===i.default.UPC_A||t===i.default.UPC_E||t===i.default.EAN_13||t===i.default.EAN_8||t===i.default.CODABAR||t===i.default.CODE_39||t===i.default.CODE_93||t===i.default.CODE_128||t===i.default.ITF||t===i.default.RSS_14||t===i.default.RSS_EXPANDED}));l&&!e&&n.push(new s.default(t)),r.includes(i.default.QR_CODE)&&n.push(new a.default),r.includes(i.default.DATA_MATRIX)&&n.push(new f.default),r.includes(i.default.AZTEC)&&n.push(new u.default),r.includes(i.default.PDF_417)&&n.push(new d.default),l&&e&&n.push(new s.default(t))}0===n.length&&(e||n.push(new s.default(t)),n.push(new a.default),n.push(new f.default),n.push(new u.default),n.push(new d.default),e&&n.push(new s.default(t))),this.readers=n},t.prototype.reset=function(){var t,e;if(null!==this.readers)try{for(var r=n(this.readers),o=r.next();!o.done;o=r.next()){o.value.reset()}}catch(i){t={error:i}}finally{try{o&&!o.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},t.prototype.decodeInternal=function(t){var e,r;if(null===this.readers)throw new c.default("No readers where selected, nothing can be read.");try{for(var o=n(this.readers),i=o.next();!i.done;i=o.next()){var a=i.value;try{return a.decode(t,this.hints)}catch(u){if(u instanceof c.default)continue}}}catch(s){e={error:s}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}throw new l.default("No MultiFormat Readers were able to detect the code.")},t}();e.default=h},1171:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1074),i=r(898),a=function(){function t(t){this.errorCorrectionLevel=o.default.forBits(t>>3&3),this.dataMask=7&t}return t.numBitsDiffering=function(t,e){return i.default.bitCount(t^e)},t.decodeFormatInformation=function(e,r){var n=t.doDecodeFormatInformation(e,r);return null!==n?n:t.doDecodeFormatInformation(e^t.FORMAT_INFO_MASK_QR,r^t.FORMAT_INFO_MASK_QR)},t.doDecodeFormatInformation=function(e,r){var o,i,a=Number.MAX_SAFE_INTEGER,u=0;try{for(var s=n(t.FORMAT_INFO_DECODE_LOOKUP),f=s.next();!f.done;f=s.next()){var l=f.value,d=l[0];if(d===e||d===r)return new t(l[1]);var c=t.numBitsDiffering(e,d);c<a&&(u=l[1],a=c),e!==r&&(c=t.numBitsDiffering(r,d))<a&&(u=l[1],a=c)}}catch(h){o={error:h}}finally{try{f&&!f.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}return a<=3?new t(u):null},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getDataMask=function(){return this.dataMask},t.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.errorCorrectionLevel===r.errorCorrectionLevel&&this.dataMask===r.dataMask},t.FORMAT_INFO_MASK_QR=21522,t.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],t}();e.default=a},1172:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t){this.mirrored=t}return t.prototype.isMirrored=function(){return this.mirrored},t.prototype.applyMirroredCorrection=function(t){if(this.mirrored&&null!==t&&!(t.length<3)){var e=t[0];t[0]=t[2],t[2]=e}},t}();e.default=n},1173:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=r(654);!function(t){t[t.TERMINATOR=0]="TERMINATOR",t[t.NUMERIC=1]="NUMERIC",t[t.ALPHANUMERIC=2]="ALPHANUMERIC",t[t.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",t[t.BYTE=4]="BYTE",t[t.ECI=5]="ECI",t[t.KANJI=6]="KANJI",t[t.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",t[t.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",t[t.HANZI=9]="HANZI"}(n=e.ModeValues||(e.ModeValues={}));var i=function(){function t(e,r,n,o){this.value=e,this.stringValue=r,this.characterCountBitsForVersions=n,this.bits=o,t.FOR_BITS.set(o,this),t.FOR_VALUE.set(e,this)}return t.forBits=function(e){var r=t.FOR_BITS.get(e);if(void 0===r)throw new o.default;return r},t.prototype.getCharacterCountBits=function(t){var e,r=t.getVersionNumber();return e=r<=9?0:r<=26?1:2,this.characterCountBitsForVersions[e]},t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.prototype.toString=function(){return this.stringValue},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.TERMINATOR=new t(n.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),t.NUMERIC=new t(n.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),t.ALPHANUMERIC=new t(n.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),t.STRUCTURED_APPEND=new t(n.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),t.BYTE=new t(n.BYTE,"BYTE",Int32Array.from([8,16,16]),4),t.ECI=new t(n.ECI,"ECI",Int32Array.from([0,0,0]),7),t.KANJI=new t(n.KANJI,"KANJI",Int32Array.from([8,10,12]),8),t.FNC1_FIRST_POSITION=new t(n.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),t.FNC1_SECOND_POSITION=new t(n.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),t.HANZI=new t(n.HANZI,"HANZI",Int32Array.from([8,10,12]),13),t}();e.default=i},1174:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(824),a=r(692),u=r(642),s=r(791),f=r(1024),l=r(898),d=r(899),c=r(1297),h=r(1299),p=function(){function t(){}return t.prototype.decode=function(e,r){void 0===r&&(r=null);var n=t.decode(e,r,!1);if(null==n||0===n.length||null==n[0])throw u.default.getNotFoundInstance();return n[0]},t.prototype.decodeMultiple=function(e,r){void 0===r&&(r=null);try{return t.decode(e,r,!0)}catch(n){if(n instanceof a.default||n instanceof i.default)throw u.default.getNotFoundInstance();throw n}},t.decode=function(e,r,i){var a,u,f=new Array,l=c.default.detectMultiple(e,r,i);try{for(var p=n(l.getPoints()),_=p.next();!_.done;_=p.next()){var g=_.value,v=h.default.decode(l.getBits(),g[4],g[5],g[6],g[7],t.getMinCodewordWidth(g),t.getMaxCodewordWidth(g)),w=new s.default(v.getText(),v.getRawBytes(),void 0,g,o.default.PDF_417);w.putMetadata(d.default.ERROR_CORRECTION_LEVEL,v.getECLevel());var y=v.getOther();null!=y&&w.putMetadata(d.default.PDF417_EXTRA_METADATA,y),f.push(w)}}catch(E){a={error:E}}finally{try{_&&!_.done&&(u=p.return)&&u.call(p)}finally{if(a)throw a.error}}return f.map((function(t){return t}))},t.getMaxWidth=function(t,e){return null==t||null==e?0:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMinWidth=function(t,e){return null==t||null==e?l.default.MAX_VALUE:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(t.getMaxWidth(e[0],e[4]),t.getMaxWidth(e[6],e[2])*f.default.MODULES_IN_CODEWORD/f.default.MODULES_IN_STOP_PATTERN),Math.max(t.getMaxWidth(e[1],e[5]),t.getMaxWidth(e[7],e[3])*f.default.MODULES_IN_CODEWORD/f.default.MODULES_IN_STOP_PATTERN)))},t.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(t.getMinWidth(e[0],e[4]),t.getMinWidth(e[6],e[2])*f.default.MODULES_IN_CODEWORD/f.default.MODULES_IN_STOP_PATTERN),Math.min(t.getMinWidth(e[1],e[5]),t.getMinWidth(e[7],e[3])*f.default.MODULES_IN_CODEWORD/f.default.MODULES_IN_STOP_PATTERN)))},t.prototype.reset=function(){},t}();e.default=p},1175:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(654),i=r(752),a=r(749),u=function(){function t(t,e){if(0===e.length)throw new o.default;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(r-n),i.default.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){var e,r;if(0===t)return this.getCoefficient(0);if(1===t){var o=0;try{for(var i=n(this.coefficients),a=i.next();!a.done;a=i.next()){var u=a.value;o=this.field.add(o,u)}}catch(d){e={error:d}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return o}for(var s=this.coefficients[0],f=this.coefficients.length,l=1;l<f;l++)s=this.field.add(this.field.multiply(t,s),this.coefficients[l]);return s},t.prototype.add=function(e){if(!this.field.equals(e.field))throw new o.default("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,n=e.coefficients;if(r.length>n.length){var a=r;r=n,n=a}var u=new Int32Array(n.length),s=n.length-r.length;i.default.arraycopy(n,0,u,0,s);for(var f=s;f<n.length;f++)u[f]=this.field.add(r[f-s],n[f]);return new t(this.field,u)},t.prototype.subtract=function(t){if(!this.field.equals(t.field))throw new o.default("ModulusPolys do not have same ModulusGF field");return t.isZero()?this:this.add(t.negative())},t.prototype.multiply=function(e){return e instanceof t?this.multiplyOther(e):this.multiplyScalar(e)},t.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new o.default("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new t(this.field,new Int32Array([0]));for(var r=this.coefficients,n=r.length,i=e.coefficients,a=i.length,u=new Int32Array(n+a-1),s=0;s<n;s++)for(var f=r[s],l=0;l<a;l++)u[s+l]=this.field.add(u[s+l],this.field.multiply(f,i[l]));return new t(this.field,u)},t.prototype.negative=function(){for(var e=this.coefficients.length,r=new Int32Array(e),n=0;n<e;n++)r[n]=this.field.subtract(0,this.coefficients[n]);return new t(this.field,r)},t.prototype.multiplyScalar=function(e){if(0===e)return new t(this.field,new Int32Array([0]));if(1===e)return this;for(var r=this.coefficients.length,n=new Int32Array(r),o=0;o<r;o++)n[o]=this.field.multiply(this.coefficients[o],e);return new t(this.field,n)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new o.default;if(0===r)return new t(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),a=0;a<n;a++)i[a]=this.field.multiply(this.coefficients[a],r);return new t(this.field,i)},t.prototype.toString=function(){for(var t=new a.default,e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);0!==r&&(r<0?(t.append(" - "),r=-r):t.length()>0&&t.append(" + "),0!==e&&1===r||t.append(r),0!==e&&(1===e?t.append("x"):(t.append("x^"),t.append(e))))}return t.toString()},t}();e.default=u},1176:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(642),o=r(736),i=function(){function t(e,r,n,o,i){e instanceof t?this.constructor_2(e):this.constructor_1(e,r,n,o,i)}return t.prototype.constructor_1=function(t,e,r,i,a){var u=null==e||null==r,s=null==i||null==a;if(u&&s)throw new n.default;u?(e=new o.default(0,i.getY()),r=new o.default(0,a.getY())):s&&(i=new o.default(t.getWidth()-1,e.getY()),a=new o.default(t.getWidth()-1,r.getY())),this.image=t,this.topLeft=e,this.bottomLeft=r,this.topRight=i,this.bottomRight=a,this.minX=Math.trunc(Math.min(e.getX(),r.getX())),this.maxX=Math.trunc(Math.max(i.getX(),a.getX())),this.minY=Math.trunc(Math.min(e.getY(),i.getY())),this.maxY=Math.trunc(Math.max(r.getY(),a.getY()))},t.prototype.constructor_2=function(t){this.image=t.image,this.topLeft=t.getTopLeft(),this.bottomLeft=t.getBottomLeft(),this.topRight=t.getTopRight(),this.bottomRight=t.getBottomRight(),this.minX=t.getMinX(),this.maxX=t.getMaxX(),this.minY=t.getMinY(),this.maxY=t.getMaxY()},t.merge=function(e,r){return null==e?r:null==r?e:new t(e.image,e.topLeft,e.bottomLeft,r.topRight,r.bottomRight)},t.prototype.addMissingRows=function(e,r,n){var i=this.topLeft,a=this.bottomLeft,u=this.topRight,s=this.bottomRight;if(e>0){var f=n?this.topLeft:this.topRight,l=Math.trunc(f.getY()-e);l<0&&(l=0);var d=new o.default(f.getX(),l);n?i=d:u=d}if(r>0){var c=n?this.bottomLeft:this.bottomRight,h=Math.trunc(c.getY()+r);h>=this.image.getHeight()&&(h=this.image.getHeight()-1);var p=new o.default(c.getX(),h);n?a=p:s=p}return new t(this.image,i,a,u,s)},t.prototype.getMinX=function(){return this.minX},t.prototype.getMaxX=function(){return this.maxX},t.prototype.getMinY=function(){return this.minY},t.prototype.getMaxY=function(){return this.maxY},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getBottomRight=function(){return this.bottomRight},t}();e.default=i},1177:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1110),i=r(1176),a=function(){function t(t){this.boundingBox=new i.default(t),this.codewords=new Array(t.getMaxY()-t.getMinY()+1)}return t.prototype.getCodewordNearby=function(e){var r=this.getCodeword(e);if(null!=r)return r;for(var n=1;n<t.MAX_NEARBY_DISTANCE;n++){var o=this.imageRowToCodewordIndex(e)-n;if(o>=0&&null!=(r=this.codewords[o]))return r;if((o=this.imageRowToCodewordIndex(e)+n)<this.codewords.length&&null!=(r=this.codewords[o]))return r}return null},t.prototype.imageRowToCodewordIndex=function(t){return t-this.boundingBox.getMinY()},t.prototype.setCodeword=function(t,e){this.codewords[this.imageRowToCodewordIndex(t)]=e},t.prototype.getCodeword=function(t){return this.codewords[this.imageRowToCodewordIndex(t)]},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.getCodewords=function(){return this.codewords},t.prototype.toString=function(){var t,e,r=new o.default,i=0;try{for(var a=n(this.codewords),u=a.next();!u.done;u=a.next()){var s=u.value;null!=s?r.format("%3d: %3d|%3d%n",i++,s.getRowNumber(),s.getValue()):r.format("%3d:    |   %n",i++)}}catch(f){t={error:f}}finally{try{u&&!u.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return r.toString()},t.MAX_NEARBY_DISTANCE=5,t}();e.default=a},1178:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}},o=this&&this.__read||function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(u){o={error:u}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var i=r(1024),a=function(){function t(){this.values=new Map}return t.prototype.setValue=function(t){t=Math.trunc(t);var e=this.values.get(t);null==e&&(e=0),e++,this.values.set(t,e)},t.prototype.getValue=function(){var t,e,r=-1,a=new Array,u=function(t,e){var n=function(){return t},o=function(){return e};o()>r?(r=o(),(a=[]).push(n())):o()===r&&a.push(n())};try{for(var s=n(this.values.entries()),f=s.next();!f.done;f=s.next()){var l=o(f.value,2);u(l[0],l[1])}}catch(d){t={error:d}}finally{try{f&&!f.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}return i.default.toIntArray(a)},t.prototype.getConfidence=function(t){return this.values.get(t)},t}();e.default=a},1179:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1180:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1070),o=r(752),i=r(654),a=function(){function t(t){this.field=t,this.cachedGenerators=[],this.cachedGenerators.push(new n.default(t,Int32Array.from([1])))}return t.prototype.buildGenerator=function(t){var e=this.cachedGenerators;if(t>=e.length)for(var r=e[e.length-1],o=this.field,i=e.length;i<=t;i++){var a=r.multiply(new n.default(o,Int32Array.from([1,o.exp(i-1+o.getGeneratorBase())])));e.push(a),r=a}return e[t]},t.prototype.encode=function(t,e){if(0===e)throw new i.default("No error correction bytes");var r=t.length-e;if(r<=0)throw new i.default("No data bytes provided");var a=this.buildGenerator(e),u=new Int32Array(r);o.default.arraycopy(t,0,u,0,r);for(var s=new n.default(this.field,u),f=(s=s.multiplyByMonomial(e,1)).divide(a)[1].getCoefficients(),l=e-f.length,d=0;d<l;d++)t[r+d]=0;o.default.arraycopy(f,0,t,r+l,f.length)},t}();e.default=a},1181:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(654),o=function(){function t(){}return t.applyMaskPenaltyRule1=function(e){return t.applyMaskPenaltyRule1Internal(e,!0)+t.applyMaskPenaltyRule1Internal(e,!1)},t.applyMaskPenaltyRule2=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i-1;a++)for(var u=n[a],s=0;s<o-1;s++){var f=u[s];f===u[s+1]&&f===n[a+1][s]&&f===n[a+1][s+1]&&r++}return t.N2*r},t.applyMaskPenaltyRule3=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var u=0;u<o;u++){var s=n[a];u+6<o&&1===s[u]&&0===s[u+1]&&1===s[u+2]&&1===s[u+3]&&1===s[u+4]&&0===s[u+5]&&1===s[u+6]&&(t.isWhiteHorizontal(s,u-4,u)||t.isWhiteHorizontal(s,u+7,u+11))&&r++,a+6<i&&1===n[a][u]&&0===n[a+1][u]&&1===n[a+2][u]&&1===n[a+3][u]&&1===n[a+4][u]&&0===n[a+5][u]&&1===n[a+6][u]&&(t.isWhiteVertical(n,u,a-4,a)||t.isWhiteVertical(n,u,a+7,a+11))&&r++}return r*t.N3},t.isWhiteHorizontal=function(t,e,r){e=Math.max(e,0),r=Math.min(r,t.length);for(var n=e;n<r;n++)if(1===t[n])return!1;return!0},t.isWhiteVertical=function(t,e,r,n){r=Math.max(r,0),n=Math.min(n,t.length);for(var o=r;o<n;o++)if(1===t[o][e])return!1;return!0},t.applyMaskPenaltyRule4=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var u=n[a],s=0;s<o;s++)1===u[s]&&r++;var f=e.getHeight()*e.getWidth();return Math.floor(10*Math.abs(2*r-f)/f)*t.N4},t.getDataMaskBit=function(t,e,r){var o,i;switch(t){case 0:o=r+e&1;break;case 1:o=1&r;break;case 2:o=e%3;break;case 3:o=(r+e)%3;break;case 4:o=Math.floor(r/2)+Math.floor(e/3)&1;break;case 5:o=(1&(i=r*e))+i%3;break;case 6:o=(1&(i=r*e))+i%3&1;break;case 7:o=(i=r*e)%3+(r+e&1)&1;break;default:throw new n.default("Invalid mask pattern: "+t)}return 0===o},t.applyMaskPenaltyRule1Internal=function(e,r){for(var n=0,o=r?e.getHeight():e.getWidth(),i=r?e.getWidth():e.getHeight(),a=e.getArray(),u=0;u<o;u++){for(var s=0,f=-1,l=0;l<i;l++){var d=r?a[u][l]:a[l][u];d===f?s++:(s>=5&&(n+=t.N1+(s-5)),s=1,f=d)}s>=5&&(n+=t.N1+(s-5))}return n},t.N1=3,t.N2=3,t.N3=40,t.N4=10,t}();e.default=o},1182:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(693),o=r(1075),i=r(825),a=r(1074),u=r(1111),s=r(654),f=r(904),l=function(){function t(){}return t.prototype.encode=function(e,r,i,f,l){if(0===e.length)throw new s.default("Found empty contents");if(r!==n.default.QR_CODE)throw new s.default("Can only encode QR_CODE, but got "+r);if(i<0||f<0)throw new s.default("Requested dimensions are too small: "+i+"x"+f);var d=a.default.L,c=t.QUIET_ZONE_SIZE;null!==l&&(void 0!==l.get(o.default.ERROR_CORRECTION)&&(d=a.default.fromString(l.get(o.default.ERROR_CORRECTION).toString())),void 0!==l.get(o.default.MARGIN)&&(c=Number.parseInt(l.get(o.default.MARGIN).toString(),10)));var h=u.default.encode(e,d,l);return t.renderResult(h,i,f,c)},t.renderResult=function(t,e,r,n){var o=t.getMatrix();if(null===o)throw new f.default;for(var a=o.getWidth(),u=o.getHeight(),s=a+2*n,l=u+2*n,d=Math.max(e,s),c=Math.max(r,l),h=Math.min(Math.floor(d/s),Math.floor(c/l)),p=Math.floor((d-a*h)/2),_=Math.floor((c-u*h)/2),g=new i.default(d,c),v=0,w=_;v<u;v++,w+=h)for(var y=0,E=p;y<a;y++,E+=h)1===o.get(y,v)&&g.setRegion(E,w,h,h);return g},t.QUIET_ZONE_SIZE=4,t}();e.default=l},1183:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(692),o=r(904),i=r(749),a=r(1326),u=r(1327),s=r(1328),f=r(1329),l=r(1330),d=function(){function t(t){this.buffer=new i.default,this.information=t}return t.prototype.decodeAllCodes=function(t,e){for(var r=e,n=null;;){var o=this.decodeGeneralPurposeField(r,n),i=f.default.parseFieldsInGeneralPurpose(o.getNewString());if(null!=i&&t.append(i),n=o.isRemaining()?""+o.getRemainingValue():null,r==o.getNewPosition())break;r=o.getNewPosition()}return t.toString()},t.prototype.isStillNumeric=function(t){if(t+7>this.information.getSize())return t+4<=this.information.getSize();for(var e=t;e<t+3;++e)if(this.information.get(e))return!0;return this.information.get(t+3)},t.prototype.decodeNumeric=function(t){if(t+7>this.information.getSize()){var e=this.extractNumericValueFromBitArray(t,4);return 0==e?new u.default(this.information.getSize(),u.default.FNC1,u.default.FNC1):new u.default(this.information.getSize(),e-1,u.default.FNC1)}var r=this.extractNumericValueFromBitArray(t,7),n=(r-8)/11,o=(r-8)%11;return new u.default(t+7,n,o)},t.prototype.extractNumericValueFromBitArray=function(e,r){return t.extractNumericValueFromBitArray(this.information,e,r)},t.extractNumericValueFromBitArray=function(t,e,r){for(var n=0,o=0;o<r;++o)t.get(e+o)&&(n|=1<<r-o-1);return n},t.prototype.decodeGeneralPurposeField=function(t,e){this.buffer.setLengthToZero(),null!=e&&this.buffer.append(e),this.current.setPosition(t);var r=this.parseBlocks();return null!=r&&r.isRemaining()?new s.default(this.current.getPosition(),this.buffer.toString(),r.getRemainingValue()):new s.default(this.current.getPosition(),this.buffer.toString())},t.prototype.parseBlocks=function(){var t,e;do{var r=this.current.getPosition();if(t=this.current.isAlpha()?(e=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(e=this.parseIsoIec646Block()).isFinished():(e=this.parseNumericBlock()).isFinished(),!(r!=this.current.getPosition())&&!t)break}while(!t);return e.getDecodedInformation()},t.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var t=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFirstDigitFNC1()){var e=void 0;return e=t.isSecondDigitFNC1()?new s.default(this.current.getPosition(),this.buffer.toString()):new s.default(this.current.getPosition(),this.buffer.toString(),t.getSecondDigit()),new l.default(!0,e)}if(this.buffer.append(t.getFirstDigit()),t.isSecondDigitFNC1()){e=new s.default(this.current.getPosition(),this.buffer.toString());return new l.default(!0,e)}this.buffer.append(t.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new l.default(!1)},t.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var t=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1()){var e=new s.default(this.current.getPosition(),this.buffer.toString());return new l.default(!0,e)}this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new l.default(!1)},t.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var t=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1()){var e=new s.default(this.current.getPosition(),this.buffer.toString());return new l.default(!0,e)}this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new l.default(!1)},t.prototype.isStillIsoIec646=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+7>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,7);if(r>=64&&r<116)return!0;if(t+8>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(t,8);return n>=232&&n<253},t.prototype.decodeIsoIec646=function(t){var e=this.extractNumericValueFromBitArray(t,5);if(15==e)return new a.default(t+5,a.default.FNC1);if(e>=5&&e<15)return new a.default(t+5,"0"+(e-5));var r,o=this.extractNumericValueFromBitArray(t,7);if(o>=64&&o<90)return new a.default(t+7,""+(o+1));if(o>=90&&o<116)return new a.default(t+7,""+(o+7));switch(this.extractNumericValueFromBitArray(t,8)){case 232:r="!";break;case 233:r='"';break;case 234:r="%";break;case 235:r="&";break;case 236:r="'";break;case 237:r="(";break;case 238:r=")";break;case 239:r="*";break;case 240:r="+";break;case 241:r=",";break;case 242:r="-";break;case 243:r=".";break;case 244:r="/";break;case 245:r=":";break;case 246:r=";";break;case 247:r="<";break;case 248:r="=";break;case 249:r=">";break;case 250:r="?";break;case 251:r="_";break;case 252:r=" ";break;default:throw new n.default}return new a.default(t+8,r)},t.prototype.isStillAlpha=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+6>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,6);return r>=16&&r<63},t.prototype.decodeAlphanumeric=function(t){var e=this.extractNumericValueFromBitArray(t,5);if(15==e)return new a.default(t+5,a.default.FNC1);if(e>=5&&e<15)return new a.default(t+5,"0"+(e-5));var r,n=this.extractNumericValueFromBitArray(t,6);if(n>=32&&n<58)return new a.default(t+6,""+(n+33));switch(n){case 58:r="*";break;case 59:r=",";break;case 60:r="-";break;case 61:r=".";break;case 62:r="/";break;default:throw new o.default("Decoding invalid alphanumeric value: "+n)}return new a.default(t+6,r)},t.prototype.isAlphaTo646ToAlphaLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<5&&e+t<this.information.getSize();++e)if(2==e){if(!this.information.get(t+2))return!1}else if(this.information.get(t+e))return!1;return!0},t.prototype.isAlphaOr646ToNumericLatch=function(t){if(t+3>this.information.getSize())return!1;for(var e=t;e<t+3;++e)if(this.information.get(e))return!1;return!0},t.prototype.isNumericToAlphaNumericLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<4&&e+t<this.information.getSize();++e)if(this.information.get(t+e))return!1;return!0},t}();e.default=d},1184:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1183),o=function(){function t(t){this.information=t,this.generalDecoder=new n.default(t)}return t.prototype.getInformation=function(){return this.information},t.prototype.getGeneralDecoder=function(){return this.generalDecoder},t}();e.default=o},1185:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1186),i=r(749),a=r(642),u=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!=e.HEADER_SIZE+o.default.GTIN_SIZE+e.WEIGHT_SIZE)throw new a.default;var t=new i.default;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+o.default.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e}(o.default);e.default=u},1186:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.encodeCompressedWeight=function(t,e,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e,r);this.addWeightCode(t,n);for(var o=this.checkWeight(n),i=1e5,a=0;a<5;++a)o/i==0&&t.append("0"),i/=10;t.append(o)},e}(r(1076).default);e.default=o},1261:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const o=n(r(0)),i=r(1149),a=n(r(1341));e.default=t=>{let{onUpdate:e,onError:r,width:n="100%",height:u="100%",facingMode:s="environment",torch:f,delay:l=500,videoConstraints:d,stopStream:c}=t;const h=o.default.useRef(null),p=o.default.useCallback((()=>{var t;const r=new i.BrowserMultiFormatReader,n=null===(t=null===h||void 0===h?void 0:h.current)||void 0===t?void 0:t.getScreenshot();n&&r.decodeFromImage(void 0,n).then((t=>{e(null,t)})).catch((t=>{e(t)}))}),[e]);return o.default.useEffect((()=>{var t,r;if("boolean"===typeof f&&(null===(t=null===navigator||void 0===navigator?void 0:navigator.mediaDevices)||void 0===t?void 0:t.getSupportedConstraints().torch)){const t=null===(r=null===h||void 0===h?void 0:h.current)||void 0===r?void 0:r.video.srcObject,n=null===t||void 0===t?void 0:t.getVideoTracks()[0];n&&n.getCapabilities().torch&&!n.getConstraints().torch&&n.applyConstraints({advanced:[{torch:f}]}).catch((t=>e(t)))}}),[f,e]),o.default.useEffect((()=>{var t;if(c){let e=null===(t=null===h||void 0===h?void 0:h.current)||void 0===t?void 0:t.video.srcObject;e&&(e.getTracks().forEach((t=>{e.removeTrack(t),t.stop()})),e=null)}}),[c]),o.default.useEffect((()=>{const t=setInterval(p,l);return()=>{clearInterval(t)}}),[]),o.default.createElement(a.default,{width:n,height:u,ref:h,screenshotFormat:"image/jpeg",videoConstraints:d||{facingMode:s},audio:!1,onUserMediaError:r})}},1262:function(t,e,r){"use strict";function n(t){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}Object.defineProperty(e,"__esModule",{value:!0}),n(r(1263)),n(r(1269)),n(r(1020)),n(r(1276)),n(r(1283)),n(r(1315)),n(r(1316)),n(r(1317)),n(r(1154)),n(r(1155))},1263:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1100),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new i.default,e)||this}return n(e,t),e}(o.BrowserCodeReader);e.BrowserAztecCodeReader=a},1264:function(t,e,r){"use strict";function n(t,e){void 0===e&&(e=t.constructor);var r=Error.captureStackTrace;r&&r(t,e)}r.r(e),r.d(e,"CustomError",(function(){return i})),r.d(e,"customErrorFactory",(function(){return u}));var o=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),i=function(t){function e(e,r){var o=this.constructor,i=t.call(this,e,r)||this;return Object.defineProperty(i,"name",{value:o.name,enumerable:!1,configurable:!0}),function(t,e){var r=Object.setPrototypeOf;r?r(t,e):t.__proto__=e}(i,o.prototype),n(i),i}return o(e,t),e}(Error),a=function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))};function u(t,e){function r(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];if(!(this instanceof r))return new(r.bind.apply(r,a([void 0],o,!1)));e.apply(this,o),Object.defineProperty(this,"name",{value:t.name||e.name,enumerable:!1,configurable:!0}),t.apply(this,o),n(this,r)}return void 0===e&&(e=Error),Object.defineProperties(r,{prototype:{value:Object.create(e.prototype,{constructor:{value:r,writable:!0,configurable:!0}})}})}},1265:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r){void 0===e&&(e=void 0),void 0===r&&(r=void 0);var n=t.call(this,r)||this;return n.index=e,n.message=r,n}return n(e,t),e}(r(1098).default);e.default=o},1266:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=r(1054),i=r(1023),a=r(1055),u=r(904),s=r(692),f=r(1149),l=r(898);!function(t){t[t.UPPER=0]="UPPER",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.DIGIT=3]="DIGIT",t[t.PUNCT=4]="PUNCT",t[t.BINARY=5]="BINARY"}(n||(n={}));var d=function(){function t(){}return t.prototype.decode=function(e){this.ddata=e;var r=e.getBits(),n=this.extractBits(r),i=this.correctBits(n),a=t.convertBoolArrayToByteArray(i),u=t.getEncodedData(i),s=new o.default(a,u,null,null);return s.setNumBits(i.length),s},t.highLevelDecode=function(t){return this.getEncodedData(t)},t.getEncodedData=function(e){for(var r=e.length,o=n.UPPER,i=n.UPPER,a="",u=0;u<r;)if(i===n.BINARY){if(r-u<5)break;var s=t.readCode(e,u,5);if(u+=5,0===s){if(r-u<11)break;s=t.readCode(e,u,11)+31,u+=11}for(var l=0;l<s;l++){if(r-u<8){u=r;break}var d=t.readCode(e,u,8);a+=f.StringUtils.castAsNonUtf8Char(d),u+=8}i=o}else{var c=i===n.DIGIT?4:5;if(r-u<c)break;d=t.readCode(e,u,c);u+=c;var h=t.getCharacter(i,d);h.startsWith("CTRL_")?(o=i,i=t.getTable(h.charAt(5)),"L"===h.charAt(6)&&(o=i)):(a+=h,i=o)}return a},t.getTable=function(t){switch(t){case"L":return n.LOWER;case"P":return n.PUNCT;case"M":return n.MIXED;case"D":return n.DIGIT;case"B":return n.BINARY;default:return n.UPPER}},t.getCharacter=function(e,r){switch(e){case n.UPPER:return t.UPPER_TABLE[r];case n.LOWER:return t.LOWER_TABLE[r];case n.MIXED:return t.MIXED_TABLE[r];case n.PUNCT:return t.PUNCT_TABLE[r];case n.DIGIT:return t.DIGIT_TABLE[r];default:throw new u.default("Bad table")}},t.prototype.correctBits=function(e){var r,n;this.ddata.getNbLayers()<=2?(n=6,r=i.default.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,r=i.default.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,r=i.default.AZTEC_DATA_10):(n=12,r=i.default.AZTEC_DATA_12);var o=this.ddata.getNbDatablocks(),u=e.length/n;if(u<o)throw new s.default;for(var f=e.length%n,l=new Int32Array(u),d=0;d<u;d++,f+=n)l[d]=t.readCode(e,f,n);try{new a.default(r).decode(l,u-o)}catch(w){throw new s.default(w)}var c=(1<<n)-1,h=0;for(d=0;d<o;d++){if(0===(g=l[d])||g===c)throw new s.default;1!==g&&g!==c-1||h++}var p=new Array(o*n-h),_=0;for(d=0;d<o;d++){var g;if(1===(g=l[d])||g===c-1)p.fill(g>1,_,_+n-1),_+=n-1;else for(var v=n-1;v>=0;--v)p[_++]=0!==(g&1<<v)}return p},t.prototype.extractBits=function(t){var e=this.ddata.isCompact(),r=this.ddata.getNbLayers(),n=(e?11:14)+4*r,o=new Int32Array(n),i=new Array(this.totalBitsInLayer(r,e));if(e)for(var a=0;a<o.length;a++)o[a]=a;else{var u=n+1+2*l.default.truncDivision(l.default.truncDivision(n,2)-1,15),s=n/2,f=l.default.truncDivision(u,2);for(a=0;a<s;a++){var d=a+l.default.truncDivision(a,15);o[s-a-1]=f-d-1,o[s+a]=f+d+1}}a=0;for(var c=0;a<r;a++){for(var h=4*(r-a)+(e?9:12),p=2*a,_=n-1-p,g=0;g<h;g++)for(var v=2*g,w=0;w<2;w++)i[c+v+w]=t.get(o[p+w],o[p+g]),i[c+2*h+v+w]=t.get(o[p+g],o[_-w]),i[c+4*h+v+w]=t.get(o[_-w],o[_-g]),i[c+6*h+v+w]=t.get(o[_-g],o[p+w]);c+=8*h}return i},t.readCode=function(t,e,r){for(var n=0,o=e;o<e+r;o++)n<<=1,t[o]&&(n|=1);return n},t.readByte=function(e,r){var n=e.length-r;return n>=8?t.readCode(e,r,8):t.readCode(e,r,n)<<8-n},t.convertBoolArrayToByteArray=function(e){for(var r=new Uint8Array((e.length+7)/8),n=0;n<r.length;n++)r[n]=t.readByte(e,8*n);return r},t.prototype.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],t.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],t.MIXED_TABLE=["CTRL_PS"," ","\\1","\\2","\\3","\\4","\\5","\\6","\\7","\b","\t","\n","\\13","\f","\r","\\33","\\34","\\35","\\36","\\37","@","\\","^","_","`","|","~","\\177","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],t.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],t.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],t}();e.default=d},1267:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(736),o=r(1268),i=r(820),a=r(1102),u=r(1023),s=r(1055),f=r(642),l=r(1072),d=r(898),c=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.toResultPoint=function(){return new n.default(this.getX(),this.getY())},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t}();e.Point=c;var h=function(){function t(t){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=t}return t.prototype.detect=function(){return this.detectMirror(!1)},t.prototype.detectMirror=function(t){var e=this.getMatrixCenter(),r=this.getBullsEyeCorners(e);if(t){var n=r[0];r[0]=r[2],r[2]=n}this.extractParameters(r);var i=this.sampleGrid(this.image,r[this.shift%4],r[(this.shift+1)%4],r[(this.shift+2)%4],r[(this.shift+3)%4]),a=this.getMatrixCornerPoints(r);return new o.default(i,a,this.compact,this.nbDataBlocks,this.nbLayers)},t.prototype.extractParameters=function(t){if(!this.isValidPoint(t[0])||!this.isValidPoint(t[1])||!this.isValidPoint(t[2])||!this.isValidPoint(t[3]))throw new f.default;var e=2*this.nbCenterLayers,r=new Int32Array([this.sampleLine(t[0],t[1],e),this.sampleLine(t[1],t[2],e),this.sampleLine(t[2],t[3],e),this.sampleLine(t[3],t[0],e)]);this.shift=this.getRotation(r,e);for(var n=0,o=0;o<4;o++){var i=r[(this.shift+o)%4];this.compact?(n<<=7,n+=i>>1&127):(n<<=10,n+=(i>>2&992)+(i>>1&31))}var a=this.getCorrectedParameterData(n,this.compact);this.compact?(this.nbLayers=1+(a>>6),this.nbDataBlocks=1+(63&a)):(this.nbLayers=1+(a>>11),this.nbDataBlocks=1+(2047&a))},t.prototype.getRotation=function(t,e){var r=0;t.forEach((function(t,n,o){r=(r<<3)+((t>>e-2<<1)+(1&t))})),r=((1&r)<<11)+(r>>1);for(var n=0;n<4;n++)if(d.default.bitCount(r^this.EXPECTED_CORNER_BITS[n])<=2)return n;throw new f.default},t.prototype.getCorrectedParameterData=function(t,e){var r,n;e?(r=7,n=2):(r=10,n=4);for(var o=r-n,i=new Int32Array(r),a=r-1;a>=0;--a)i[a]=15&t,t>>=4;try{new s.default(u.default.AZTEC_PARAM).decode(i,o)}catch(d){throw new f.default}var l=0;for(a=0;a<n;a++)l=(l<<4)+i[a];return l},t.prototype.getBullsEyeCorners=function(t){var e=t,r=t,o=t,i=t,a=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var u=this.getFirstDifferent(e,a,1,-1),s=this.getFirstDifferent(r,a,1,1),l=this.getFirstDifferent(o,a,-1,1),d=this.getFirstDifferent(i,a,-1,-1);if(this.nbCenterLayers>2){var c=this.distancePoint(d,u)*this.nbCenterLayers/(this.distancePoint(i,e)*(this.nbCenterLayers+2));if(c<.75||c>1.25||!this.isWhiteOrBlackRectangle(u,s,l,d))break}e=u,r=s,o=l,i=d,a=!a}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new f.default;this.compact=5===this.nbCenterLayers;var h=new n.default(e.getX()+.5,e.getY()-.5),p=new n.default(r.getX()+.5,r.getY()+.5),_=new n.default(o.getX()-.5,o.getY()+.5),g=new n.default(i.getX()-.5,i.getY()-.5);return this.expandSquare([h,p,_,g],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},t.prototype.getMatrixCenter=function(){var t,e,r,n;try{t=(l=new a.default(this.image).detect())[0],e=l[1],r=l[2],n=l[3]}catch(d){var o=this.image.getWidth()/2,u=this.image.getHeight()/2;t=this.getFirstDifferent(new c(o+7,u-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new c(o+7,u+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new c(o-7,u+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new c(o-7,u-7),!1,-1,-1).toResultPoint()}var s=i.default.round((t.getX()+n.getX()+e.getX()+r.getX())/4),f=i.default.round((t.getY()+n.getY()+e.getY()+r.getY())/4);try{var l;t=(l=new a.default(this.image,15,s,f).detect())[0],e=l[1],r=l[2],n=l[3]}catch(d){t=this.getFirstDifferent(new c(s+7,f-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new c(s+7,f+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new c(s-7,f+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new c(s-7,f-7),!1,-1,-1).toResultPoint()}return s=i.default.round((t.getX()+n.getX()+e.getX()+r.getX())/4),f=i.default.round((t.getY()+n.getY()+e.getY()+r.getY())/4),new c(s,f)},t.prototype.getMatrixCornerPoints=function(t){return this.expandSquare(t,2*this.nbCenterLayers,this.getDimension())},t.prototype.sampleGrid=function(t,e,r,n,o){var i=l.default.getInstance(),a=this.getDimension(),u=a/2-this.nbCenterLayers,s=a/2+this.nbCenterLayers;return i.sampleGrid(t,a,a,u,u,s,u,s,s,u,s,e.getX(),e.getY(),r.getX(),r.getY(),n.getX(),n.getY(),o.getX(),o.getY())},t.prototype.sampleLine=function(t,e,r){for(var n=0,o=this.distanceResultPoint(t,e),a=o/r,u=t.getX(),s=t.getY(),f=a*(e.getX()-t.getX())/o,l=a*(e.getY()-t.getY())/o,d=0;d<r;d++)this.image.get(i.default.round(u+d*f),i.default.round(s+d*l))&&(n|=1<<r-d-1);return n},t.prototype.isWhiteOrBlackRectangle=function(t,e,r,n){t=new c(t.getX()-3,t.getY()+3),e=new c(e.getX()-3,e.getY()-3),r=new c(r.getX()+3,r.getY()-3),n=new c(n.getX()+3,n.getY()+3);var o=this.getColor(n,t);if(0===o)return!1;var i=this.getColor(t,e);return i===o&&((i=this.getColor(e,r))===o&&(i=this.getColor(r,n))===o)},t.prototype.getColor=function(t,e){for(var r=this.distancePoint(t,e),n=(e.getX()-t.getX())/r,o=(e.getY()-t.getY())/r,a=0,u=t.getX(),s=t.getY(),f=this.image.get(t.getX(),t.getY()),l=Math.ceil(r),d=0;d<l;d++)u+=n,s+=o,this.image.get(i.default.round(u),i.default.round(s))!==f&&a++;var c=a/r;return c>.1&&c<.9?0:c<=.1===f?1:-1},t.prototype.getFirstDifferent=function(t,e,r,n){for(var o=t.getX()+r,i=t.getY()+n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r,i+=n;for(o-=r,i-=n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r;for(o-=r;this.isValid(o,i)&&this.image.get(o,i)===e;)i+=n;return new c(o,i-=n)},t.prototype.expandSquare=function(t,e,r){var o=r/(2*e),i=t[0].getX()-t[2].getX(),a=t[0].getY()-t[2].getY(),u=(t[0].getX()+t[2].getX())/2,s=(t[0].getY()+t[2].getY())/2,f=new n.default(u+o*i,s+o*a),l=new n.default(u-o*i,s-o*a);return i=t[1].getX()-t[3].getX(),a=t[1].getY()-t[3].getY(),u=(t[1].getX()+t[3].getX())/2,s=(t[1].getY()+t[3].getY())/2,[f,new n.default(u+o*i,s+o*a),l,new n.default(u-o*i,s-o*a)]},t.prototype.isValid=function(t,e){return t>=0&&t<this.image.getWidth()&&e>0&&e<this.image.getHeight()},t.prototype.isValidPoint=function(t){var e=i.default.round(t.getX()),r=i.default.round(t.getY());return this.isValid(e,r)},t.prototype.distancePoint=function(t,e){return i.default.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.distanceResultPoint=function(t,e){return i.default.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(d.default.truncDivision(this.nbLayers-4,8)+1)+15},t}();e.default=h},1268:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r,n,o,i){var a=t.call(this,e,r)||this;return a.compact=n,a.nbDatablocks=o,a.nbLayers=i,a}return n(e,t),e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(r(1071).default);e.default=o},1269:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1104),a=function(t){function e(e,r){return void 0===e&&(e=500),t.call(this,new i.default(r),e,r)||this}return n(e,t),e}(o.BrowserCodeReader);e.BrowserBarcodeReader=a},1270:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.count=0,o.finderPattern=n,o}return n(e,t),e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e}(r(1105).default);e.default=o},1271:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(764),u=r(900),s=r(1168),f=r(1275),l=r(642),d=function(t){function e(e){var r=t.call(this)||this,n=null==e?null:e.get(a.default.POSSIBLE_FORMATS),o=[];return null!=n&&(n.indexOf(i.default.EAN_13)>-1&&o.push(new s.default),n.indexOf(i.default.EAN_8)>-1&&o.push(new f.default)),0===o.length&&(o.push(new s.default),o.push(new f.default)),r.readers=o,r}return n(e,t),e.prototype.decodeRow=function(t,e,r){var n,i;try{for(var a=o(this.readers),u=a.next();!u.done;u=a.next()){var s=u.value;try{return s.decodeRow(t,e,r)}catch(f){}}}catch(d){n={error:d}}finally{try{u&&!u.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}throw new l.default},e.prototype.reset=function(){var t,e;try{for(var r=o(this.readers),n=r.next();!n.done;n=r.next()){n.value.reset()}}catch(i){t={error:i}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e}(u.default);e.default=d},1272:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1073),o=r(1273),i=r(1274),a=function(){function t(){}return t.decodeRow=function(t,e,r){var a=n.default.findGuardPattern(e,r,!1,this.EXTENSION_START_PATTERN,new Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return(new o.default).decodeRow(t,e,a)}catch(u){return(new i.default).decodeRow(t,e,a)}},t.EXTENSION_START_PATTERN=[1,1,2],t}();e.default=a},1273:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(1073),a=r(791),u=r(736),s=r(899),f=r(642),l=function(){function t(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=[0,0,0,0],this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,n){var i=this.decodeRowStringBuffer,s=this.decodeMiddle(r,n,i),f=i.toString(),l=t.parseExtensionString(f),d=[new u.default((n[0]+n[1])/2,e),new u.default(s,e)],c=new a.default(f,null,0,d,o.default.UPC_EAN_EXTENSION,(new Date).getTime());return null!=l&&c.putAllMetadata(l),c},t.prototype.decodeMiddle=function(e,r,o){var a,u,s=this.decodeMiddleCounters;s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var l=e.getSize(),d=r[1],c=0,h=0;h<5&&d<l;h++){var p=i.default.decodeDigit(e,s,d,i.default.L_AND_G_PATTERNS);o+=String.fromCharCode("0".charCodeAt(0)+p%10);try{for(var _=n(s),g=_.next();!g.done;g=_.next()){d+=g.value}}catch(w){a={error:w}}finally{try{g&&!g.done&&(u=_.return)&&u.call(_)}finally{if(a)throw a.error}}p>=10&&(c|=1<<4-h),4!==h&&(d=e.getNextSet(d),d=e.getNextUnset(d))}if(5!==o.length)throw new f.default;var v=this.determineCheckDigit(c);if(t.extensionChecksum(o.toString())!==v)throw new f.default;return d},t.extensionChecksum=function(t){for(var e=t.length,r=0,n=e-2;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0);r*=3;for(n=e-1;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0);return(r*=3)%10},t.prototype.determineCheckDigit=function(t){for(var e=0;e<10;e++)if(t===this.CHECK_DIGIT_ENCODINGS[e])return e;throw new f.default},t.parseExtensionString=function(e){if(5!==e.length)return null;var r=t.parseExtension5String(e);return null==r?null:new Map([[s.default.SUGGESTED_PRICE,r]])},t.parseExtension5String=function(t){var e;switch(t.charAt(0)){case"0":e="\xa3";break;case"5":e="$";break;case"9":switch(t){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}e="";break;default:e=""}var r=parseInt(t.substring(1)),n=r%100;return e+(r/100).toString()+"."+(n<10?"0"+n:n.toString())},t}();e.default=l},1274:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(693),i=r(1073),a=r(791),u=r(736),s=r(899),f=r(642),l=function(){function t(){this.decodeMiddleCounters=[0,0,0,0],this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,n){var i=this.decodeRowStringBuffer,s=this.decodeMiddle(r,n,i),f=i.toString(),l=t.parseExtensionString(f),d=[new u.default((n[0]+n[1])/2,e),new u.default(s,e)],c=new a.default(f,null,0,d,o.default.UPC_EAN_EXTENSION,(new Date).getTime());return null!=l&&c.putAllMetadata(l),c},t.prototype.decodeMiddle=function(t,e,r){var o,a,u=this.decodeMiddleCounters;u[0]=0,u[1]=0,u[2]=0,u[3]=0;for(var s=t.getSize(),l=e[1],d=0,c=0;c<2&&l<s;c++){var h=i.default.decodeDigit(t,u,l,i.default.L_AND_G_PATTERNS);r+=String.fromCharCode("0".charCodeAt(0)+h%10);try{for(var p=n(u),_=p.next();!_.done;_=p.next()){l+=_.value}}catch(g){o={error:g}}finally{try{_&&!_.done&&(a=p.return)&&a.call(p)}finally{if(o)throw o.error}}h>=10&&(d|=1<<1-c),1!==c&&(l=t.getNextSet(l),l=t.getNextUnset(l))}if(2!==r.length)throw new f.default;if(parseInt(r.toString())%4!==d)throw new f.default;return l},t.parseExtensionString=function(t){return 2!==t.length?null:new Map([[s.default.ISSUE_NUMBER,parseInt(t)]])},t}();e.default=l},1275:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(1169),u=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=[0,0,0,0],e}return n(e,t),e.prototype.decodeMiddle=function(t,e,r){var n,i,u,s,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var l=t.getSize(),d=e[1],c=0;c<4&&d<l;c++){var h=a.default.decodeDigit(t,f,d,a.default.L_PATTERNS);r+=String.fromCharCode("0".charCodeAt(0)+h);try{for(var p=o(f),_=p.next();!_.done;_=p.next()){d+=_.value}}catch(w){n={error:w}}finally{try{_&&!_.done&&(i=p.return)&&i.call(p)}finally{if(n)throw n.error}}}d=a.default.findGuardPattern(t,d,!0,a.default.MIDDLE_PATTERN,new Array(a.default.MIDDLE_PATTERN.length).fill(0))[1];for(c=0;c<4&&d<l;c++){h=a.default.decodeDigit(t,f,d,a.default.L_PATTERNS);r+=String.fromCharCode("0".charCodeAt(0)+h);try{for(var g=o(f),v=g.next();!v.done;v=g.next()){d+=v.value}}catch(y){u={error:y}}finally{try{v&&!v.done&&(s=g.return)&&s.call(g)}finally{if(u)throw u.error}}}return{rowOffset:d,resultString:r}},e.prototype.getBarcodeFormat=function(){return i.default.EAN_8},e}(a.default);e.default=u},1276:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1106),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new i.default,e)||this}return n(e,t),e}(o.BrowserCodeReader);e.BrowserDatamatrixCodeReader=a},1277:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1055),i=r(1023),a=r(1278),u=r(1280),s=r(1281),f=r(824),l=function(){function t(){this.rsDecoder=new o.default(i.default.DATA_MATRIX_FIELD_256)}return t.prototype.decode=function(t){var e,r,o=new a.default(t),i=o.getVersion(),f=o.readCodewords(),l=u.default.getDataBlocks(f,i),d=0;try{for(var c=n(l),h=c.next();!h.done;h=c.next()){d+=h.value.getNumDataCodewords()}}catch(A){e={error:A}}finally{try{h&&!h.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}for(var p=new Uint8Array(d),_=l.length,g=0;g<_;g++){var v=l[g],w=v.getCodewords(),y=v.getNumDataCodewords();this.correctErrors(w,y);for(var E=0;E<y;E++)p[E*_+g]=w[E]}return s.default.decode(p)},t.prototype.correctErrors=function(t,e){t.length;var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(o){throw new f.default}for(var n=0;n<e;n++)t[n]=r[n]},t}();e.default=l},1278:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(825),o=r(1279),i=r(692),a=r(654),u=function(){function t(e){var r=e.getHeight();if(r<8||r>144||0!==(1&r))throw new i.default;this.version=t.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new n.default(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return t.prototype.getVersion=function(){return this.version},t.readVersion=function(t){var e=t.getHeight(),r=t.getWidth();return o.default.getVersionForDimensions(e,r)},t.prototype.readCodewords=function(){var t=new Int8Array(this.version.getTotalCodewords()),e=0,r=4,n=0,o=this.mappingBitMatrix.getHeight(),a=this.mappingBitMatrix.getWidth(),u=!1,s=!1,f=!1,l=!1;do{if(r!==o||0!==n||u)if(r!==o-2||0!==n||0===(3&a)||s)if(r!==o+4||2!==n||0!==(7&a)||f)if(r!==o-2||0!==n||4!==(7&a)||l){do{r<o&&n>=0&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,a)),r-=2,n+=2}while(r>=0&&n<a);r+=1,n+=3;do{r>=0&&n<a&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,a)),r+=2,n-=2}while(r<o&&n>=0);r+=3,n+=1}else t[e++]=255&this.readCorner4(o,a),r-=2,n+=2,l=!0;else t[e++]=255&this.readCorner3(o,a),r-=2,n+=2,f=!0;else t[e++]=255&this.readCorner2(o,a),r-=2,n+=2,s=!0;else t[e++]=255&this.readCorner1(o,a),r-=2,n+=2,u=!0}while(r<o||n<a);if(e!==this.version.getTotalCodewords())throw new i.default;return t},t.prototype.readModule=function(t,e,r,n){return t<0&&(t+=r,e+=4-(r+4&7)),e<0&&(e+=n,t+=4-(n+4&7)),this.readMappingMatrix.set(e,t),this.mappingBitMatrix.get(e,t)},t.prototype.readUtah=function(t,e,r,n){var o=0;return this.readModule(t-2,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-2,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e,r,n)&&(o|=1),o<<=1,this.readModule(t,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t,e,r,n)&&(o|=1),o},t.prototype.readCorner1=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,1,t,e)&&(r|=1),r<<=1,this.readModule(t-1,2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.readCorner2=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-4,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner3=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-3,t,e)&&(r|=1),r<<=1,this.readModule(1,e-2,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner4=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.extractDataRegion=function(t){var e=this.version.getSymbolSizeRows(),r=this.version.getSymbolSizeColumns();if(t.getHeight()!==e)throw new a.default("Dimension of bitMatrix must match the version size");for(var o=this.version.getDataRegionSizeRows(),i=this.version.getDataRegionSizeColumns(),u=e/o|0,s=r/i|0,f=u*o,l=s*i,d=new n.default(l,f),c=0;c<u;++c)for(var h=c*o,p=0;p<s;++p)for(var _=p*i,g=0;g<o;++g)for(var v=c*(o+2)+1+g,w=h+g,y=0;y<i;++y){var E=p*(i+2)+1+y;if(t.get(E,v)){var A=_+y;d.set(A,w)}}return d},t}();e.default=u},1279:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(692),i=function(){function t(t,e,r){this.ecCodewords=t,this.ecBlocks=[e],r&&this.ecBlocks.push(r)}return t.prototype.getECCodewords=function(){return this.ecCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t}();e.ECBlocks=i;var a=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}();e.ECB=a;var u=function(){function t(t,e,r,o,i,a){var u,s;this.versionNumber=t,this.symbolSizeRows=e,this.symbolSizeColumns=r,this.dataRegionSizeRows=o,this.dataRegionSizeColumns=i,this.ecBlocks=a;var f=0,l=a.getECCodewords(),d=a.getECBlocks();try{for(var c=n(d),h=c.next();!h.done;h=c.next()){var p=h.value;f+=p.getCount()*(p.getDataCodewords()+l)}}catch(_){u={error:_}}finally{try{h&&!h.done&&(s=c.return)&&s.call(c)}finally{if(u)throw u.error}}this.totalCodewords=f}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},t.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},t.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},t.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t.getVersionForDimensions=function(e,r){var i,a;if(0!==(1&e)||0!==(1&r))throw new o.default;try{for(var u=n(t.VERSIONS),s=u.next();!s.done;s=u.next()){var f=s.value;if(f.symbolSizeRows===e&&f.symbolSizeColumns===r)return f}}catch(l){i={error:l}}finally{try{s&&!s.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}throw new o.default},t.prototype.toString=function(){return""+this.versionNumber},t.buildVersions=function(){return[new t(1,10,10,8,8,new i(5,new a(1,3))),new t(2,12,12,10,10,new i(7,new a(1,5))),new t(3,14,14,12,12,new i(10,new a(1,8))),new t(4,16,16,14,14,new i(12,new a(1,12))),new t(5,18,18,16,16,new i(14,new a(1,18))),new t(6,20,20,18,18,new i(18,new a(1,22))),new t(7,22,22,20,20,new i(20,new a(1,30))),new t(8,24,24,22,22,new i(24,new a(1,36))),new t(9,26,26,24,24,new i(28,new a(1,44))),new t(10,32,32,14,14,new i(36,new a(1,62))),new t(11,36,36,16,16,new i(42,new a(1,86))),new t(12,40,40,18,18,new i(48,new a(1,114))),new t(13,44,44,20,20,new i(56,new a(1,144))),new t(14,48,48,22,22,new i(68,new a(1,174))),new t(15,52,52,24,24,new i(42,new a(2,102))),new t(16,64,64,14,14,new i(56,new a(2,140))),new t(17,72,72,16,16,new i(36,new a(4,92))),new t(18,80,80,18,18,new i(48,new a(4,114))),new t(19,88,88,20,20,new i(56,new a(4,144))),new t(20,96,96,22,22,new i(68,new a(4,174))),new t(21,104,104,24,24,new i(56,new a(6,136))),new t(22,120,120,18,18,new i(68,new a(6,175))),new t(23,132,132,20,20,new i(62,new a(8,163))),new t(24,144,144,22,22,new i(62,new a(8,156),new a(2,155))),new t(25,8,18,6,16,new i(7,new a(1,5))),new t(26,8,32,6,14,new i(11,new a(1,10))),new t(27,12,26,10,24,new i(14,new a(1,16))),new t(28,12,36,10,16,new i(18,new a(1,22))),new t(29,16,36,14,16,new i(24,new a(1,32))),new t(30,16,48,14,22,new i(28,new a(1,49)))]},t.VERSIONS=t.buildVersions(),t}();e.default=u},1280:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(654),i=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r){var i,a,u,s,f=r.getECBlocks(),l=0,d=f.getECBlocks();try{for(var c=n(d),h=c.next();!h.done;h=c.next()){l+=(w=h.value).getCount()}}catch(M){i={error:M}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}var p=new Array(l),_=0;try{for(var g=n(d),v=g.next();!v.done;v=g.next())for(var w=v.value,y=0;y<w.getCount();y++){var E=w.getDataCodewords(),A=f.getECCodewords()+E;p[_++]=new t(E,new Uint8Array(A))}}catch(P){u={error:P}}finally{try{v&&!v.done&&(s=g.return)&&s.call(g)}finally{if(u)throw u.error}}var C=p[0].codewords.length-f.getECCodewords(),m=C-1,I=0;for(y=0;y<m;y++)for(var S=0;S<_;S++)p[S].codewords[y]=e[I++];var O=24===r.getVersionNumber(),T=O?8:_;for(S=0;S<T;S++)p[S].codewords[C-1]=e[I++];var R=p[0].codewords.length;for(y=C;y<R;y++)for(S=0;S<_;S++){var b=O?(S+8)%_:S,N=O&&b>7?y-1:y;p[b].codewords[N]=e[I++]}if(I!==e.length)throw new o.default;return p},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}();e.default=i},1281:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=r(1054),i=r(1107),a=r(749),u=r(1051),s=r(1069),f=r(692),l=r(904);!function(t){t[t.PAD_ENCODE=0]="PAD_ENCODE",t[t.ASCII_ENCODE=1]="ASCII_ENCODE",t[t.C40_ENCODE=2]="C40_ENCODE",t[t.TEXT_ENCODE=3]="TEXT_ENCODE",t[t.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",t[t.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",t[t.BASE256_ENCODE=6]="BASE256_ENCODE"}(n||(n={}));var d=function(){function t(){}return t.decode=function(t){var e=new i.default(t),r=new a.default,u=new a.default,s=new Array,l=n.ASCII_ENCODE;do{if(l===n.ASCII_ENCODE)l=this.decodeAsciiSegment(e,r,u);else{switch(l){case n.C40_ENCODE:this.decodeC40Segment(e,r);break;case n.TEXT_ENCODE:this.decodeTextSegment(e,r);break;case n.ANSIX12_ENCODE:this.decodeAnsiX12Segment(e,r);break;case n.EDIFACT_ENCODE:this.decodeEdifactSegment(e,r);break;case n.BASE256_ENCODE:this.decodeBase256Segment(e,r,s);break;default:throw new f.default}l=n.ASCII_ENCODE}}while(l!==n.PAD_ENCODE&&e.available()>0);return u.length()>0&&r.append(u.toString()),new o.default(t,r.toString(),0===s.length?null:s,null)},t.decodeAsciiSegment=function(t,e,r){var o=!1;do{var i=t.readBits(8);if(0===i)throw new f.default;if(i<=128)return o&&(i+=128),e.append(String.fromCharCode(i-1)),n.ASCII_ENCODE;if(129===i)return n.PAD_ENCODE;if(i<=229){var a=i-130;a<10&&e.append("0"),e.append(""+a)}else switch(i){case 230:return n.C40_ENCODE;case 231:return n.BASE256_ENCODE;case 232:e.append(String.fromCharCode(29));break;case 233:case 234:case 241:break;case 235:o=!0;break;case 236:e.append("[)>\x1e05\x1d"),r.insert(0,"\x1e\x04");break;case 237:e.append("[)>\x1e06\x1d"),r.insert(0,"\x1e\x04");break;case 238:return n.ANSIX12_ENCODE;case 239:return n.TEXT_ENCODE;case 240:return n.EDIFACT_ENCODE;default:if(254!==i||0!==t.available())throw new f.default}}while(t.available()>0);return n.ASCII_ENCODE},t.decodeC40Segment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var u=n[a];switch(o){case 0:if(u<3)o=u+1;else{if(!(u<this.C40_BASIC_SET_CHARS.length))throw new f.default;var s=this.C40_BASIC_SET_CHARS[u];r?(e.append(String.fromCharCode(s.charCodeAt(0)+128)),r=!1):e.append(s)}break;case 1:r?(e.append(String.fromCharCode(u+128)),r=!1):e.append(String.fromCharCode(u)),o=0;break;case 2:if(u<this.C40_SHIFT2_SET_CHARS.length){s=this.C40_SHIFT2_SET_CHARS[u];r?(e.append(String.fromCharCode(s.charCodeAt(0)+128)),r=!1):e.append(s)}else switch(u){case 27:e.append(String.fromCharCode(29));break;case 30:r=!0;break;default:throw new f.default}o=0;break;case 3:r?(e.append(String.fromCharCode(u+224)),r=!1):e.append(String.fromCharCode(u+96)),o=0;break;default:throw new f.default}}}while(t.available()>0)},t.decodeTextSegment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var u=n[a];switch(o){case 0:if(u<3)o=u+1;else{if(!(u<this.TEXT_BASIC_SET_CHARS.length))throw new f.default;var s=this.TEXT_BASIC_SET_CHARS[u];r?(e.append(String.fromCharCode(s.charCodeAt(0)+128)),r=!1):e.append(s)}break;case 1:r?(e.append(String.fromCharCode(u+128)),r=!1):e.append(String.fromCharCode(u)),o=0;break;case 2:if(u<this.TEXT_SHIFT2_SET_CHARS.length){s=this.TEXT_SHIFT2_SET_CHARS[u];r?(e.append(String.fromCharCode(s.charCodeAt(0)+128)),r=!1):e.append(s)}else switch(u){case 27:e.append(String.fromCharCode(29));break;case 30:r=!0;break;default:throw new f.default}o=0;break;case 3:if(!(u<this.TEXT_SHIFT3_SET_CHARS.length))throw new f.default;s=this.TEXT_SHIFT3_SET_CHARS[u];r?(e.append(String.fromCharCode(s.charCodeAt(0)+128)),r=!1):e.append(s),o=0;break;default:throw new f.default}}}while(t.available()>0)},t.decodeAnsiX12Segment=function(t,e){var r=[];do{if(8===t.available())return;var n=t.readBits(8);if(254===n)return;this.parseTwoBytes(n,t.readBits(8),r);for(var o=0;o<3;o++){var i=r[o];switch(i){case 0:e.append("\r");break;case 1:e.append("*");break;case 2:e.append(">");break;case 3:e.append(" ");break;default:if(i<14)e.append(String.fromCharCode(i+44));else{if(!(i<40))throw new f.default;e.append(String.fromCharCode(i+51))}}}}while(t.available()>0)},t.parseTwoBytes=function(t,e,r){var n=(t<<8)+e-1,o=Math.floor(n/1600);r[0]=o,n-=1600*o,o=Math.floor(n/40),r[1]=o,r[2]=n-40*o},t.decodeEdifactSegment=function(t,e){do{if(t.available()<=16)return;for(var r=0;r<4;r++){var n=t.readBits(6);if(31===n){var o=8-t.getBitOffset();return void(8!==o&&t.readBits(o))}0===(32&n)&&(n|=64),e.append(String.fromCharCode(n))}}while(t.available()>0)},t.decodeBase256Segment=function(t,e,r){var n,o=1+t.getByteOffset(),i=this.unrandomize255State(t.readBits(8),o++);if((n=0===i?t.available()/8|0:i<250?i:250*(i-249)+this.unrandomize255State(t.readBits(8),o++))<0)throw new f.default;for(var a=new Uint8Array(n),d=0;d<n;d++){if(t.available()<8)throw new f.default;a[d]=this.unrandomize255State(t.readBits(8),o++)}r.push(a);try{e.append(u.default.decode(a,s.default.ISO88591))}catch(c){throw new l.default("Platform does not support required encoding: "+c.message)}},t.unrandomize255State=function(t,e){var r=t-(149*e%255+1);return r>=0?r:r+256},t.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],t.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],t.TEXT_SHIFT2_SET_CHARS=t.C40_SHIFT2_SET_CHARS,t.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",String.fromCharCode(127)],t}();e.default=d},1282:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(736),o=r(1071),i=r(1072),a=r(1102),u=r(642),s=function(){function t(t){this.image=t,this.rectangleDetector=new a.default(this.image)}return t.prototype.detect=function(){var e=this.rectangleDetector.detect(),r=this.detectSolid1(e);if((r=this.detectSolid2(r))[3]=this.correctTopRight(r),!r[3])throw new u.default;var n=(r=this.shiftToModuleCenter(r))[0],i=r[1],a=r[2],s=r[3],f=this.transitionsBetween(n,s)+1,l=this.transitionsBetween(a,s)+1;1===(1&f)&&(f+=1),1===(1&l)&&(l+=1),4*f<7*l&&4*l<7*f&&(f=l=Math.max(f,l));var d=t.sampleGrid(this.image,n,i,a,s,f,l);return new o.default(d,[n,i,a,s])},t.shiftPoint=function(t,e,r){var o=(e.getX()-t.getX())/(r+1),i=(e.getY()-t.getY())/(r+1);return new n.default(t.getX()+o,t.getY()+i)},t.moveAway=function(t,e,r){var o=t.getX(),i=t.getY();return o<e?o-=1:o+=1,i<r?i-=1:i+=1,new n.default(o,i)},t.prototype.detectSolid1=function(t){var e=t[0],r=t[1],n=t[3],o=t[2],i=this.transitionsBetween(e,r),a=this.transitionsBetween(r,n),u=this.transitionsBetween(n,o),s=this.transitionsBetween(o,e),f=i,l=[o,e,r,n];return f>a&&(f=a,l[0]=e,l[1]=r,l[2]=n,l[3]=o),f>u&&(f=u,l[0]=r,l[1]=n,l[2]=o,l[3]=e),f>s&&(l[0]=n,l[1]=o,l[2]=e,l[3]=r),l},t.prototype.detectSolid2=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),u=t.shiftPoint(n,o,4*(a+1)),s=t.shiftPoint(o,n,4*(a+1));return this.transitionsBetween(u,r)<this.transitionsBetween(s,i)?(e[0]=r,e[1]=n,e[2]=o,e[3]=i):(e[0]=n,e[1]=o,e[2]=i,e[3]=r),e},t.prototype.correctTopRight=function(e){var r=e[0],o=e[1],i=e[2],a=e[3],u=this.transitionsBetween(r,a),s=this.transitionsBetween(o,a),f=t.shiftPoint(r,o,4*(s+1)),l=t.shiftPoint(i,o,4*(u+1));u=this.transitionsBetween(f,a),s=this.transitionsBetween(l,a);var d=new n.default(a.getX()+(i.getX()-o.getX())/(u+1),a.getY()+(i.getY()-o.getY())/(u+1)),c=new n.default(a.getX()+(r.getX()-o.getX())/(s+1),a.getY()+(r.getY()-o.getY())/(s+1));return this.isValid(d)?this.isValid(c)?this.transitionsBetween(f,d)+this.transitionsBetween(l,d)>this.transitionsBetween(f,c)+this.transitionsBetween(l,c)?d:c:d:this.isValid(c)?c:null},t.prototype.shiftToModuleCenter=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i)+1,u=this.transitionsBetween(o,i)+1,s=t.shiftPoint(r,n,4*u),f=t.shiftPoint(o,n,4*a);1===(1&(a=this.transitionsBetween(s,i)+1))&&(a+=1),1===(1&(u=this.transitionsBetween(f,i)+1))&&(u+=1);var l,d,c=(r.getX()+n.getX()+o.getX()+i.getX())/4,h=(r.getY()+n.getY()+o.getY()+i.getY())/4;return r=t.moveAway(r,c,h),n=t.moveAway(n,c,h),o=t.moveAway(o,c,h),i=t.moveAway(i,c,h),s=t.shiftPoint(r,n,4*u),s=t.shiftPoint(s,i,4*a),l=t.shiftPoint(n,r,4*u),l=t.shiftPoint(l,o,4*a),f=t.shiftPoint(o,i,4*u),f=t.shiftPoint(f,n,4*a),d=t.shiftPoint(i,o,4*u),[s,l,f,d=t.shiftPoint(d,r,4*a)]},t.prototype.isValid=function(t){return t.getX()>=0&&t.getX()<this.image.getWidth()&&t.getY()>0&&t.getY()<this.image.getHeight()},t.sampleGrid=function(t,e,r,n,o,a,u){return i.default.getInstance().sampleGrid(t,a,u,.5,.5,a-.5,.5,a-.5,u-.5,.5,u-.5,e.getX(),e.getY(),o.getX(),o.getY(),n.getX(),n.getY(),r.getX(),r.getY())},t.prototype.transitionsBetween=function(t,e){var r=Math.trunc(t.getX()),n=Math.trunc(t.getY()),o=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.abs(i-n)>Math.abs(o-r);if(a){var u=r;r=n,n=u,u=o,o=i,i=u}for(var s=Math.abs(o-r),f=Math.abs(i-n),l=-s/2,d=n<i?1:-1,c=r<o?1:-1,h=0,p=this.image.get(a?n:r,a?r:n),_=r,g=n;_!==o;_+=c){var v=this.image.get(a?g:_,a?_:g);if(v!==p&&(h++,p=v),(l+=f)>0){if(g===i)break;g+=d,l-=s}}return h},t}();e.default=s},1283:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1170),a=function(t){function e(e,r){void 0===e&&(e=null),void 0===r&&(r=500);var n=new i.default;return n.setHints(e),t.call(this,n,r)||this}return n(e,t),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e}(o.BrowserCodeReader);e.BrowserMultiFormatReader=a},1284:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(825),i=r(1023),a=r(1055),u=r(1285),s=r(1172),f=r(1289),l=r(1290),d=r(824),c=function(){function t(){this.rsDecoder=new a.default(i.default.QR_CODE_FIELD_256)}return t.prototype.decodeBooleanArray=function(t,e){return this.decodeBitMatrix(o.default.parseFromBooleanArray(t),e)},t.prototype.decodeBitMatrix=function(t,e){var r=new u.default(t),n=null;try{return this.decodeBitMatrixParser(r,e)}catch(i){n=i}try{r.remask(),r.setMirror(!0),r.readVersion(),r.readFormatInformation(),r.mirror();var o=this.decodeBitMatrixParser(r,e);return o.setOther(new s.default(!0)),o}catch(i){if(null!==n)throw n;throw i}},t.prototype.decodeBitMatrixParser=function(t,e){var r,o,i,a,u=t.readVersion(),s=t.readFormatInformation().getErrorCorrectionLevel(),d=t.readCodewords(),c=f.default.getDataBlocks(d,u,s),h=0;try{for(var p=n(c),_=p.next();!_.done;_=p.next()){h+=(E=_.value).getNumDataCodewords()}}catch(I){r={error:I}}finally{try{_&&!_.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}var g=new Uint8Array(h),v=0;try{for(var w=n(c),y=w.next();!y.done;y=w.next()){var E,A=(E=y.value).getCodewords(),C=E.getNumDataCodewords();this.correctErrors(A,C);for(var m=0;m<C;m++)g[v++]=A[m]}}catch(S){i={error:S}}finally{try{y&&!y.done&&(a=w.return)&&a.call(w)}finally{if(i)throw i.error}}return l.default.decode(g,u,s,e)},t.prototype.correctErrors=function(t,e){t.length;var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(o){throw new d.default}for(var n=0;n<e;n++)t[n]=r[n]},t}();e.default=c},1285:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1109),o=r(1171),i=r(1288),a=r(692),u=function(){function t(t){var e=t.getHeight();if(e<21||1!==(3&e))throw new a.default;this.bitMatrix=t}return t.prototype.readFormatInformation=function(){if(null!==this.parsedFormatInfo&&void 0!==this.parsedFormatInfo)return this.parsedFormatInfo;for(var t=0,e=0;e<6;e++)t=this.copyBit(e,8,t);t=this.copyBit(7,8,t),t=this.copyBit(8,8,t),t=this.copyBit(8,7,t);for(var r=5;r>=0;r--)t=this.copyBit(8,r,t);var n=this.bitMatrix.getHeight(),i=0,u=n-7;for(r=n-1;r>=u;r--)i=this.copyBit(8,r,i);for(e=n-8;e<n;e++)i=this.copyBit(e,8,i);if(this.parsedFormatInfo=o.default.decodeFormatInformation(t,i),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new a.default},t.prototype.readVersion=function(){if(null!==this.parsedVersion&&void 0!==this.parsedVersion)return this.parsedVersion;var t=this.bitMatrix.getHeight(),e=Math.floor((t-17)/4);if(e<=6)return n.default.getVersionForNumber(e);for(var r=0,o=t-11,i=5;i>=0;i--)for(var u=t-9;u>=o;u--)r=this.copyBit(u,i,r);var s=n.default.decodeVersionInformation(r);if(null!==s&&s.getDimensionForVersion()===t)return this.parsedVersion=s,s;r=0;for(u=5;u>=0;u--)for(i=t-9;i>=o;i--)r=this.copyBit(u,i,r);if(null!==(s=n.default.decodeVersionInformation(r))&&s.getDimensionForVersion()===t)return this.parsedVersion=s,s;throw new a.default},t.prototype.copyBit=function(t,e,r){return(this.isMirror?this.bitMatrix.get(e,t):this.bitMatrix.get(t,e))?r<<1|1:r<<1},t.prototype.readCodewords=function(){var t=this.readFormatInformation(),e=this.readVersion(),r=i.default.values.get(t.getDataMask()),n=this.bitMatrix.getHeight();r.unmaskBitMatrix(this.bitMatrix,n);for(var o=e.buildFunctionPattern(),u=!0,s=new Uint8Array(e.getTotalCodewords()),f=0,l=0,d=0,c=n-1;c>0;c-=2){6===c&&c--;for(var h=0;h<n;h++)for(var p=u?n-1-h:h,_=0;_<2;_++)o.get(c-_,p)||(d++,l<<=1,this.bitMatrix.get(c-_,p)&&(l|=1),8===d&&(s[f++]=l,d=0,l=0));u=!u}if(f!==e.getTotalCodewords())throw new a.default;return s},t.prototype.remask=function(){if(null!==this.parsedFormatInfo){var t=i.default.values[this.parsedFormatInfo.getDataMask()],e=this.bitMatrix.getHeight();t.unmaskBitMatrix(this.bitMatrix,e)}},t.prototype.setMirror=function(t){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=t},t.prototype.mirror=function(){for(var t=this.bitMatrix,e=0,r=t.getWidth();e<r;e++)for(var n=e+1,o=t.getHeight();n<o;n++)t.get(e,n)!==t.get(n,e)&&(t.flip(n,e),t.flip(e,n))},t}();e.default=u},1286:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=function(){function t(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];this.ecCodewordsPerBlock=t,this.ecBlocks=e}return t.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},t.prototype.getNumBlocks=function(){var t,e,r=0,o=this.ecBlocks;try{for(var i=n(o),a=i.next();!a.done;a=i.next()){r+=a.value.getCount()}}catch(u){t={error:u}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return r},t.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},t.prototype.getECBlocks=function(){return this.ecBlocks},t}();e.default=o},1287:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}();e.default=n},1288:function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.DATA_MASK_000=0]="DATA_MASK_000",t[t.DATA_MASK_001=1]="DATA_MASK_001",t[t.DATA_MASK_010=2]="DATA_MASK_010",t[t.DATA_MASK_011=3]="DATA_MASK_011",t[t.DATA_MASK_100=4]="DATA_MASK_100",t[t.DATA_MASK_101=5]="DATA_MASK_101",t[t.DATA_MASK_110=6]="DATA_MASK_110",t[t.DATA_MASK_111=7]="DATA_MASK_111"}(n=e.DataMaskValues||(e.DataMaskValues={}));var o=function(){function t(t,e){this.value=t,this.isMasked=e}return t.prototype.unmaskBitMatrix=function(t,e){for(var r=0;r<e;r++)for(var n=0;n<e;n++)this.isMasked(r,n)&&t.flip(n,r)},t.values=new Map([[n.DATA_MASK_000,new t(n.DATA_MASK_000,(function(t,e){return 0===(t+e&1)}))],[n.DATA_MASK_001,new t(n.DATA_MASK_001,(function(t,e){return 0===(1&t)}))],[n.DATA_MASK_010,new t(n.DATA_MASK_010,(function(t,e){return e%3===0}))],[n.DATA_MASK_011,new t(n.DATA_MASK_011,(function(t,e){return(t+e)%3===0}))],[n.DATA_MASK_100,new t(n.DATA_MASK_100,(function(t,e){return 0===(Math.floor(t/2)+Math.floor(e/3)&1)}))],[n.DATA_MASK_101,new t(n.DATA_MASK_101,(function(t,e){return t*e%6===0}))],[n.DATA_MASK_110,new t(n.DATA_MASK_110,(function(t,e){return t*e%6<3}))],[n.DATA_MASK_111,new t(n.DATA_MASK_111,(function(t,e){return 0===(t+e+t*e%3&1)}))]]),t}();e.default=o},1289:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(654),i=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r,i){var a,u,s,f;if(e.length!==r.getTotalCodewords())throw new o.default;var l=r.getECBlocksForLevel(i),d=0,c=l.getECBlocks();try{for(var h=n(c),p=h.next();!p.done;p=h.next()){d+=(y=p.value).getCount()}}catch(N){a={error:N}}finally{try{p&&!p.done&&(u=h.return)&&u.call(h)}finally{if(a)throw a.error}}var _=new Array(d),g=0;try{for(var v=n(c),w=v.next();!w.done;w=v.next())for(var y=w.value,E=0;E<y.getCount();E++){var A=y.getDataCodewords(),C=l.getECCodewordsPerBlock()+A;_[g++]=new t(A,new Uint8Array(C))}}catch(M){s={error:M}}finally{try{w&&!w.done&&(f=v.return)&&f.call(v)}finally{if(s)throw s.error}}for(var m=_[0].codewords.length,I=_.length-1;I>=0;){if(_[I].codewords.length===m)break;I--}I++;var S=m-l.getECCodewordsPerBlock(),O=0;for(E=0;E<S;E++)for(var T=0;T<g;T++)_[T].codewords[E]=e[O++];for(T=I;T<g;T++)_[T].codewords[S]=e[O++];var R=_[0].codewords.length;for(E=S;E<R;E++)for(T=0;T<g;T++){var b=T<I?E:E+1;_[T].codewords[b]=e[O++]}return _},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}();e.default=i},1290:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1107),o=r(1032),i=r(1054),a=r(1069),u=r(1173),s=r(749),f=r(1051),l=r(692),d=function(){function t(){}return t.decode=function(e,r,a,f){var d=new n.default(e),c=new s.default,h=new Array,p=-1,_=-1;try{var g=null,v=!1,w=void 0;do{if(d.available()<4)w=u.default.TERMINATOR;else{var y=d.readBits(4);w=u.default.forBits(y)}switch(w){case u.default.TERMINATOR:break;case u.default.FNC1_FIRST_POSITION:case u.default.FNC1_SECOND_POSITION:v=!0;break;case u.default.STRUCTURED_APPEND:if(d.available()<16)throw new l.default;p=d.readBits(8),_=d.readBits(8);break;case u.default.ECI:var E=t.parseECIValue(d);if(null===(g=o.default.getCharacterSetECIByValue(E)))throw new l.default;break;case u.default.HANZI:var A=d.readBits(4),C=d.readBits(w.getCharacterCountBits(r));A===t.GB2312_SUBSET&&t.decodeHanziSegment(d,c,C);break;default:var m=d.readBits(w.getCharacterCountBits(r));switch(w){case u.default.NUMERIC:t.decodeNumericSegment(d,c,m);break;case u.default.ALPHANUMERIC:t.decodeAlphanumericSegment(d,c,m,v);break;case u.default.BYTE:t.decodeByteSegment(d,c,m,g,h,f);break;case u.default.KANJI:t.decodeKanjiSegment(d,c,m);break;default:throw new l.default}}}while(w!==u.default.TERMINATOR)}catch(I){throw new l.default}return new i.default(e,c.toString(),0===h.length?null:h,null===a?null:a.toString(),p,_)},t.decodeHanziSegment=function(t,e,r){if(13*r>t.available())throw new l.default;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),u=i/96<<8&4294967295|i%96;u+=u<959?41377:42657,n[o]=u>>8&255,n[o+1]=255&u,o+=2,r--}try{e.append(f.default.decode(n,a.default.GB2312))}catch(s){throw new l.default(s)}},t.decodeKanjiSegment=function(t,e,r){if(13*r>t.available())throw new l.default;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),u=i/192<<8&4294967295|i%192;u+=u<7936?33088:49472,n[o]=u>>8,n[o+1]=u,o+=2,r--}try{e.append(f.default.decode(n,a.default.SHIFT_JIS))}catch(s){throw new l.default(s)}},t.decodeByteSegment=function(t,e,r,n,o,i){if(8*r>t.available())throw new l.default;for(var u,s=new Uint8Array(r),d=0;d<r;d++)s[d]=t.readBits(8);u=null===n?a.default.guessEncoding(s,i):n.getName();try{e.append(f.default.decode(s,u))}catch(c){throw new l.default(c)}o.push(s)},t.toAlphaNumericChar=function(e){if(e>=t.ALPHANUMERIC_CHARS.length)throw new l.default;return t.ALPHANUMERIC_CHARS[e]},t.decodeAlphanumericSegment=function(e,r,n,o){for(var i=r.length();n>1;){if(e.available()<11)throw new l.default;var a=e.readBits(11);r.append(t.toAlphaNumericChar(Math.floor(a/45))),r.append(t.toAlphaNumericChar(a%45)),n-=2}if(1===n){if(e.available()<6)throw new l.default;r.append(t.toAlphaNumericChar(e.readBits(6)))}if(o)for(var u=i;u<r.length();u++)"%"===r.charAt(u)&&(u<r.length()-1&&"%"===r.charAt(u+1)?r.deleteCharAt(u+1):r.setCharAt(u,String.fromCharCode(29)))},t.decodeNumericSegment=function(e,r,n){for(;n>=3;){if(e.available()<10)throw new l.default;var o=e.readBits(10);if(o>=1e3)throw new l.default;r.append(t.toAlphaNumericChar(Math.floor(o/100))),r.append(t.toAlphaNumericChar(Math.floor(o/10)%10)),r.append(t.toAlphaNumericChar(o%10)),n-=3}if(2===n){if(e.available()<7)throw new l.default;var i=e.readBits(7);if(i>=100)throw new l.default;r.append(t.toAlphaNumericChar(Math.floor(i/10))),r.append(t.toAlphaNumericChar(i%10))}else if(1===n){if(e.available()<4)throw new l.default;var a=e.readBits(4);if(a>=10)throw new l.default;r.append(t.toAlphaNumericChar(a))}},t.parseECIValue=function(t){var e=t.readBits(8);if(0===(128&e))return 127&e;if(128===(192&e))return(63&e)<<8&4294967295|t.readBits(8);if(192===(224&e))return(31&e)<<16&4294967295|t.readBits(16);throw new l.default},t.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t.GB2312_SUBSET=1,t}();e.default=d},1291:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(764),o=r(736),i=r(1071),a=r(1072),u=r(1103),s=r(820),f=r(1109),l=r(1292),d=r(1295),c=r(642),h=function(){function t(t){this.image=t}return t.prototype.getImage=function(){return this.image},t.prototype.getResultPointCallback=function(){return this.resultPointCallback},t.prototype.detect=function(t){this.resultPointCallback=null===t||void 0===t?null:t.get(n.default.NEED_RESULT_POINT_CALLBACK);var e=new l.default(this.image,this.resultPointCallback).find(t);return this.processFinderPatternInfo(e)},t.prototype.processFinderPatternInfo=function(e){var r=e.getTopLeft(),n=e.getTopRight(),o=e.getBottomLeft(),a=this.calculateModuleSize(r,n,o);if(a<1)throw new c.default("No pattern found in proccess finder.");var u=t.computeDimension(r,n,o,a),s=f.default.getProvisionalVersionForDimension(u),l=s.getDimensionForVersion()-7,d=null;if(s.getAlignmentPatternCenters().length>0)for(var h=n.getX()-r.getX()+o.getX(),p=n.getY()-r.getY()+o.getY(),_=1-3/l,g=Math.floor(r.getX()+_*(h-r.getX())),v=Math.floor(r.getY()+_*(p-r.getY())),w=4;w<=16;w<<=1)try{d=this.findAlignmentInRegion(a,g,v,w);break}catch(C){if(!(C instanceof c.default))throw C}var y,E=t.createTransform(r,n,o,d,u),A=t.sampleGrid(this.image,E,u);return y=null===d?[o,r,n]:[o,r,n,d],new i.default(A,y)},t.createTransform=function(t,e,r,n,o){var i,a,s,f,l=o-3.5;return null!==n?(i=n.getX(),a=n.getY(),f=s=l-3):(i=e.getX()-t.getX()+r.getX(),a=e.getY()-t.getY()+r.getY(),s=l,f=l),u.default.quadrilateralToQuadrilateral(3.5,3.5,l,3.5,s,f,3.5,l,t.getX(),t.getY(),e.getX(),e.getY(),i,a,r.getX(),r.getY())},t.sampleGrid=function(t,e,r){return a.default.getInstance().sampleGridWithTransform(t,r,r,e)},t.computeDimension=function(t,e,r,n){var i=s.default.round(o.default.distance(t,e)/n),a=s.default.round(o.default.distance(t,r)/n),u=Math.floor((i+a)/2)+7;switch(3&u){case 0:u++;break;case 2:u--;break;case 3:throw new c.default("Dimensions could be not found.")}return u},t.prototype.calculateModuleSize=function(t,e,r){return(this.calculateModuleSizeOneWay(t,e)+this.calculateModuleSizeOneWay(t,r))/2},t.prototype.calculateModuleSizeOneWay=function(t,e){var r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY())),n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY()));return isNaN(r)?n/7:isNaN(n)?r/7:(r+n)/14},t.prototype.sizeOfBlackWhiteBlackRunBothWays=function(t,e,r,n){var o=this.sizeOfBlackWhiteBlackRun(t,e,r,n),i=1,a=t-(r-t);a<0?(i=t/(t-a),a=0):a>=this.image.getWidth()&&(i=(this.image.getWidth()-1-t)/(a-t),a=this.image.getWidth()-1);var u=Math.floor(e-(n-e)*i);return i=1,u<0?(i=e/(e-u),u=0):u>=this.image.getHeight()&&(i=(this.image.getHeight()-1-e)/(u-e),u=this.image.getHeight()-1),a=Math.floor(t+(a-t)*i),(o+=this.sizeOfBlackWhiteBlackRun(t,e,a,u))-1},t.prototype.sizeOfBlackWhiteBlackRun=function(t,e,r,n){var o=Math.abs(n-e)>Math.abs(r-t);if(o){var i=t;t=e,e=i,i=r,r=n,n=i}for(var a=Math.abs(r-t),u=Math.abs(n-e),f=-a/2,l=t<r?1:-1,d=e<n?1:-1,c=0,h=r+l,p=t,_=e;p!==h;p+=l){var g=o?_:p,v=o?p:_;if(1===c===this.image.get(g,v)){if(2===c)return s.default.distance(p,_,t,e);c++}if((f+=u)>0){if(_===n)break;_+=d,f-=a}}return 2===c?s.default.distance(r+l,n,t,e):NaN},t.prototype.findAlignmentInRegion=function(t,e,r,n){var o=Math.floor(n*t),i=Math.max(0,e-o),a=Math.min(this.image.getWidth()-1,e+o);if(a-i<3*t)throw new c.default("Alignment top exceeds estimated module size.");var u=Math.max(0,r-o),s=Math.min(this.image.getHeight()-1,r+o);if(s-u<3*t)throw new c.default("Alignment bottom exceeds estimated module size.");return new d.default(this.image,i,u,a-i,s-u,t,this.resultPointCallback).find()},t}();e.default=h},1292:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(764),i=r(736),a=r(1293),u=r(1294),s=r(642),f=function(){function t(t,e){this.image=t,this.resultPointCallback=e,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=e}return t.prototype.getImage=function(){return this.image},t.prototype.getPossibleCenters=function(){return this.possibleCenters},t.prototype.find=function(e){var r=null!==e&&void 0!==e&&void 0!==e.get(o.default.TRY_HARDER),n=null!==e&&void 0!==e&&void 0!==e.get(o.default.PURE_BARCODE),a=this.image,s=a.getHeight(),f=a.getWidth(),l=Math.floor(3*s/(4*t.MAX_MODULES));(l<t.MIN_SKIP||r)&&(l=t.MIN_SKIP);for(var d=!1,c=new Int32Array(5),h=l-1;h<s&&!d;h+=l){c[0]=0,c[1]=0,c[2]=0,c[3]=0,c[4]=0;for(var p=0,_=0;_<f;_++)if(a.get(_,h))1===(1&p)&&p++,c[p]++;else if(0===(1&p))if(4===p)if(t.foundPatternCross(c)){if(!0!==this.handlePossibleCenter(c,h,_,n)){c[0]=c[2],c[1]=c[3],c[2]=c[4],c[3]=1,c[4]=0,p=3;continue}if(l=2,!0===this.hasSkipped)d=this.haveMultiplyConfirmedCenters();else{var g=this.findRowSkip();g>c[2]&&(h+=g-c[2]-l,_=f-1)}p=0,c[0]=0,c[1]=0,c[2]=0,c[3]=0,c[4]=0}else c[0]=c[2],c[1]=c[3],c[2]=c[4],c[3]=1,c[4]=0,p=3;else c[++p]++;else c[p]++;if(t.foundPatternCross(c))!0===this.handlePossibleCenter(c,h,f,n)&&(l=c[0],this.hasSkipped&&(d=this.haveMultiplyConfirmedCenters()))}var v=this.selectBestPatterns();return i.default.orderBestPatterns(v),new u.default(v)},t.centerFromEnd=function(t,e){return e-t[4]-t[3]-t[2]/2},t.foundPatternCross=function(t){for(var e=0,r=0;r<5;r++){var n=t[r];if(0===n)return!1;e+=n}if(e<7)return!1;var o=e/7,i=o/2;return Math.abs(o-t[0])<i&&Math.abs(o-t[1])<i&&Math.abs(3*o-t[2])<3*i&&Math.abs(o-t[3])<i&&Math.abs(o-t[4])<i},t.prototype.getCrossCheckStateCount=function(){var t=this.crossCheckStateCount;return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t},t.prototype.crossCheckDiagonal=function(e,r,n,o){for(var i=this.getCrossCheckStateCount(),a=0,u=this.image;e>=a&&r>=a&&u.get(r-a,e-a);)i[2]++,a++;if(e<a||r<a)return!1;for(;e>=a&&r>=a&&!u.get(r-a,e-a)&&i[1]<=n;)i[1]++,a++;if(e<a||r<a||i[1]>n)return!1;for(;e>=a&&r>=a&&u.get(r-a,e-a)&&i[0]<=n;)i[0]++,a++;if(i[0]>n)return!1;var s=u.getHeight(),f=u.getWidth();for(a=1;e+a<s&&r+a<f&&u.get(r+a,e+a);)i[2]++,a++;if(e+a>=s||r+a>=f)return!1;for(;e+a<s&&r+a<f&&!u.get(r+a,e+a)&&i[3]<n;)i[3]++,a++;if(e+a>=s||r+a>=f||i[3]>=n)return!1;for(;e+a<s&&r+a<f&&u.get(r+a,e+a)&&i[4]<n;)i[4]++,a++;if(i[4]>=n)return!1;var l=i[0]+i[1]+i[2]+i[3]+i[4];return Math.abs(l-o)<2*o&&t.foundPatternCross(i)},t.prototype.crossCheckVertical=function(e,r,n,o){for(var i=this.image,a=i.getHeight(),u=this.getCrossCheckStateCount(),s=e;s>=0&&i.get(r,s);)u[2]++,s--;if(s<0)return NaN;for(;s>=0&&!i.get(r,s)&&u[1]<=n;)u[1]++,s--;if(s<0||u[1]>n)return NaN;for(;s>=0&&i.get(r,s)&&u[0]<=n;)u[0]++,s--;if(u[0]>n)return NaN;for(s=e+1;s<a&&i.get(r,s);)u[2]++,s++;if(s===a)return NaN;for(;s<a&&!i.get(r,s)&&u[3]<n;)u[3]++,s++;if(s===a||u[3]>=n)return NaN;for(;s<a&&i.get(r,s)&&u[4]<n;)u[4]++,s++;if(u[4]>=n)return NaN;var f=u[0]+u[1]+u[2]+u[3]+u[4];return 5*Math.abs(f-o)>=2*o?NaN:t.foundPatternCross(u)?t.centerFromEnd(u,s):NaN},t.prototype.crossCheckHorizontal=function(e,r,n,o){for(var i=this.image,a=i.getWidth(),u=this.getCrossCheckStateCount(),s=e;s>=0&&i.get(s,r);)u[2]++,s--;if(s<0)return NaN;for(;s>=0&&!i.get(s,r)&&u[1]<=n;)u[1]++,s--;if(s<0||u[1]>n)return NaN;for(;s>=0&&i.get(s,r)&&u[0]<=n;)u[0]++,s--;if(u[0]>n)return NaN;for(s=e+1;s<a&&i.get(s,r);)u[2]++,s++;if(s===a)return NaN;for(;s<a&&!i.get(s,r)&&u[3]<n;)u[3]++,s++;if(s===a||u[3]>=n)return NaN;for(;s<a&&i.get(s,r)&&u[4]<n;)u[4]++,s++;if(u[4]>=n)return NaN;var f=u[0]+u[1]+u[2]+u[3]+u[4];return 5*Math.abs(f-o)>=o?NaN:t.foundPatternCross(u)?t.centerFromEnd(u,s):NaN},t.prototype.handlePossibleCenter=function(e,r,n,o){var i=e[0]+e[1]+e[2]+e[3]+e[4],u=t.centerFromEnd(e,n),s=this.crossCheckVertical(r,Math.floor(u),e[2],i);if(!isNaN(s)&&(u=this.crossCheckHorizontal(Math.floor(u),Math.floor(s),e[2],i),!isNaN(u)&&(!o||this.crossCheckDiagonal(Math.floor(s),Math.floor(u),e[2],i)))){for(var f=i/7,l=!1,d=this.possibleCenters,c=0,h=d.length;c<h;c++){var p=d[c];if(p.aboutEquals(f,s,u)){d[c]=p.combineEstimate(s,u,f),l=!0;break}}if(!l){var _=new a.default(u,s,f);d.push(_),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(_)}return!0}return!1},t.prototype.findRowSkip=function(){var e,r;if(this.possibleCenters.length<=1)return 0;var o=null;try{for(var i=n(this.possibleCenters),a=i.next();!a.done;a=i.next()){var u=a.value;if(u.getCount()>=t.CENTER_QUORUM){if(null!=o)return this.hasSkipped=!0,Math.floor((Math.abs(o.getX()-u.getX())-Math.abs(o.getY()-u.getY()))/2);o=u}}}catch(s){e={error:s}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return 0},t.prototype.haveMultiplyConfirmedCenters=function(){var e,r,o,i,a=0,u=0,s=this.possibleCenters.length;try{for(var f=n(this.possibleCenters),l=f.next();!l.done;l=f.next()){(_=l.value).getCount()>=t.CENTER_QUORUM&&(a++,u+=_.getEstimatedModuleSize())}}catch(g){e={error:g}}finally{try{l&&!l.done&&(r=f.return)&&r.call(f)}finally{if(e)throw e.error}}if(a<3)return!1;var d=u/s,c=0;try{for(var h=n(this.possibleCenters),p=h.next();!p.done;p=h.next()){var _=p.value;c+=Math.abs(_.getEstimatedModuleSize()-d)}}catch(v){o={error:v}}finally{try{p&&!p.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}return c<=.05*u},t.prototype.selectBestPatterns=function(){var t,e,r,o,i=this.possibleCenters.length;if(i<3)throw new s.default;var a,u=this.possibleCenters;if(i>3){var f=0,l=0;try{for(var d=n(this.possibleCenters),c=d.next();!c.done;c=d.next()){var h=c.value.getEstimatedModuleSize();f+=h,l+=h*h}}catch(E){t={error:E}}finally{try{c&&!c.done&&(e=d.return)&&e.call(d)}finally{if(t)throw t.error}}a=f/i;var p=Math.sqrt(l/i-a*a);u.sort((function(t,e){var r=Math.abs(e.getEstimatedModuleSize()-a),n=Math.abs(t.getEstimatedModuleSize()-a);return r<n?-1:r>n?1:0}));for(var _=Math.max(.2*a,p),g=0;g<u.length&&u.length>3;g++){var v=u[g];Math.abs(v.getEstimatedModuleSize()-a)>_&&(u.splice(g,1),g--)}}if(u.length>3){f=0;try{for(var w=n(u),y=w.next();!y.done;y=w.next()){f+=y.value.getEstimatedModuleSize()}}catch(A){r={error:A}}finally{try{y&&!y.done&&(o=w.return)&&o.call(w)}finally{if(r)throw r.error}}a=f/u.length,u.sort((function(t,e){if(e.getCount()===t.getCount()){var r=Math.abs(e.getEstimatedModuleSize()-a),n=Math.abs(t.getEstimatedModuleSize()-a);return r<n?1:r>n?-1:0}return e.getCount()-t.getCount()})),u.splice(3)}return[u[0],u[1],u[2]]},t.CENTER_QUORUM=2,t.MIN_SKIP=3,t.MAX_MODULES=57,t}();e.default=f},1293:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r,n,o){var i=t.call(this,e,r)||this;return i.estimatedModuleSize=n,i.count=o,void 0===o&&(i.count=1),i}return n(e,t),e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){var o=this.count+1;return new e((this.count*this.getX()+r)/o,(this.count*this.getY()+t)/o,(this.count*this.estimatedModuleSize+n)/o,o)},e}(r(736).default);e.default=o},1294:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t){this.bottomLeft=t[0],this.topLeft=t[1],this.topRight=t[2]}return t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t}();e.default=n},1295:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1296),i=r(642),a=function(){function t(t,e,r,n,o,i,a){this.image=t,this.startX=e,this.startY=r,this.width=n,this.height=o,this.moduleSize=i,this.resultPointCallback=a,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return t.prototype.find=function(){for(var t=this.startX,e=this.height,r=t+this.width,n=this.startY+e/2,o=new Int32Array(3),a=this.image,u=0;u<e;u++){var s=n+(0===(1&u)?Math.floor((u+1)/2):-Math.floor((u+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var f=t;f<r&&!a.get(f,s);)f++;for(var l=0;f<r;){if(a.get(f,s))if(1===l)o[1]++;else if(2===l){var d;if(this.foundPatternCross(o))if(null!==(d=this.handlePossibleCenter(o,s,f)))return d;o[0]=o[2],o[1]=1,o[2]=0,l=1}else o[++l]++;else 1===l&&l++,o[l]++;f++}if(this.foundPatternCross(o))if(null!==(d=this.handlePossibleCenter(o,s,r)))return d}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new i.default},t.centerFromEnd=function(t,e){return e-t[2]-t[1]/2},t.prototype.foundPatternCross=function(t){for(var e=this.moduleSize,r=e/2,n=0;n<3;n++)if(Math.abs(e-t[n])>=r)return!1;return!0},t.prototype.crossCheckVertical=function(e,r,n,o){var i=this.image,a=i.getHeight(),u=this.crossCheckStateCount;u[0]=0,u[1]=0,u[2]=0;for(var s=e;s>=0&&i.get(r,s)&&u[1]<=n;)u[1]++,s--;if(s<0||u[1]>n)return NaN;for(;s>=0&&!i.get(r,s)&&u[0]<=n;)u[0]++,s--;if(u[0]>n)return NaN;for(s=e+1;s<a&&i.get(r,s)&&u[1]<=n;)u[1]++,s++;if(s===a||u[1]>n)return NaN;for(;s<a&&!i.get(r,s)&&u[2]<=n;)u[2]++,s++;if(u[2]>n)return NaN;var f=u[0]+u[1]+u[2];return 5*Math.abs(f-o)>=2*o?NaN:this.foundPatternCross(u)?t.centerFromEnd(u,s):NaN},t.prototype.handlePossibleCenter=function(e,r,i){var a,u,s=e[0]+e[1]+e[2],f=t.centerFromEnd(e,i),l=this.crossCheckVertical(r,f,2*e[1],s);if(!isNaN(l)){var d=(e[0]+e[1]+e[2])/3;try{for(var c=n(this.possibleCenters),h=c.next();!h.done;h=c.next()){var p=h.value;if(p.aboutEquals(d,l,f))return p.combineEstimate(l,f,d)}}catch(g){a={error:g}}finally{try{h&&!h.done&&(u=c.return)&&u.call(c)}finally{if(a)throw a.error}}var _=new o.default(f,l,d);this.possibleCenters.push(_),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(_)}return null},t}();e.default=a},1296:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.estimatedModuleSize=n,o}return n(e,t),e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){return new e((this.getX()+r)/2,(this.getY()+t)/2,(this.estimatedModuleSize+n)/2)},e}(r(736).default);e.default=o},1297:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(736),i=r(752),a=r(1022),u=r(1298),s=function(){function t(){}return t.detectMultiple=function(e,r,n){var o=e.getBlackMatrix(),i=t.detect(n,o);return i.length||((o=o.clone()).rotate180(),i=t.detect(n,o)),new u.default(o,i)},t.detect=function(e,r){for(var o,i,a=new Array,u=0,s=0,f=!1;u<r.getHeight();){var l=t.findVertices(r,u,s);if(null!=l[0]||null!=l[3]){if(f=!0,a.push(l),!e)break;null!=l[2]?(s=Math.trunc(l[2].getX()),u=Math.trunc(l[2].getY())):(s=Math.trunc(l[4].getX()),u=Math.trunc(l[4].getY()))}else{if(!f)break;f=!1,s=0;try{for(var d=n(a),c=d.next();!c.done;c=d.next()){var h=c.value;null!=h[1]&&(u=Math.trunc(Math.max(u,h[1].getY()))),null!=h[3]&&(u=Math.max(u,Math.trunc(h[3].getY())))}}catch(p){o={error:p}}finally{try{c&&!c.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}u+=t.ROW_STEP}}return a},t.findVertices=function(e,r,n){var o=e.getHeight(),i=e.getWidth(),a=new Array(8);return t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.START_PATTERN),t.INDEXES_START_PATTERN),null!=a[4]&&(n=Math.trunc(a[4].getX()),r=Math.trunc(a[4].getY())),t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.STOP_PATTERN),t.INDEXES_STOP_PATTERN),a},t.copyToResult=function(t,e,r){for(var n=0;n<r.length;n++)t[r[n]]=e[n]},t.findRowsWithPattern=function(e,r,n,i,u,s){for(var f=new Array(4),l=!1,d=new Int32Array(s.length);i<r;i+=t.ROW_STEP){if(null!=(_=t.findGuardPattern(e,u,i,n,!1,s,d))){for(;i>0;){if(null==(p=t.findGuardPattern(e,u,--i,n,!1,s,d))){i++;break}_=p}f[0]=new o.default(_[0],i),f[1]=new o.default(_[1],i),l=!0;break}}var c=i+1;if(l){for(var h=0,p=Int32Array.from([Math.trunc(f[0].getX()),Math.trunc(f[1].getX())]);c<r;c++){var _;if(null!=(_=t.findGuardPattern(e,p[0],c,n,!1,s,d))&&Math.abs(p[0]-_[0])<t.MAX_PATTERN_DRIFT&&Math.abs(p[1]-_[1])<t.MAX_PATTERN_DRIFT)p=_,h=0;else{if(h>t.SKIPPED_ROW_COUNT_MAX)break;h++}}c-=h+1,f[2]=new o.default(p[0],c),f[3]=new o.default(p[1],c)}return c-i<t.BARCODE_MIN_HEIGHT&&a.default.fill(f,null),f},t.findGuardPattern=function(e,r,n,o,u,s,f){a.default.fillWithin(f,0,f.length,0);for(var l=r,d=0;e.get(l,n)&&l>0&&d++<t.MAX_PIXEL_DRIFT;)l--;for(var c=l,h=0,p=s.length,_=u;c<o;c++){if(e.get(c,n)!==_)f[h]++;else{if(h===p-1){if(t.patternMatchVariance(f,s,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE)return new Int32Array([l,c]);l+=f[0]+f[1],i.default.arraycopy(f,2,f,0,h-1),f[h-1]=0,f[h]=0,h--}else h++;f[h]=1,_=!_}}return h===p-1&&t.patternMatchVariance(f,s,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE?new Int32Array([l,c-1]):null},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return 1/0;var u=o/i;r*=u;for(var s=0,f=0;f<n;f++){var l=t[f],d=e[f]*u,c=l>d?l-d:d-l;if(c>r)return 1/0;s+=c}return s/o},t.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),t.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),t.MAX_AVG_VARIANCE=.42,t.MAX_INDIVIDUAL_VARIANCE=.8,t.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),t.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),t.MAX_PIXEL_DRIFT=3,t.MAX_PATTERN_DRIFT=5,t.SKIPPED_ROW_COUNT_MAX=25,t.ROW_STEP=5,t.BARCODE_MIN_HEIGHT=10,t}();e.default=s},1298:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}();e.default=n},1299:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(824),i=r(692),a=r(642),u=r(820),s=r(1024),f=r(1300),l=r(1176),d=r(1303),c=r(1305),h=r(1177),p=r(1306),_=r(1178),g=r(1307),v=r(1308),w=r(1110),y=function(){function t(){}return t.decode=function(e,r,n,o,i,u,s){for(var f,c=new l.default(e,r,n,o,i),p=null,_=null,g=!0;;g=!1){if(null!=r&&(p=t.getRowIndicatorColumn(e,c,r,!0,u,s)),null!=o&&(_=t.getRowIndicatorColumn(e,c,o,!1,u,s)),null==(f=t.merge(p,_)))throw a.default.getNotFoundInstance();var v=f.getBoundingBox();if(!g||null==v||!(v.getMinY()<c.getMinY()||v.getMaxY()>c.getMaxY()))break;c=v}f.setBoundingBox(c);var w=f.getBarcodeColumnCount()+1;f.setDetectionResultColumn(0,p),f.setDetectionResultColumn(w,_);for(var y=null!=p,E=1;E<=w;E++){var A=y?E:w-E;if(void 0===f.getDetectionResultColumn(A)){var C=void 0;C=0===A||A===w?new d.default(c,0===A):new h.default(c),f.setDetectionResultColumn(A,C);for(var m=-1,I=m,S=c.getMinY();S<=c.getMaxY();S++){if((m=t.getStartColumn(f,A,S,y))<0||m>c.getMaxX()){if(-1===I)continue;m=I}var O=t.detectCodeword(e,c.getMinX(),c.getMaxX(),y,m,S,u,s);null!=O&&(C.setCodeword(S,O),I=m,u=Math.min(u,O.getWidth()),s=Math.max(s,O.getWidth()))}}}return t.createDecoderResult(f)},t.merge=function(e,r){if(null==e&&null==r)return null;var n=t.getBarcodeMetadata(e,r);if(null==n)return null;var o=l.default.merge(t.adjustBoundingBox(e),t.adjustBoundingBox(r));return new c.default(n,o)},t.adjustBoundingBox=function(e){var r,o;if(null==e)return null;var i=e.getRowHeights();if(null==i)return null;var a=t.getMax(i),u=0;try{for(var s=n(i),f=s.next();!f.done;f=s.next()){var l=f.value;if(u+=a-l,l>0)break}}catch(p){r={error:p}}finally{try{f&&!f.done&&(o=s.return)&&o.call(s)}finally{if(r)throw r.error}}for(var d=e.getCodewords(),c=0;u>0&&null==d[c];c++)u--;var h=0;for(c=i.length-1;c>=0&&(h+=a-i[c],!(i[c]>0));c--);for(c=d.length-1;h>0&&null==d[c];c--)h--;return e.getBoundingBox().addMissingRows(u,h,e.isLeft())},t.getMax=function(t){var e,r,o=-1;try{for(var i=n(t),a=i.next();!a.done;a=i.next()){var u=a.value;o=Math.max(o,u)}}catch(s){e={error:s}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return o},t.getBarcodeMetadata=function(t,e){var r,n;return null==t||null==(r=t.getBarcodeMetadata())?null==e?null:e.getBarcodeMetadata():null==e||null==(n=e.getBarcodeMetadata())?r:r.getColumnCount()!==n.getColumnCount()&&r.getErrorCorrectionLevel()!==n.getErrorCorrectionLevel()&&r.getRowCount()!==n.getRowCount()?null:r},t.getRowIndicatorColumn=function(e,r,n,o,i,a){for(var u=new d.default(r,o),s=0;s<2;s++)for(var f=0===s?1:-1,l=Math.trunc(Math.trunc(n.getX())),c=Math.trunc(Math.trunc(n.getY()));c<=r.getMaxY()&&c>=r.getMinY();c+=f){var h=t.detectCodeword(e,0,e.getWidth(),o,l,c,i,a);null!=h&&(u.setCodeword(c,h),l=o?h.getStartX():h.getEndX())}return u},t.adjustCodewordCount=function(e,r){var n=r[0][1],o=n.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-t.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===o.length){if(i<1||i>s.default.MAX_CODEWORDS_IN_BARCODE)throw a.default.getNotFoundInstance();n.setValue(i)}else o[0]!==i&&n.setValue(i)},t.createDecoderResult=function(e){var r=t.createBarcodeMatrix(e);t.adjustCodewordCount(e,r);for(var n=new Array,o=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],a=new Array,u=0;u<e.getBarcodeRowCount();u++)for(var f=0;f<e.getBarcodeColumnCount();f++){var l=r[u][f+1].getValue(),d=u*e.getBarcodeColumnCount()+f;0===l.length?n.push(d):1===l.length?o[d]=l[0]:(a.push(d),i.push(l))}for(var c=new Array(i.length),h=0;h<c.length;h++)c[h]=i[h];return t.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),o,s.default.toIntArray(n),s.default.toIntArray(a),c)},t.createDecoderResultFromAmbiguousValues=function(e,r,n,i,a){for(var u=new Int32Array(i.length),s=100;s-- >0;){for(var f=0;f<u.length;f++)r[i[f]]=a[f][u[f]];try{return t.decodeCodewords(r,e,n)}catch(l){if(!(l instanceof o.default))throw l}if(0===u.length)throw o.default.getChecksumInstance();for(f=0;f<u.length;f++){if(u[f]<a[f].length-1){u[f]++;break}if(u[f]=0,f===u.length-1)throw o.default.getChecksumInstance()}}throw o.default.getChecksumInstance()},t.createBarcodeMatrix=function(t){for(var e,r,o,i,a=Array.from({length:t.getBarcodeRowCount()},(function(){return new Array(t.getBarcodeColumnCount()+2)})),u=0;u<a.length;u++)for(var s=0;s<a[u].length;s++)a[u][s]=new _.default;var f=0;try{for(var l=n(t.getDetectionResultColumns()),d=l.next();!d.done;d=l.next()){var c=d.value;if(null!=c)try{for(var h=n(c.getCodewords()),p=h.next();!p.done;p=h.next()){var g=p.value;if(null!=g){var v=g.getRowNumber();if(v>=0){if(v>=a.length)continue;a[v][f].setValue(g.getValue())}}}}catch(w){o={error:w}}finally{try{p&&!p.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}f++}}catch(y){e={error:y}}finally{try{d&&!d.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}return a},t.isValidBarcodeColumn=function(t,e){return e>=0&&e<=t.getBarcodeColumnCount()+1},t.getStartColumn=function(e,r,o,i){var a,u,s=i?1:-1,f=null;if(t.isValidBarcodeColumn(e,r-s)&&(f=e.getDetectionResultColumn(r-s).getCodeword(o)),null!=f)return i?f.getEndX():f.getStartX();if(null!=(f=e.getDetectionResultColumn(r).getCodewordNearby(o)))return i?f.getStartX():f.getEndX();if(t.isValidBarcodeColumn(e,r-s)&&(f=e.getDetectionResultColumn(r-s).getCodewordNearby(o)),null!=f)return i?f.getEndX():f.getStartX();for(var l=0;t.isValidBarcodeColumn(e,r-s);){r-=s;try{for(var d=n(e.getDetectionResultColumn(r).getCodewords()),c=d.next();!c.done;c=d.next()){var h=c.value;if(null!=h)return(i?h.getEndX():h.getStartX())+s*l*(h.getEndX()-h.getStartX())}}catch(p){a={error:p}}finally{try{c&&!c.done&&(u=d.return)&&u.call(d)}finally{if(a)throw a.error}}l++}return i?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},t.detectCodeword=function(e,r,n,o,i,a,f,l){i=t.adjustCodewordStartColumn(e,r,n,o,i,a);var d,c=t.getModuleBitCount(e,r,n,o,i,a);if(null==c)return null;var h=u.default.sum(c);if(o)d=i+h;else{for(var _=0;_<c.length/2;_++){var v=c[_];c[_]=c[c.length-1-_],c[c.length-1-_]=v}i=(d=i)-h}if(!t.checkCodewordSkew(h,f,l))return null;var w=g.default.getDecodedValue(c),y=s.default.getCodeword(w);return-1===y?null:new p.default(i,d,t.getCodewordBucketNumber(w),y)},t.getModuleBitCount=function(t,e,r,n,o,i){for(var a=o,u=new Int32Array(8),s=0,f=n?1:-1,l=n;(n?a<r:a>=e)&&s<u.length;)t.get(a,i)===l?(u[s]++,a+=f):(s++,l=!l);return s===u.length||a===(n?r:e)&&s===u.length-1?u:null},t.getNumberOfECCodeWords=function(t){return 2<<t},t.adjustCodewordStartColumn=function(e,r,n,o,i,a){for(var u=i,s=o?-1:1,f=0;f<2;f++){for(;(o?u>=r:u<n)&&o===e.get(u,a);){if(Math.abs(i-u)>t.CODEWORD_SKEW_SIZE)return i;u+=s}s=-s,o=!o}return u},t.checkCodewordSkew=function(e,r,n){return r-t.CODEWORD_SKEW_SIZE<=e&&e<=n+t.CODEWORD_SKEW_SIZE},t.decodeCodewords=function(e,r,n){if(0===e.length)throw i.default.getFormatInstance();var o=1<<r+1,a=t.correctErrors(e,n,o);t.verifyCodewordCount(e,o);var u=v.default.decode(e,""+r);return u.setErrorsCorrected(a),u.setErasures(n.length),u},t.correctErrors=function(e,r,n){if(null!=r&&r.length>n/2+t.MAX_ERRORS||n<0||n>t.MAX_EC_CODEWORDS)throw o.default.getChecksumInstance();return t.errorCorrection.decode(e,n,r)},t.verifyCodewordCount=function(t,e){if(t.length<4)throw i.default.getFormatInstance();var r=t[0];if(r>t.length)throw i.default.getFormatInstance();if(0===r){if(!(e<t.length))throw i.default.getFormatInstance();t[0]=t.length-e}},t.getBitCountForCodeword=function(t){for(var e=new Int32Array(8),r=0,n=e.length-1;!((1&t)!==r&&(r=1&t,--n<0));)e[n]++,t>>=1;return e},t.getCodewordBucketNumber=function(t){return t instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(t):this.getCodewordBucketNumber_number(t)},t.getCodewordBucketNumber_number=function(e){return t.getCodewordBucketNumber(t.getBitCountForCodeword(e))},t.getCodewordBucketNumber_Int32Array=function(t){return(t[0]-t[2]+t[4]-t[6]+9)%9},t.toString=function(t){for(var e=new w.default,r=0;r<t.length;r++){e.format("Row %2d: ",r);for(var n=0;n<t[r].length;n++){var o=t[r][n];0===o.getValue().length?e.format("        ",null):e.format("%4d(%2d)",o.getValue()[0],o.getConfidence(o.getValue()[0]))}e.format("%n")}return e.toString()},t.CODEWORD_SKEW_SIZE=2,t.MAX_ERRORS=3,t.MAX_EC_CODEWORDS=512,t.errorCorrection=new f.default,t}();e.default=y},1300:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(824),i=r(1175),a=r(1301),u=function(){function t(){this.field=a.default.PDF417_GF}return t.prototype.decode=function(t,e,r){for(var a,u,s=new i.default(this.field,t),f=new Int32Array(e),l=!1,d=e;d>0;d--){var c=s.evaluateAt(this.field.exp(d));f[e-d]=c,0!==c&&(l=!0)}if(!l)return 0;var h=this.field.getOne();if(null!=r)try{for(var p=n(r),_=p.next();!_.done;_=p.next()){var g=_.value,v=this.field.exp(t.length-1-g),w=new i.default(this.field,new Int32Array([this.field.subtract(0,v),1]));h=h.multiply(w)}}catch(O){a={error:O}}finally{try{_&&!_.done&&(u=p.return)&&u.call(p)}finally{if(a)throw a.error}}var y=new i.default(this.field,f),E=this.runEuclideanAlgorithm(this.field.buildMonomial(e,1),y,e),A=E[0],C=E[1],m=this.findErrorLocations(A),I=this.findErrorMagnitudes(C,A,m);for(d=0;d<m.length;d++){var S=t.length-1-this.field.log(m[d]);if(S<0)throw o.default.getChecksumInstance();t[S]=this.field.subtract(t[S],I[d])}return m.length},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var n=t;t=e,e=n}for(var i=t,a=e,u=this.field.getZero(),s=this.field.getOne();a.getDegree()>=Math.round(r/2);){var f=i,l=u;if(u=s,(i=a).isZero())throw o.default.getChecksumInstance();a=f;for(var d=this.field.getZero(),c=i.getCoefficient(i.getDegree()),h=this.field.inverse(c);a.getDegree()>=i.getDegree()&&!a.isZero();){var p=a.getDegree()-i.getDegree(),_=this.field.multiply(a.getCoefficient(a.getDegree()),h);d=d.add(this.field.buildMonomial(p,_)),a=a.subtract(i.multiplyByMonomial(p,_))}s=d.multiply(u).subtract(l).negative()}var g=s.getCoefficient(0);if(0===g)throw o.default.getChecksumInstance();var v=this.field.inverse(g);return[s.multiply(v),a.multiply(v)]},t.prototype.findErrorLocations=function(t){for(var e=t.getDegree(),r=new Int32Array(e),n=0,i=1;i<this.field.getSize()&&n<e;i++)0===t.evaluateAt(i)&&(r[n]=this.field.inverse(i),n++);if(n!==e)throw o.default.getChecksumInstance();return r},t.prototype.findErrorMagnitudes=function(t,e,r){for(var n=e.getDegree(),o=new Int32Array(n),a=1;a<=n;a++)o[n-a]=this.field.multiply(a,e.getCoefficient(a));var u=new i.default(this.field,o),s=r.length,f=new Int32Array(s);for(a=0;a<s;a++){var l=this.field.inverse(r[a]),d=this.field.subtract(0,t.evaluateAt(l)),c=this.field.inverse(u.evaluateAt(l));f[a]=this.field.multiply(d,c)}return f},t}();e.default=u},1301:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1024),i=r(1175),a=r(654),u=function(t){function e(e,r){var n=t.call(this)||this;n.modulus=e,n.expTable=new Int32Array(e),n.logTable=new Int32Array(e);for(var o=1,a=0;a<e;a++)n.expTable[a]=o,o=o*r%e;for(a=0;a<e-1;a++)n.logTable[n.expTable[a]]=a;return n.zero=new i.default(n,new Int32Array([0])),n.one=new i.default(n,new Int32Array([1])),n}return n(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new a.default;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new i.default(this,r)},e.PDF417_GF=new e(o.default.NUMBER_OF_CODEWORDS,3),e}(r(1302).default);e.default=u},1302:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(654),o=r(1101),i=function(){function t(){}return t.prototype.add=function(t,e){return(t+e)%this.modulus},t.prototype.subtract=function(t,e){return(this.modulus+t-e)%this.modulus},t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new n.default;return this.logTable[t]},t.prototype.inverse=function(t){if(0===t)throw new o.default;return this.expTable[this.modulus-this.logTable[t]-1]},t.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.modulus-1)]},t.prototype.getSize=function(){return this.modulus},t.prototype.equals=function(t){return t===this},t}();e.default=i},1303:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(1024),a=r(1304),u=r(1177),s=r(1178),f=function(t){function e(e,r){var n=t.call(this,e)||this;return n._isLeft=r,n}return n(e,t),e.prototype.setRowNumbers=function(){var t,e;try{for(var r=o(this.getCodewords()),n=r.next();!n.done;n=r.next()){var i=n.value;null!=i&&i.setRowNumberAsRowIndicatorColumn()}}catch(a){t={error:a}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var e=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(e,t);for(var r=this.getBoundingBox(),n=this._isLeft?r.getTopLeft():r.getTopRight(),o=this._isLeft?r.getBottomLeft():r.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.imageRowToCodewordIndex(Math.trunc(o.getY())),u=-1,s=1,f=0,l=i;l<a;l++)if(null!=e[l]){var d=e[l],c=d.getRowNumber()-u;if(0===c)f++;else if(1===c)s=Math.max(s,f),f=1,u=d.getRowNumber();else if(c<0||d.getRowNumber()>=t.getRowCount()||c>l)e[l]=null;else{for(var h=void 0,p=(h=s>2?(s-2)*c:c)>=l,_=1;_<=h&&!p;_++)p=null!=e[l-_];p?e[l]=null:(u=d.getRowNumber(),f=1)}}},e.prototype.getRowHeights=function(){var t,e,r=this.getBarcodeMetadata();if(null==r)return null;this.adjustIncompleteIndicatorColumnRowNumbers(r);var n=new Int32Array(r.getRowCount());try{for(var i=o(this.getCodewords()),a=i.next();!a.done;a=i.next()){var u=a.value;if(null!=u){var s=u.getRowNumber();if(s>=n.length)continue;n[s]++}}}catch(f){t={error:f}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return n},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var e=this.getBoundingBox(),r=this._isLeft?e.getTopLeft():e.getTopRight(),n=this._isLeft?e.getBottomLeft():e.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(r.getY())),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.getCodewords(),u=-1,s=1,f=0,l=o;l<i;l++)if(null!=a[l]){var d=a[l];d.setRowNumberAsRowIndicatorColumn();var c=d.getRowNumber()-u;0===c?f++:1===c?(s=Math.max(s,f),f=1,u=d.getRowNumber()):d.getRowNumber()>=t.getRowCount()?a[l]=null:(u=d.getRowNumber(),f=1)}},e.prototype.getBarcodeMetadata=function(){var t,e,r=this.getCodewords(),n=new s.default,u=new s.default,f=new s.default,l=new s.default;try{for(var d=o(r),c=d.next();!c.done;c=d.next()){var h=c.value;if(null!=h){h.setRowNumberAsRowIndicatorColumn();var p=h.getValue()%30,_=h.getRowNumber();switch(this._isLeft||(_+=2),_%3){case 0:u.setValue(3*p+1);break;case 1:l.setValue(p/3),f.setValue(p%3);break;case 2:n.setValue(p+1)}}}}catch(v){t={error:v}}finally{try{c&&!c.done&&(e=d.return)&&e.call(d)}finally{if(t)throw t.error}}if(0===n.getValue().length||0===u.getValue().length||0===f.getValue().length||0===l.getValue().length||n.getValue()[0]<1||u.getValue()[0]+f.getValue()[0]<i.default.MIN_ROWS_IN_BARCODE||u.getValue()[0]+f.getValue()[0]>i.default.MAX_ROWS_IN_BARCODE)return null;var g=new a.default(n.getValue()[0],u.getValue()[0],f.getValue()[0],l.getValue()[0]);return this.removeIncorrectCodewords(r,g),g},e.prototype.removeIncorrectCodewords=function(t,e){for(var r=0;r<t.length;r++){var n=t[r];if(null!=t[r]){var o=n.getValue()%30,i=n.getRowNumber();if(i>e.getRowCount())t[r]=null;else switch(this._isLeft||(i+=2),i%3){case 0:3*o+1!==e.getRowCountUpperPart()&&(t[r]=null);break;case 1:Math.trunc(o/3)===e.getErrorCorrectionLevel()&&o%3===e.getRowCountLowerPart()||(t[r]=null);break;case 2:o+1!==e.getColumnCount()&&(t[r]=null)}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+"\n"+t.prototype.toString.call(this)},e}(u.default);e.default=f},1304:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r,n){this.columnCount=t,this.errorCorrectionLevel=n,this.rowCountUpperPart=e,this.rowCountLowerPart=r,this.rowCount=e+r}return t.prototype.getColumnCount=function(){return this.columnCount},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getRowCount=function(){return this.rowCount},t.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},t.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},t}();e.default=n},1305:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1024),i=r(1110),a=function(){function t(t,e){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=t,this.barcodeColumnCount=t.getColumnCount(),this.boundingBox=e,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}return t.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var t,e=o.default.MAX_CODEWORDS_IN_BARCODE;do{t=e,e=this.adjustRowNumbersAndGetCount()}while(e>0&&e<t);return this.detectionResultColumns},t.prototype.adjustIndicatorColumnRowNumbers=function(t){null!=t&&t.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},t.prototype.adjustRowNumbersAndGetCount=function(){var t=this.adjustRowNumbersByRow();if(0===t)return 0;for(var e=1;e<this.barcodeColumnCount+1;e++)for(var r=this.detectionResultColumns[e].getCodewords(),n=0;n<r.length;n++)null!=r[n]&&(r[n].hasValidRowNumber()||this.adjustRowNumbers(e,n,r));return t},t.prototype.adjustRowNumbersByRow=function(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()},t.prototype.adjustRowNumbersFromBothRI=function(){if(null!=this.detectionResultColumns[0]&&null!=this.detectionResultColumns[this.barcodeColumnCount+1])for(var t=this.detectionResultColumns[0].getCodewords(),e=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),r=0;r<t.length;r++)if(null!=t[r]&&null!=e[r]&&t[r].getRowNumber()===e[r].getRowNumber())for(var n=1;n<=this.barcodeColumnCount;n++){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o&&(o.setRowNumber(t[r].getRowNumber()),o.hasValidRowNumber()||(this.detectionResultColumns[n].getCodewords()[r]=null))}},t.prototype.adjustRowNumbersFromRRI=function(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;for(var e=0,r=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=this.barcodeColumnCount+1;a>0&&i<this.ADJUST_ROW_NUMBER_SKIP;a--){var u=this.detectionResultColumns[a].getCodewords()[n];null!=u&&(i=t.adjustRowNumberIfValid(o,i,u),u.hasValidRowNumber()||e++)}return e},t.prototype.adjustRowNumbersFromLRI=function(){if(null==this.detectionResultColumns[0])return 0;for(var e=0,r=this.detectionResultColumns[0].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=1;a<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;a++){var u=this.detectionResultColumns[a].getCodewords()[n];null!=u&&(i=t.adjustRowNumberIfValid(o,i,u),u.hasValidRowNumber()||e++)}return e},t.adjustRowNumberIfValid=function(t,e,r){return null==r||r.hasValidRowNumber()||(r.isValidRowNumber(t)?(r.setRowNumber(t),e=0):++e),e},t.prototype.adjustRowNumbers=function(e,r,o){var i,a,u=o[r],s=this.detectionResultColumns[e-1].getCodewords(),f=s;null!=this.detectionResultColumns[e+1]&&(f=this.detectionResultColumns[e+1].getCodewords());var l=new Array(14);l[2]=s[r],l[3]=f[r],r>0&&(l[0]=o[r-1],l[4]=s[r-1],l[5]=f[r-1]),r>1&&(l[8]=o[r-2],l[10]=s[r-2],l[11]=f[r-2]),r<o.length-1&&(l[1]=o[r+1],l[6]=s[r+1],l[7]=f[r+1]),r<o.length-2&&(l[9]=o[r+2],l[12]=s[r+2],l[13]=f[r+2]);try{for(var d=n(l),c=d.next();!c.done;c=d.next()){var h=c.value;if(t.adjustRowNumber(u,h))return}}catch(p){i={error:p}}finally{try{c&&!c.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}},t.adjustRowNumber=function(t,e){return null!=e&&(!(!e.hasValidRowNumber()||e.getBucket()!==t.getBucket())&&(t.setRowNumber(e.getRowNumber()),!0))},t.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},t.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},t.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},t.prototype.setBoundingBox=function(t){this.boundingBox=t},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.setDetectionResultColumn=function(t,e){this.detectionResultColumns[t]=e},t.prototype.getDetectionResultColumn=function(t){return this.detectionResultColumns[t]},t.prototype.toString=function(){var t=this.detectionResultColumns[0];null==t&&(t=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var e=new i.default,r=0;r<t.getCodewords().length;r++){e.format("CW %3d:",r);for(var n=0;n<this.barcodeColumnCount+2;n++)if(null!=this.detectionResultColumns[n]){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o?e.format(" %3d|%3d",o.getRowNumber(),o.getValue()):e.format("    |   ")}else e.format("    |   ");e.format("%n")}return e.toString()},t}();e.default=a},1306:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(e,r,n,o){this.rowNumber=t.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(r),this.bucket=Math.trunc(n),this.value=Math.trunc(o)}return t.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},t.prototype.isValidRowNumber=function(e){return e!==t.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},t.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))},t.prototype.getWidth=function(){return this.endX-this.startX},t.prototype.getStartX=function(){return this.startX},t.prototype.getEndX=function(){return this.endX},t.prototype.getBucket=function(){return this.bucket},t.prototype.getValue=function(){return this.value},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.setRowNumber=function(t){this.rowNumber=t},t.prototype.toString=function(){return this.rowNumber+"|"+this.value},t.BARCODE_ROW_UNKNOWN=-1,t}();e.default=n},1307:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(820),o=r(1024),i=r(1158),a=function(){function t(){}return t.initialize=function(){for(var e=0;e<o.default.SYMBOL_TABLE.length;e++)for(var r=o.default.SYMBOL_TABLE[e],n=1&r,i=0;i<o.default.BARS_IN_MODULE;i++){for(var a=0;(1&r)===n;)a+=1,r>>=1;n=1&r,t.RATIOS_TABLE[e]||(t.RATIOS_TABLE[e]=new Array(o.default.BARS_IN_MODULE)),t.RATIOS_TABLE[e][o.default.BARS_IN_MODULE-i-1]=Math.fround(a/o.default.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},t.getDecodedValue=function(e){var r=t.getDecodedCodewordValue(t.sampleBitCounts(e));return-1!==r?r:t.getClosestDecodedValue(e)},t.sampleBitCounts=function(t){for(var e=n.default.sum(t),r=new Int32Array(o.default.BARS_IN_MODULE),i=0,a=0,u=0;u<o.default.MODULES_IN_CODEWORD;u++){var s=e/(2*o.default.MODULES_IN_CODEWORD)+u*e/o.default.MODULES_IN_CODEWORD;a+t[i]<=s&&(a+=t[i],i++),r[i]++}return r},t.getDecodedCodewordValue=function(e){var r=t.getBitValue(e);return-1===o.default.getCodeword(r)?-1:r},t.getBitValue=function(t){for(var e=0,r=0;r<t.length;r++)for(var n=0;n<t[r];n++)e=e<<1|(r%2===0?1:0);return Math.trunc(e)},t.getClosestDecodedValue=function(e){var r=n.default.sum(e),a=new Array(o.default.BARS_IN_MODULE);if(r>1)for(var u=0;u<a.length;u++)a[u]=Math.fround(e[u]/r);var s=i.default.MAX_VALUE,f=-1;this.bSymbolTableReady||t.initialize();for(var l=0;l<t.RATIOS_TABLE.length;l++){for(var d=0,c=t.RATIOS_TABLE[l],h=0;h<o.default.BARS_IN_MODULE;h++){var p=Math.fround(c[h]-a[h]);if((d+=Math.fround(p*p))>=s)break}d<s&&(s=d,f=o.default.SYMBOL_TABLE[l])}return f},t.bSymbolTableReady=!1,t.RATIOS_TABLE=new Array(o.default.SYMBOL_TABLE.length).map((function(t){return new Array(o.default.BARS_IN_MODULE)})),t}();e.default=a},1308:function(t,e,r){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0});var n,o,i=r(692),a=r(1032),u=r(1054),s=r(1309),f=r(1022),l=r(749),d=r(898),c=r(1310),h=r(1311),p=r(1051);function _(){if("undefined"!==typeof window)return window.BigInt||null;if("undefined"!==typeof t)return t.BigInt||null;throw new Error("Can't search globals for BigInt!")}function g(t){if("undefined"===typeof o&&(o=_()),null===o)throw new Error("BigInt is not supported!");return o(t)}!function(t){t[t.ALPHA=0]="ALPHA",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.PUNCT=3]="PUNCT",t[t.ALPHA_SHIFT=4]="ALPHA_SHIFT",t[t.PUNCT_SHIFT=5]="PUNCT_SHIFT"}(n||(n={}));var v=function(){function t(){}return t.decode=function(e,r){var n=new l.default(""),o=a.default.ISO8859_1;n.enableDecoding(o);for(var f=1,d=e[f++],c=new s.default;f<e[0];){switch(d){case t.TEXT_COMPACTION_MODE_LATCH:f=t.textCompaction(e,f,n);break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:f=t.byteCompaction(d,e,o,f,n);break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[f++]);break;case t.NUMERIC_COMPACTION_MODE_LATCH:f=t.numericCompaction(e,f,n);break;case t.ECI_CHARSET:a.default.getCharacterSetECIByValue(e[f++]);break;case t.ECI_GENERAL_PURPOSE:f+=2;break;case t.ECI_USER_DEFINED:f++;break;case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:f=t.decodeMacroBlock(e,f,c);break;case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:throw new i.default;default:f--,f=t.textCompaction(e,f,n)}if(!(f<e.length))throw i.default.getFormatInstance();d=e[f++]}if(0===n.length())throw i.default.getFormatInstance();var h=new u.default(null,n.toString(),null,r);return h.setOther(c),h},t.decodeMacroBlock=function(e,r,n){if(r+t.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw i.default.getFormatInstance();for(var o=new Int32Array(t.NUMBER_OF_SEQUENCE_CODEWORDS),a=0;a<t.NUMBER_OF_SEQUENCE_CODEWORDS;a++,r++)o[a]=e[r];n.setSegmentIndex(d.default.parseInt(t.decodeBase900toBase10(o,t.NUMBER_OF_SEQUENCE_CODEWORDS)));var u=new l.default;r=t.textCompaction(e,r,u),n.setFileId(u.toString());var s=-1;for(e[r]===t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=r+1);r<e[0];)switch(e[r]){case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++r]){case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var h=new l.default;r=t.textCompaction(e,r+1,h),n.setFileName(h.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var p=new l.default;r=t.textCompaction(e,r+1,p),n.setSender(p.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var _=new l.default;r=t.textCompaction(e,r+1,_),n.setAddressee(_.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var g=new l.default;r=t.numericCompaction(e,r+1,g),n.setSegmentCount(d.default.parseInt(g.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var v=new l.default;r=t.numericCompaction(e,r+1,v),n.setTimestamp(c.default.parseLong(v.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var w=new l.default;r=t.numericCompaction(e,r+1,w),n.setChecksum(d.default.parseInt(w.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var y=new l.default;r=t.numericCompaction(e,r+1,y),n.setFileSize(c.default.parseLong(y.toString()));break;default:throw i.default.getFormatInstance()}break;case t.MACRO_PDF417_TERMINATOR:r++,n.setLastSegment(!0);break;default:throw i.default.getFormatInstance()}if(-1!==s){var E=r-s;n.isLastSegment()&&E--,n.setOptionalData(f.default.copyOfRange(e,s,s+E))}return r},t.textCompaction=function(e,r,n){for(var o=new Int32Array(2*(e[0]-r)),i=new Int32Array(2*(e[0]-r)),a=0,u=!1;r<e[0]&&!u;){var s=e[r++];if(s<t.TEXT_COMPACTION_MODE_LATCH)o[a]=s/30,o[a+1]=s%30,a+=2;else switch(s){case t.TEXT_COMPACTION_MODE_LATCH:o[a++]=t.TEXT_COMPACTION_MODE_LATCH;break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,u=!0;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o[a]=t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,s=e[r++],i[a]=s,a++}}return t.decodeTextCompaction(o,i,a,n),r},t.decodeTextCompaction=function(e,r,o,i){for(var a=n.ALPHA,u=n.ALPHA,s=0;s<o;){var f=e[s],l="";switch(a){case n.ALPHA:if(f<26)l=String.fromCharCode(65+f);else switch(f){case 26:l=" ";break;case t.LL:a=n.LOWER;break;case t.ML:a=n.MIXED;break;case t.PS:u=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.LOWER:if(f<26)l=String.fromCharCode(97+f);else switch(f){case 26:l=" ";break;case t.AS:u=a,a=n.ALPHA_SHIFT;break;case t.ML:a=n.MIXED;break;case t.PS:u=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.MIXED:if(f<t.PL)l=t.MIXED_CHARS[f];else switch(f){case t.PL:a=n.PUNCT;break;case 26:l=" ";break;case t.LL:a=n.LOWER;break;case t.AL:a=n.ALPHA;break;case t.PS:u=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.PUNCT:if(f<t.PAL)l=t.PUNCT_CHARS[f];else switch(f){case t.PAL:a=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.ALPHA_SHIFT:if(a=u,f<26)l=String.fromCharCode(65+f);else switch(f){case 26:l=" ";break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.PUNCT_SHIFT:if(a=u,f<t.PAL)l=t.PUNCT_CHARS[f];else switch(f){case t.PAL:a=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}}""!==l&&i.append(l),s++}},t.byteCompaction=function(e,r,n,o,i){var a=new h.default,u=0,s=0,f=!1;switch(e){case t.BYTE_COMPACTION_MODE_LATCH:for(var l=new Int32Array(6),d=r[o++];o<r[0]&&!f;)switch(l[u++]=d,s=900*s+d,d=r[o++]){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,f=!0;break;default:if(u%5===0&&u>0){for(var c=0;c<6;++c)a.write(Number(g(s)>>g(8*(5-c))));s=0,u=0}}o===r[0]&&d<t.TEXT_COMPACTION_MODE_LATCH&&(l[u++]=d);for(var _=0;_<u;_++)a.write(l[_]);break;case t.BYTE_COMPACTION_MODE_LATCH_6:for(;o<r[0]&&!f;){var v=r[o++];if(v<t.TEXT_COMPACTION_MODE_LATCH)u++,s=900*s+v;else switch(v){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,f=!0}if(u%5===0&&u>0){for(c=0;c<6;++c)a.write(Number(g(s)>>g(8*(5-c))));s=0,u=0}}}return i.append(p.default.decode(a.toByteArray(),n)),o},t.numericCompaction=function(e,r,n){for(var o=0,i=!1,a=new Int32Array(t.MAX_NUMERIC_CODEWORDS);r<e[0]&&!i;){var u=e[r++];if(r===e[0]&&(i=!0),u<t.TEXT_COMPACTION_MODE_LATCH)a[o]=u,o++;else switch(u){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,i=!0}(o%t.MAX_NUMERIC_CODEWORDS===0||u===t.NUMERIC_COMPACTION_MODE_LATCH||i)&&o>0&&(n.append(t.decodeBase900toBase10(a,o)),o=0)}return r},t.decodeBase900toBase10=function(e,r){for(var n=g(0),o=0;o<r;o++)n+=t.EXP900[r-o-1]*g(e[o]);var a=n.toString();if("1"!==a.charAt(0))throw new i.default;return a.substring(1)},t.TEXT_COMPACTION_MODE_LATCH=900,t.BYTE_COMPACTION_MODE_LATCH=901,t.NUMERIC_COMPACTION_MODE_LATCH=902,t.BYTE_COMPACTION_MODE_LATCH_6=924,t.ECI_USER_DEFINED=925,t.ECI_GENERAL_PURPOSE=926,t.ECI_CHARSET=927,t.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,t.MACRO_PDF417_TERMINATOR=922,t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,t.MAX_NUMERIC_CODEWORDS=15,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,t.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,t.PL=25,t.LL=27,t.AS=27,t.ML=28,t.AL=28,t.PS=29,t.PAL=29,t.PUNCT_CHARS=";<>@[\\]_`~!\r\t,:\n-.$/\"|*()?{}'",t.MIXED_CHARS="0123456789&\r\t,:#-.$/+%*=^",t.EXP900=_()?function(){var t=[];t[0]=g(1);var e=g(900);t[1]=e;for(var r=2;r<16;r++)t[r]=t[r-1]*e;return t}():[],t.NUMBER_OF_SEQUENCE_CODEWORDS=2,t}();e.default=v}).call(this,r(28))},1309:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return t.prototype.getSegmentIndex=function(){return this.segmentIndex},t.prototype.setSegmentIndex=function(t){this.segmentIndex=t},t.prototype.getFileId=function(){return this.fileId},t.prototype.setFileId=function(t){this.fileId=t},t.prototype.getOptionalData=function(){return this.optionalData},t.prototype.setOptionalData=function(t){this.optionalData=t},t.prototype.isLastSegment=function(){return this.lastSegment},t.prototype.setLastSegment=function(t){this.lastSegment=t},t.prototype.getSegmentCount=function(){return this.segmentCount},t.prototype.setSegmentCount=function(t){this.segmentCount=t},t.prototype.getSender=function(){return this.sender||null},t.prototype.setSender=function(t){this.sender=t},t.prototype.getAddressee=function(){return this.addressee||null},t.prototype.setAddressee=function(t){this.addressee=t},t.prototype.getFileName=function(){return this.fileName},t.prototype.setFileName=function(t){this.fileName=t},t.prototype.getFileSize=function(){return this.fileSize},t.prototype.setFileSize=function(t){this.fileSize=t},t.prototype.getChecksum=function(){return this.checksum},t.prototype.setChecksum=function(t){this.checksum=t},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.setTimestamp=function(t){this.timestamp=t},t}();e.default=n},1310:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.parseLong=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t}();e.default=n},1311:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1022),i=r(1312),a=r(898),u=r(654),s=r(1314),f=r(752),l=r(1098),d=function(t){function e(e){void 0===e&&(e=32);var r=t.call(this)||this;if(r.count=0,e<0)throw new u.default("Negative initial size: "+e);return r.buf=new Uint8Array(e),r}return n(e,t),e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var e=this.buf.length<<1;if(e-t<0&&(e=t),e<0){if(t<0)throw new s.default;e=a.default.MAX_VALUE}this.buf=o.default.copyOfUint8Array(this.buf,e)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,e,r){if(e<0||e>t.length||r<0||e+r-t.length>0)throw new l.default;this.ensureCapacity(this.count+r),f.default.arraycopy(t,e,this.buf,this.count,r),this.count+=r},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return o.default.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?"string"===typeof t?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e}(i.default);e.default=d},1312:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1098),o=r(1313),i=function(){function t(){}return t.prototype.writeBytes=function(t){this.writeBytesOffset(t,0,t.length)},t.prototype.writeBytesOffset=function(t,e,r){if(null==t)throw new o.default;if(e<0||e>t.length||r<0||e+r>t.length||e+r<0)throw new n.default;if(0!==r)for(var i=0;i<r;i++)this.write(t[e+i])},t.prototype.flush=function(){},t.prototype.close=function(){},t}();e.default=i},1313:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1314:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},1315:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1174),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new i.default,e)||this}return n(e,t),e}(o.BrowserCodeReader);e.BrowserPDF417Reader=a},1316:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1020),i=r(1108),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new i.default,e)||this}return n(e,t),e}(o.BrowserCodeReader);e.BrowserQRCodeReader=a},1317:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1075),o=r(1111),i=r(1074),a=r(654),u=r(904),s=function(){function t(){}return t.prototype.write=function(e,r,u,s){if(void 0===s&&(s=null),0===e.length)throw new a.default("Found empty contents");if(r<0||u<0)throw new a.default("Requested dimensions are too small: "+r+"x"+u);var f=i.default.L,l=t.QUIET_ZONE_SIZE;null!==s&&(void 0!==s.get(n.default.ERROR_CORRECTION)&&(f=i.default.fromString(s.get(n.default.ERROR_CORRECTION).toString())),void 0!==s.get(n.default.MARGIN)&&(l=Number.parseInt(s.get(n.default.MARGIN).toString(),10)));var d=o.default.encode(e,f,s);return this.renderResult(d,r,u,l)},t.prototype.writeToDom=function(t,e,r,n,o){void 0===o&&(o=null),"string"===typeof t&&(t=document.querySelector(t));var i=this.write(e,r,n,o);t&&t.appendChild(i)},t.prototype.renderResult=function(t,e,r,n){var o=t.getMatrix();if(null===o)throw new u.default;for(var i=o.getWidth(),a=o.getHeight(),s=i+2*n,f=a+2*n,l=Math.max(e,s),d=Math.max(r,f),c=Math.min(Math.floor(l/s),Math.floor(d/f)),h=Math.floor((l-i*c)/2),p=Math.floor((d-a*c)/2),_=this.createSVGElement(l,d),g=0,v=p;g<a;g++,v+=c)for(var w=0,y=h;w<i;w++,y+=c)if(1===o.get(w,g)){var E=this.createSvgRectElement(y,v,c,c);_.appendChild(E)}return _},t.prototype.createSVGElement=function(e,r){var n=document.createElementNS(t.SVG_NS,"svg");return n.setAttributeNS(null,"height",e.toString()),n.setAttributeNS(null,"width",r.toString()),n},t.prototype.createSvgRectElement=function(e,r,n,o){var i=document.createElementNS(t.SVG_NS,"rect");return i.setAttributeNS(null,"x",e.toString()),i.setAttributeNS(null,"y",r.toString()),i.setAttributeNS(null,"height",n.toString()),i.setAttributeNS(null,"width",o.toString()),i.setAttributeNS(null,"fill","#000000"),i},t.QUIET_ZONE_SIZE=4,t.SVG_NS="http://www.w3.org/2000/svg",t}();e.BrowserQRCodeSvgWriter=s},1318:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1022),i=r(749),a=function(){function t(t,e){this.width=t,this.height=e;for(var r=new Array(e),n=0;n!==e;n++)r[n]=new Uint8Array(t);this.bytes=r}return t.prototype.getHeight=function(){return this.height},t.prototype.getWidth=function(){return this.width},t.prototype.get=function(t,e){return this.bytes[e][t]},t.prototype.getArray=function(){return this.bytes},t.prototype.setNumber=function(t,e,r){this.bytes[e][t]=r},t.prototype.setBoolean=function(t,e,r){this.bytes[e][t]=r?1:0},t.prototype.clear=function(t){var e,r;try{for(var i=n(this.bytes),a=i.next();!a.done;a=i.next()){var u=a.value;o.default.fill(u,t)}}catch(s){e={error:s}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;if(this.width!==r.width)return!1;if(this.height!==r.height)return!1;for(var n=0,o=this.height;n<o;++n)for(var i=this.bytes[n],a=r.bytes[n],u=0,s=this.width;u<s;++u)if(i[u]!==a[u])return!1;return!0},t.prototype.toString=function(){for(var t=new i.default,e=0,r=this.height;e<r;++e){for(var n=this.bytes[e],o=0,a=this.width;o<a;++o)switch(n[o]){case 0:t.append(" 0");break;case 1:t.append(" 1");break;default:t.append("  ")}t.append("\n")}return t.toString()},t}();e.default=a},1319:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1021),o=r(898),i=r(1112),a=r(1181),u=r(1113),s=r(654),f=function(){function t(){}return t.clearMatrix=function(t){t.clear(255)},t.buildMatrix=function(e,r,n,o,i){t.clearMatrix(i),t.embedBasicPatterns(n,i),t.embedTypeInfo(r,o,i),t.maybeEmbedVersionInfo(n,i),t.embedDataBits(e,o,i)},t.embedBasicPatterns=function(e,r){t.embedPositionDetectionPatternsAndSeparators(r),t.embedDarkDotAtLeftBottomCorner(r),t.maybeEmbedPositionAdjustmentPatterns(e,r),t.embedTimingPatterns(r)},t.embedTypeInfo=function(e,r,o){var i=new n.default;t.makeTypeInfoBits(e,r,i);for(var a=0,u=i.getSize();a<u;++a){var s=i.get(i.getSize()-1-a),f=t.TYPE_INFO_COORDINATES[a],l=f[0],d=f[1];if(o.setBoolean(l,d,s),a<8){var c=o.getWidth()-a-1,h=8;o.setBoolean(c,h,s)}else{c=8,h=o.getHeight()-7+(a-8);o.setBoolean(c,h,s)}}},t.maybeEmbedVersionInfo=function(e,r){if(!(e.getVersionNumber()<7)){var o=new n.default;t.makeVersionInfoBits(e,o);for(var i=17,a=0;a<6;++a)for(var u=0;u<3;++u){var s=o.get(i);i--,r.setBoolean(a,r.getHeight()-11+u,s),r.setBoolean(r.getHeight()-11+u,a,s)}}},t.embedDataBits=function(e,r,n){for(var o=0,i=-1,s=n.getWidth()-1,f=n.getHeight()-1;s>0;){for(6===s&&(s-=1);f>=0&&f<n.getHeight();){for(var l=0;l<2;++l){var d=s-l;if(t.isEmpty(n.get(d,f))){var c=void 0;o<e.getSize()?(c=e.get(o),++o):c=!1,255!==r&&a.default.getDataMaskBit(r,d,f)&&(c=!c),n.setBoolean(d,f,c)}}f+=i}f+=i=-i,s-=2}if(o!==e.getSize())throw new u.default("Not all bits consumed: "+o+"/"+e.getSize())},t.findMSBSet=function(t){return 32-o.default.numberOfLeadingZeros(t)},t.calculateBCHCode=function(e,r){if(0===r)throw new s.default("0 polynomial");var n=t.findMSBSet(r);for(e<<=n-1;t.findMSBSet(e)>=n;)e^=r<<t.findMSBSet(e)-n;return e},t.makeTypeInfoBits=function(e,r,o){if(!i.default.isValidMaskPattern(r))throw new u.default("Invalid mask pattern");var a=e.getBits()<<3|r;o.appendBits(a,5);var s=t.calculateBCHCode(a,t.TYPE_INFO_POLY);o.appendBits(s,10);var f=new n.default;if(f.appendBits(t.TYPE_INFO_MASK_PATTERN,15),o.xor(f),15!==o.getSize())throw new u.default("should not happen but we got: "+o.getSize())},t.makeVersionInfoBits=function(e,r){r.appendBits(e.getVersionNumber(),6);var n=t.calculateBCHCode(e.getVersionNumber(),t.VERSION_INFO_POLY);if(r.appendBits(n,12),18!==r.getSize())throw new u.default("should not happen but we got: "+r.getSize())},t.isEmpty=function(t){return 255===t},t.embedTimingPatterns=function(e){for(var r=8;r<e.getWidth()-8;++r){var n=(r+1)%2;t.isEmpty(e.get(r,6))&&e.setNumber(r,6,n),t.isEmpty(e.get(6,r))&&e.setNumber(6,r,n)}},t.embedDarkDotAtLeftBottomCorner=function(t){if(0===t.get(8,t.getHeight()-8))throw new u.default;t.setNumber(8,t.getHeight()-8,1)},t.embedHorizontalSeparationPattern=function(e,r,n){for(var o=0;o<8;++o){if(!t.isEmpty(n.get(e+o,r)))throw new u.default;n.setNumber(e+o,r,0)}},t.embedVerticalSeparationPattern=function(e,r,n){for(var o=0;o<7;++o){if(!t.isEmpty(n.get(e,r+o)))throw new u.default;n.setNumber(e,r+o,0)}},t.embedPositionAdjustmentPattern=function(e,r,n){for(var o=0;o<5;++o)for(var i=t.POSITION_ADJUSTMENT_PATTERN[o],a=0;a<5;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPattern=function(e,r,n){for(var o=0;o<7;++o)for(var i=t.POSITION_DETECTION_PATTERN[o],a=0;a<7;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPatternsAndSeparators=function(e){var r=t.POSITION_DETECTION_PATTERN[0].length;t.embedPositionDetectionPattern(0,0,e),t.embedPositionDetectionPattern(e.getWidth()-r,0,e),t.embedPositionDetectionPattern(0,e.getWidth()-r,e);t.embedHorizontalSeparationPattern(0,7,e),t.embedHorizontalSeparationPattern(e.getWidth()-8,7,e),t.embedHorizontalSeparationPattern(0,e.getWidth()-8,e);t.embedVerticalSeparationPattern(7,0,e),t.embedVerticalSeparationPattern(e.getHeight()-7-1,0,e),t.embedVerticalSeparationPattern(7,e.getHeight()-7,e)},t.maybeEmbedPositionAdjustmentPatterns=function(e,r){if(!(e.getVersionNumber()<2))for(var n=e.getVersionNumber()-1,o=t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],i=0,a=o.length;i!==a;i++){var u=o[i];if(u>=0)for(var s=0;s!==a;s++){var f=o[s];f>=0&&t.isEmpty(r.get(f,u))&&t.embedPositionAdjustmentPattern(f-2,u-2,r)}}},t.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),t.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),t.VERSION_INFO_POLY=7973,t.TYPE_INFO_POLY=1335,t.TYPE_INFO_MASK_PATTERN=21522,t}();e.default=f},1320:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){this.dataBytes=t,this.errorCorrectionBytes=e}return t.prototype.getDataBytes=function(){return this.dataBytes},t.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},t}();e.default=n},1321:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1182),o=r(693),i=r(654),a=function(){function t(){}return t.prototype.encode=function(t,e,r,a,u){if(e!==o.default.QR_CODE)throw new i.default("No encoder available for format "+e);return(new n.default).encode(t,e,r,a,u)},t}();e.default=a},1322:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(752),i=r(1053),a=r(1052),u=r(654),s=function(t){function e(e,r,n,o,i,a,s,f){var l=t.call(this,a,s)||this;if(l.yuvData=e,l.dataWidth=r,l.dataHeight=n,l.left=o,l.top=i,o+a>r||i+s>n)throw new u.default("Crop rectangle does not fit within image data.");return f&&l.reverseHorizontal(a,s),l}return n(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new u.default("Requested row is outside the image: "+t);var r=this.getWidth();(null===e||void 0===e||e.length<r)&&(e=new Uint8ClampedArray(r));var n=(t+this.top)*this.dataWidth+this.left;return o.default.arraycopy(this.yuvData,n,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.yuvData;var r=t*e,n=new Uint8ClampedArray(r),i=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return o.default.arraycopy(this.yuvData,i,n,0,r),n;for(var a=0;a<e;a++){var u=a*t;o.default.arraycopy(this.yuvData,i,n,u,t),i+=this.dataWidth}return n},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+r,n,o,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,r=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,n=new Int32Array(t*r),o=this.yuvData,i=this.top*this.dataWidth+this.left,a=0;a<r;a++){for(var u=a*t,s=0;s<t;s++){var f=255&o[i+s*e.THUMBNAIL_SCALE_FACTOR];n[u+s]=4278190080|65793*f}i+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return n},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,e){for(var r=this.yuvData,n=0,o=this.top*this.dataWidth+this.left;n<e;n++,o+=this.dataWidth)for(var i=o+t/2,a=o,u=o+t-1;a<i;a++,u--){var s=r[a];r[a]=r[u],r[u]=s}},e.prototype.invert=function(){return new a.default(this)},e.THUMBNAIL_SCALE_FACTOR=2,e}(i.default);e.default=s},1323:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0}),r(1052);var o=r(1052),i=r(1053),a=r(752),u=r(654),s=function(t){function e(e,r,n,o,i,a,s){var f=t.call(this,r,n)||this;if(f.dataWidth=o,f.dataHeight=i,f.left=a,f.top=s,4===e.BYTES_PER_ELEMENT){for(var l=r*n,d=new Uint8ClampedArray(l),c=0;c<l;c++){var h=e[c],p=h>>16&255,_=h>>7&510,g=255&h;d[c]=(p+_+g)/4&255}f.luminances=d}else f.luminances=e;if(void 0===o&&(f.dataWidth=r),void 0===i&&(f.dataHeight=n),void 0===a&&(f.left=0),void 0===s&&(f.top=0),f.left+r>f.dataWidth||f.top+n>f.dataHeight)throw new u.default("Crop rectangle does not fit within image data.");return f}return n(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new u.default("Requested row is outside the image: "+t);var r=this.getWidth();(null===e||void 0===e||e.length<r)&&(e=new Uint8ClampedArray(r));var n=(t+this.top)*this.dataWidth+this.left;return a.default.arraycopy(this.luminances,n,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.luminances;var r=t*e,n=new Uint8ClampedArray(r),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return a.default.arraycopy(this.luminances,o,n,0,r),n;for(var i=0;i<e;i++){var u=i*t;a.default.arraycopy(this.luminances,o,n,u,t),o+=this.dataWidth}return n},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.luminances,n,o,this.dataWidth,this.dataHeight,this.left+t,this.top+r)},e.prototype.invert=function(){return new o.default(this)},e}(i.default);e.default=s},1324:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(693),a=r(642),u=r(791),s=r(820),f=r(1164),l=r(1105),d=r(1165),c=r(1166),h=r(1325),p=r(1338),_=r(1339),g=r(1340),v=r(752),w=function(t){function e(){var r=null!==t&&t.apply(this,arguments)||this;return r.pairs=new Array(e.MAX_PAIRS),r.rows=new Array,r.startEnd=[2],r}return n(e,t),e.prototype.decodeRow=function(t,r,n){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,r))}catch(o){console.log(o)}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,r))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,e){for(var r,n=!1;!n;)try{this.pairs.push(this.retrieveNextPair(e,this.pairs,t))}catch(i){if(!this.pairs.length)throw new i;n=!0}if(this.checkChecksum())return this.pairs;if(r=!!this.rows.length,this.storeRow(t,!1),r){var o=this.checkRowsBoolean(!1);if(null!=o)return o;if(null!=(o=this.checkRowsBoolean(!0)))return o}throw new a.default},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var e=null;try{e=this.checkRows(new Array,0)}catch(r){console.log(r)}return t&&(this.rows=this.rows.reverse()),e},e.prototype.checkRows=function(t,r){for(var n,i,u=r;u<this.rows.length;u++){var s=this.rows[u];this.pairs.length=0;try{for(var f=o(t),l=f.next();!l.done;l=f.next()){var d=l.value;this.pairs.push(d.getPairs())}}catch(h){n={error:h}}finally{try{l&&!l.done&&(i=f.return)&&i.call(f)}finally{if(n)throw n.error}}if(this.pairs.push(s.getPairs()),e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var c=new Array(t);c.push(s);try{return this.checkRows(c,u+1)}catch(p){console.log(p)}}}throw new a.default},e.isValidSequence=function(t){var r,n;try{for(var i=o(e.FINDER_PATTERN_SEQUENCES),a=i.next();!a.done;a=i.next()){var u=a.value;if(!(t.length>u.length)){for(var s=!0,f=0;f<t.length;f++)if(t[f].getFinderPattern().getValue()!=u[f]){s=!1;break}if(s)return!0}}}catch(l){r={error:l}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return!1},e.prototype.storeRow=function(t,r){for(var n=0,o=!1,i=!1;n<this.rows.length;){var a=this.rows[n];if(a.getRowNumber()>t){i=a.isEquivalent(this.pairs);break}o=a.isEquivalent(this.pairs),n++}i||o||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(n,new _.default(this.pairs,t,r)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,e){var r,n,i,a,u,s;try{for(var f=o(e),l=f.next();!l.done;l=f.next()){var d=l.value;if(d.getPairs().length!==t.length){try{for(var c=o(d.getPairs()),h=c.next();!h.done;h=c.next()){var _=h.value,g=!1;try{for(var v=o(t),w=v.next();!w.done;w=v.next()){var y=w.value;if(p.default.equals(_,y)){g=!0;break}}}catch(E){u={error:E}}finally{try{w&&!w.done&&(s=v.return)&&s.call(v)}finally{if(u)throw u.error}}g||!1}}catch(A){i={error:A}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}}}}catch(C){r={error:C}}finally{try{l&&!l.done&&(n=f.return)&&n.call(f)}finally{if(r)throw r.error}}},e.isPartialRow=function(t,e){var r,n,i,a,u,s;try{for(var f=o(e),l=f.next();!l.done;l=f.next()){var d=l.value,c=!0;try{for(var h=o(t),p=h.next();!p.done;p=h.next()){var _=p.value,g=!1;try{for(var v=o(d.getPairs()),w=v.next();!w.done;w=v.next()){var y=w.value;if(_.equals(y)){g=!0;break}}}catch(E){u={error:E}}finally{try{w&&!w.done&&(s=v.return)&&s.call(v)}finally{if(u)throw u.error}}if(!g){c=!1;break}}}catch(A){i={error:A}}finally{try{p&&!p.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}if(c)return!0}}catch(C){r={error:C}}finally{try{l&&!l.done&&(n=f.return)&&n.call(f)}finally{if(r)throw r.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var e=g.default.buildBitArray(t),r=h.createDecoder(e).parseInformation(),n=t[0].getFinderPattern().getResultPoints(),o=t[t.length-1].getFinderPattern().getResultPoints(),a=[n[0],n[1],o[0],o[1]];return new u.default(r,null,null,a,i.default.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),e=t.getLeftChar(),r=t.getRightChar();if(null==r)return!1;for(var n=r.getChecksumPortion(),o=2,i=1;i<this.pairs.size();++i){var a=this.pairs.get(i);n+=a.getLeftChar().getChecksumPortion(),o++;var u=a.getRightChar();null!=u&&(n+=u.getChecksumPortion(),o++)}return 211*(o-4)+(n%=211)==e.getValue()},e.getNextSecondBar=function(t,e){var r;return t.get(e)?(r=t.getNextUnset(e),r=t.getNextSet(r)):(r=t.getNextSet(e),r=t.getNextUnset(r)),r},e.prototype.retrieveNextPair=function(t,r,n){var o,i=r.length%2==0;this.startFromEven&&(i=!i);var u=!0,s=-1;do{this.findNextPair(t,r,s),null==(o=this.parseFoundFinderPattern(t,n,i))?s=e.getNextSecondBar(t,this.startEnd[0]):u=!1}while(u);var f,l=this.decodeDataCharacter(t,o,i,!0);if(!this.isEmptyPair(r)&&r[r.length-1].mustBeLast())throw new a.default;try{f=this.decodeDataCharacter(t,o,i,!1)}catch(d){f=null,console.log(d)}return new p.default(l,f,o,!0)},e.prototype.isEmptyPair=function(t){return 0===t.length},e.prototype.findNextPair=function(t,r,n){var o=this.getDecodeFinderCounters();o[0]=0,o[1]=0,o[2]=0,o[3]=0;var i,u=t.getSize();if(n>=0)i=n;else if(this.isEmptyPair(r))i=0;else{i=r[r.length-1].getFinderPattern().getStartEnd()[1]}var s=r.length%2!=0;this.startFromEven&&(s=!s);for(var f=!1;i<u&&(f=!t.get(i));)i++;for(var l=0,d=i,c=i;c<u;c++)if(t.get(c)!=f)o[l]++;else{if(3==l){if(s&&e.reverseCounters(o),e.isFinderPattern(o))return this.startEnd[0]=d,void(this.startEnd[1]=c);s&&e.reverseCounters(o),d+=o[0]+o[1],o[0]=o[2],o[1]=o[3],o[2]=0,o[3]=0,l--}else l++;o[l]=1,f=!f}throw new a.default},e.reverseCounters=function(t){for(var e=t.length,r=0;r<e/2;++r){var n=t[r];t[r]=t[e-r-1],t[e-r-1]=n}},e.prototype.parseFoundFinderPattern=function(t,r,n){var o,i,a;if(n){for(var u=this.startEnd[0]-1;u>=0&&!t.get(u);)u--;u++,o=this.startEnd[0]-u,i=u,a=this.startEnd[1]}else i=this.startEnd[0],o=(a=t.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];var s,f=this.getDecodeFinderCounters();v.default.arraycopy(f,0,f,1,f.length-1),f[0]=o;try{s=this.parseFinderValue(f,e.FINDER_PATTERNS)}catch(l){return null}return new d.default(s,[i,a],i,a,r)},e.prototype.decodeDataCharacter=function(t,r,n,o){for(var i=this.getDataCharacterCounters(),u=0;u<i.length;u++)i[u]=0;if(o)e.recordPatternInReverse(t,r.getStartEnd()[0],i);else{e.recordPattern(t,r.getStartEnd()[1],i);for(var f=0,d=i.length-1;f<d;f++,d--){var h=i[f];i[f]=i[d],i[d]=h}}var p=s.default.sum(new Int32Array(i))/17,_=(r.getStartEnd()[1]-r.getStartEnd()[0])/15;if(Math.abs(p-_)/_>.3)throw new a.default;var g=this.getOddCounts(),v=this.getEvenCounts(),w=this.getOddRoundingErrors(),y=this.getEvenRoundingErrors();for(f=0;f<i.length;f++){var E=1*i[f]/p,A=E+.5;if(A<1){if(E<.3)throw new a.default;A=1}else if(A>8){if(E>8.7)throw new a.default;A=8}var C=f/2;0==(1&f)?(g[C]=A,w[C]=E-A):(v[C]=A,y[C]=E-A)}this.adjustOddEvenCounts(17);var m=4*r.getValue()+(n?0:2)+(o?0:1)-1,I=0,S=0;for(f=g.length-1;f>=0;f--){if(e.isNotA1left(r,n,o)){var O=e.WEIGHTS[m][2*f];S+=g[f]*O}I+=g[f]}var T=0;for(f=v.length-1;f>=0;f--)if(e.isNotA1left(r,n,o)){O=e.WEIGHTS[m][2*f+1];T+=v[f]*O}var R=S+T;if(0!=(1&I)||I>13||I<4)throw new a.default;var b=(13-I)/2,N=e.SYMBOL_WIDEST[b],M=9-N,P=c.default.getRSSvalue(g,N,!0),D=c.default.getRSSvalue(v,M,!1),B=P*e.EVEN_TOTAL_SUBSET[b]+D+e.GSUM[b];return new l.default(B,R)},e.isNotA1left=function(t,e,r){return!(0==t.getValue()&&e&&r)},e.prototype.adjustOddEvenCounts=function(t){var r=s.default.sum(new Int32Array(this.getOddCounts())),n=s.default.sum(new Int32Array(this.getEvenCounts())),o=!1,i=!1;r>13?i=!0:r<4&&(o=!0);var u=!1,f=!1;n>13?f=!0:n<4&&(u=!0);var l=r+n-t,d=1==(1&r),c=0==(1&n);if(1==l)if(d){if(c)throw new a.default;i=!0}else{if(!c)throw new a.default;f=!0}else if(-1==l)if(d){if(c)throw new a.default;o=!0}else{if(!c)throw new a.default;u=!0}else{if(0!=l)throw new a.default;if(d){if(!c)throw new a.default;r<n?(o=!0,f=!0):(i=!0,u=!0)}else if(c)throw new a.default}if(o){if(i)throw new a.default;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(i&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(f)throw new a.default;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}f&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[[1,8,4,1],[3,6,4,1],[3,4,6,1],[3,2,8,1],[2,6,5,1],[2,2,9,1]],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PAT_A=0,e.FINDER_PAT_B=1,e.FINDER_PAT_C=2,e.FINDER_PAT_D=3,e.FINDER_PAT_E=4,e.FINDER_PAT_F=5,e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C,e.FINDER_PAT_B,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e}(f.default);e.default=w},1325:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(904),o=r(1183),i=r(1331),a=r(1332),u=r(1333),s=r(1334),f=r(1335),l=r(1336),d=r(1337);e.createDecoder=function(t){try{if(t.get(1))return new i.default(t);if(!t.get(2))return new a.default(t);switch(o.default.extractNumericValueFromBitArray(t,1,4)){case 4:return new u.default(t);case 5:return new s.default(t)}switch(o.default.extractNumericValueFromBitArray(t,1,5)){case 12:return new f.default(t);case 13:return new l.default(t)}switch(o.default.extractNumericValueFromBitArray(t,1,7)){case 56:return new d.default(t,"310","11");case 57:return new d.default(t,"320","11");case 58:return new d.default(t,"310","13");case 59:return new d.default(t,"320","13");case 60:return new d.default(t,"310","15");case 61:return new d.default(t,"320","15");case 62:return new d.default(t,"310","17");case 63:return new d.default(t,"320","17")}}catch(e){throw console.log(e),new n.default("unknown decoder: "+t)}}},1326:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r){var n=t.call(this,e)||this;return n.value=r,n}return n(e,t),e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e}(r(1114).default);e.default=o},1327:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(692),i=function(t){function e(e,r,n){var i=t.call(this,e)||this;if(r<0||r>10||n<0||n>10)throw new o.default;return i.firstDigit=r,i.secondDigit=n,i}return n(e,t),e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return 10*this.firstDigit+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit==e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit==e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e}(r(1114).default);e.default=i},1328:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e,r,n){var o=t.call(this,e)||this;return n?(o.remaining=!0,o.remainingValue=o.remainingValue):(o.remaining=!1,o.remainingValue=0),o.newString=r,o}return n(e,t),e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e}(r(1114).default);e.default=o},1329:function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"===typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(642),i=function(){function t(){}return t.parseFieldsInGeneralPurpose=function(e){var r,i,a,u,s,f,l,d;if(!e)return null;if(e.length<2)throw new o.default;var c=e.substring(0,2);try{for(var h=n(t.TWO_DIGIT_DATA_LENGTH),p=h.next();!p.done;p=h.next()){if((m=p.value)[0]===c)return m[1]===t.VARIABLE_LENGTH?t.processVariableAI(2,m[2],e):t.processFixedAI(2,m[1],e)}}catch(I){r={error:I}}finally{try{p&&!p.done&&(i=h.return)&&i.call(h)}finally{if(r)throw r.error}}if(e.length<3)throw new o.default;var _=e.substring(0,3);try{for(var g=n(t.THREE_DIGIT_DATA_LENGTH),v=g.next();!v.done;v=g.next()){if((m=v.value)[0]===_)return m[1]===t.VARIABLE_LENGTH?t.processVariableAI(3,m[2],e):t.processFixedAI(3,m[1],e)}}catch(S){a={error:S}}finally{try{v&&!v.done&&(u=g.return)&&u.call(g)}finally{if(a)throw a.error}}try{for(var w=n(t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),y=w.next();!y.done;y=w.next()){if((m=y.value)[0]===_)return m[1]===t.VARIABLE_LENGTH?t.processVariableAI(4,m[2],e):t.processFixedAI(4,m[1],e)}}catch(O){s={error:O}}finally{try{y&&!y.done&&(f=w.return)&&f.call(w)}finally{if(s)throw s.error}}if(e.length<4)throw new o.default;var E=e.substring(0,4);try{for(var A=n(t.FOUR_DIGIT_DATA_LENGTH),C=A.next();!C.done;C=A.next()){var m;if((m=C.value)[0]===E)return m[1]===t.VARIABLE_LENGTH?t.processVariableAI(4,m[2],e):t.processFixedAI(4,m[1],e)}}catch(T){l={error:T}}finally{try{C&&!C.done&&(d=A.return)&&d.call(A)}finally{if(l)throw l.error}}throw new o.default},t.processFixedAI=function(e,r,n){if(n.length<e)throw new o.default;var i=n.substring(0,e);if(n.length<e+r)throw new o.default;var a=n.substring(e,e+r),u=n.substring(e+r),s="("+i+")"+a,f=t.parseFieldsInGeneralPurpose(u);return null==f?s:s+f},t.processVariableAI=function(e,r,n){var o,i=n.substring(0,e);o=n.length<e+r?n.length:e+r;var a=n.substring(e,o),u=n.substring(o),s="("+i+")"+a,f=t.parseFieldsInGeneralPurpose(u);return null==f?s:s+f},t.VARIABLE_LENGTH=[],t.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",t.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",t.VARIABLE_LENGTH,20],["22",t.VARIABLE_LENGTH,29],["30",t.VARIABLE_LENGTH,8],["37",t.VARIABLE_LENGTH,8],["90",t.VARIABLE_LENGTH,30],["91",t.VARIABLE_LENGTH,30],["92",t.VARIABLE_LENGTH,30],["93",t.VARIABLE_LENGTH,30],["94",t.VARIABLE_LENGTH,30],["95",t.VARIABLE_LENGTH,30],["96",t.VARIABLE_LENGTH,30],["97",t.VARIABLE_LENGTH,3],["98",t.VARIABLE_LENGTH,30],["99",t.VARIABLE_LENGTH,30]],t.THREE_DIGIT_DATA_LENGTH=[["240",t.VARIABLE_LENGTH,30],["241",t.VARIABLE_LENGTH,30],["242",t.VARIABLE_LENGTH,6],["250",t.VARIABLE_LENGTH,30],["251",t.VARIABLE_LENGTH,30],["253",t.VARIABLE_LENGTH,17],["254",t.VARIABLE_LENGTH,20],["400",t.VARIABLE_LENGTH,30],["401",t.VARIABLE_LENGTH,30],["402",17],["403",t.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",t.VARIABLE_LENGTH,20],["421",t.VARIABLE_LENGTH,15],["422",3],["423",t.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",t.VARIABLE_LENGTH,15],["391",t.VARIABLE_LENGTH,18],["392",t.VARIABLE_LENGTH,15],["393",t.VARIABLE_LENGTH,18],["703",t.VARIABLE_LENGTH,30]],t.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",t.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",t.VARIABLE_LENGTH,20],["8003",t.VARIABLE_LENGTH,30],["8004",t.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",t.VARIABLE_LENGTH,30],["8008",t.VARIABLE_LENGTH,12],["8018",18],["8020",t.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",t.VARIABLE_LENGTH,70],["8200",t.VARIABLE_LENGTH,70]],t}();e.default=i},1330:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){e?this.decodedInformation=null:(this.finished=t,this.decodedInformation=e)}return t.prototype.getDecodedInformation=function(){return this.decodedInformation},t.prototype.isFinished=function(){return this.finished},t}();e.default=n},1331:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1076),i=r(749),a=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.parseInformation=function(){var t=new i.default;t.append("(01)");var r=t.length(),n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(n),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,r),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e}(o.default);e.default=a},1332:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(749),i=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.parseInformation=function(){var t=new o.default;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e}(r(1184).default);e.default=i},1333:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.addWeightCode=function(t,e){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e}(r(1185).default);e.default=o},1334:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.addWeightCode=function(t,e){e<1e4?t.append("(3202)"):t.append("(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e}(r(1185).default);e.default=o},1335:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1076),i=r(642),a=r(749),u=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+o.default.GTIN_SIZE)throw new i.default;var t=new a.default;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+o.default.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(r),t.append(")");var n=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+o.default.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(n.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e}(o.default);e.default=u},1336:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1076),i=r(642),a=r(749),u=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+o.default.GTIN_SIZE)throw new i.default;var t=new a.default;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+o.default.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(r),t.append(")");var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+o.default.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);n/100==0&&t.append("0"),n/10==0&&t.append("0"),t.append(n);var u=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+o.default.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(u.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e}(o.default);e.default=u},1337:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=r(1186),i=r(642),a=r(749),u=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.dateCode=n,o.firstAIdigits=r,o}return n(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!=e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new i.default;var t=new a.default;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(r,e.DATE_SIZE);if(38400!=n){t.append("("),t.append(this.dateCode),t.append(")");var o=n%32,i=(n/=32)%12+1,a=n/=12;a/10==0&&t.append("0"),t.append(a),i/10==0&&t.append("0"),t.append(i),o/10==0&&t.append("0"),t.append(o)}},e.prototype.addWeightCode=function(t,e){t.append("("),t.append(this.firstAIdigits),t.append(e/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e}(o.default);e.default=u},1338:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r,n){this.leftchar=t,this.rightchar=e,this.finderpattern=r,this.maybeLast=n}return t.prototype.mayBeLast=function(){return this.maybeLast},t.prototype.getLeftChar=function(){return this.leftchar},t.prototype.getRightChar=function(){return this.rightchar},t.prototype.getFinderPattern=function(){return this.finderpattern},t.prototype.mustBeLast=function(){return null==this.rightchar},t.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"},t.equals=function(e,r){return e instanceof t&&(t.equalsOrNull(e.leftchar,r.leftchar)&&t.equalsOrNull(e.rightchar,r.rightchar)&&t.equalsOrNull(e.finderpattern,r.finderpattern))},t.equalsOrNull=function(e,r){return null===e?null===r:t.equals(e,r)},t.prototype.hashCode=function(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()},t.hashNotNull=function(t){return null===t?0:t.hashCode()},t}();e.default=n},1339:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e,r){this.pairs=t,this.rowNumber=e,this.wasReversed=r}return t.prototype.getPairs=function(){return this.pairs},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.isReversed=function(){return this.wasReversed},t.prototype.isEquivalent=function(t){return this.checkEqualitity(this,t)},t.prototype.toString=function(){return"{ "+this.pairs+" }"},t.prototype.equals=function(e,r){return e instanceof t&&(this.checkEqualitity(e,r)&&e.wasReversed===r.wasReversed)},t.prototype.checkEqualitity=function(t,e){var r;if(t&&e)return t.forEach((function(t,n){e.forEach((function(e){t.getLeftChar().getValue()===e.getLeftChar().getValue()&&t.getRightChar().getValue()===e.getRightChar().getValue()&&t.getFinderPatter().getValue()===e.getFinderPatter().getValue()&&(r=!0)}))})),r},t}();e.default=n},1340:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1021),o=function(){function t(){}return t.buildBitArray=function(t){var e=2*t.length-1;null==t[t.length-1].getRightChar()&&(e-=1);for(var r=12*e,o=new n.default(r),i=0,a=t[0].getRightChar().getValue(),u=11;u>=0;--u)0!=(a&1<<u)&&o.set(i),i++;for(u=1;u<t.length;++u){for(var s=t[u],f=s.getLeftChar().getValue(),l=11;l>=0;--l)0!=(f&1<<l)&&o.set(i),i++;if(null!=s.getRightChar()){var d=s.getRightChar().getValue();for(l=11;l>=0;--l)0!=(d&1<<l)&&o.set(i),i++}}return o},t}();e.default=o},1341:function(t,e,r){var n;n=function(t){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(t,e,r){"use strict";r.r(e);var n=r("react"),o=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),i=function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},a=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r};function u(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)}"undefined"!==typeof window&&(void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(t){var e=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return e?new Promise((function(r,n){e.call(navigator,t,r,n)})):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}));var s=function(t){function e(e){var r=t.call(this,e)||this;return r.canvas=null,r.ctx=null,r.unmounted=!1,r.state={hasUserMedia:!1},r}return o(e,t),e.prototype.componentDidMount=function(){var t=this.state,e=this.props;u()?t.hasUserMedia||this.requestUserMedia():e.onUserMediaError("getUserMedia not supported")},e.prototype.componentDidUpdate=function(t){var e=this.props;if(u()){var r=JSON.stringify(t.audioConstraints)!==JSON.stringify(e.audioConstraints),n=JSON.stringify(t.videoConstraints)!==JSON.stringify(e.videoConstraints),o=t.minScreenshotWidth!==e.minScreenshotWidth,i=t.minScreenshotHeight!==e.minScreenshotHeight;(n||o||i)&&(this.canvas=null,this.ctx=null),(r||n)&&(this.stopAndCleanup(),this.requestUserMedia())}else e.onUserMediaError("getUserMedia not supported")},e.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},e.stopMediaStream=function(t){t&&(t.getVideoTracks&&t.getAudioTracks?(t.getVideoTracks().map((function(e){t.removeTrack(e),e.stop()})),t.getAudioTracks().map((function(e){t.removeTrack(e),e.stop()}))):t.stop())},e.prototype.stopAndCleanup=function(){var t=this.state;t.hasUserMedia&&(e.stopMediaStream(this.stream),t.src&&window.URL.revokeObjectURL(t.src))},e.prototype.getScreenshot=function(t){var e=this.state,r=this.props;if(!e.hasUserMedia)return null;var n=this.getCanvas(t);return n&&n.toDataURL(r.screenshotFormat,r.screenshotQuality)},e.prototype.getCanvas=function(t){var e=this.state,r=this.props;if(!this.video)return null;if(!e.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var n=this.video.videoWidth,o=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var i=n/o;o=(n=r.minScreenshotWidth||this.video.clientWidth)/i,r.minScreenshotHeight&&o<r.minScreenshotHeight&&(n=(o=r.minScreenshotHeight)*i)}this.canvas=document.createElement("canvas"),this.canvas.width=(null===t||void 0===t?void 0:t.width)||n,this.canvas.height=(null===t||void 0===t?void 0:t.height)||o,this.ctx=this.canvas.getContext("2d")}var a=this.ctx,u=this.canvas;return a&&u&&(r.mirrored&&(a.translate(u.width,0),a.scale(-1,1)),a.imageSmoothingEnabled=r.imageSmoothing,a.drawImage(this.video,0,0,(null===t||void 0===t?void 0:t.width)||u.width,(null===t||void 0===t?void 0:t.height)||u.height),r.mirrored&&(a.scale(-1,1),a.translate(-u.width,0))),u},e.prototype.requestUserMedia=function(){var t=this,r=this.props,n=function(n,o){var i={video:"undefined"===typeof o||o};r.audio&&(i.audio="undefined"===typeof n||n),navigator.mediaDevices.getUserMedia(i).then((function(r){t.unmounted?e.stopMediaStream(r):t.handleUserMedia(null,r)})).catch((function(e){t.handleUserMedia(e)}))};if("mediaDevices"in navigator)n(r.audioConstraints,r.videoConstraints);else{var o=function(t){return{optional:[{sourceId:t}]}},i=function(t){var e=t.deviceId;return"string"===typeof e?e:Array.isArray(e)&&e.length>0?e[0]:"object"===typeof e&&e.ideal?e.ideal:null};MediaStreamTrack.getSources((function(t){var e=null,a=null;t.forEach((function(t){"audio"===t.kind?e=t.id:"video"===t.kind&&(a=t.id)}));var u=i(r.audioConstraints);u&&(e=u);var s=i(r.videoConstraints);s&&(a=s),n(o(e),o(a))}))}},e.prototype.handleUserMedia=function(t,e){var r=this.props;if(t||!e)return this.setState({hasUserMedia:!1}),void r.onUserMediaError(t);this.stream=e;try{this.video&&(this.video.srcObject=e),this.setState({hasUserMedia:!0})}catch(n){this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(e)})}r.onUserMedia(e)},e.prototype.render=function(){var t=this,e=this.state,r=this.props,o=r.audio,u=(r.forceScreenshotSourceSize,r.onUserMedia,r.onUserMediaError,r.screenshotFormat,r.screenshotQuality,r.minScreenshotWidth,r.minScreenshotHeight,r.audioConstraints,r.videoConstraints,r.imageSmoothing,r.mirrored),s=r.style,f=void 0===s?{}:s,l=a(r,["audio","forceScreenshotSourceSize","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style"]),d=u?i(i({},f),{transform:(f.transform||"")+" scaleX(-1)"}):f;return n.createElement("video",i({autoPlay:!0,src:e.src,muted:o,playsInline:!0,ref:function(e){t.video=e},style:d},l))},e.defaultProps={audio:!0,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},e}(n.Component);e.default=s},react:function(e,r){e.exports=t}}).default},t.exports=n(r(0))},1386:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const o=n(r(1261));e.default=o.default},642:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.getNotFoundInstance=function(){return new e},e}(r(763).default);e.default=o},654:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o},692:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.getFormatInstance=function(){return new e},e}(r(763).default);e.default=o},693:function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.AZTEC=0]="AZTEC",t[t.CODABAR=1]="CODABAR",t[t.CODE_39=2]="CODE_39",t[t.CODE_93=3]="CODE_93",t[t.CODE_128=4]="CODE_128",t[t.DATA_MATRIX=5]="DATA_MATRIX",t[t.EAN_8=6]="EAN_8",t[t.EAN_13=7]="EAN_13",t[t.ITF=8]="ITF",t[t.MAXICODE=9]="MAXICODE",t[t.PDF_417=10]="PDF_417",t[t.QR_CODE=11]="QR_CODE",t[t.RSS_14=12]="RSS_14",t[t.RSS_EXPANDED=13]="RSS_EXPANDED",t[t.UPC_A=14]="UPC_A",t[t.UPC_E=15]="UPC_E",t[t.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"}(n||(n={})),e.default=n},736:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(820),o=r(1158),i=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.equals=function(e){if(e instanceof t){var r=e;return this.x===r.x&&this.y===r.y}return!1},t.prototype.hashCode=function(){return 31*o.default.floatToIntBits(this.x)+o.default.floatToIntBits(this.y)},t.prototype.toString=function(){return"("+this.x+","+this.y+")"},t.orderBestPatterns=function(t){var e,r,n,o=this.distance(t[0],t[1]),i=this.distance(t[1],t[2]),a=this.distance(t[0],t[2]);if(i>=o&&i>=a?(r=t[0],e=t[1],n=t[2]):a>=i&&a>=o?(r=t[1],e=t[0],n=t[2]):(r=t[2],e=t[0],n=t[1]),this.crossProductZ(e,r,n)<0){var u=e;e=n,n=u}t[0]=e,t[1]=r,t[2]=n},t.distance=function(t,e){return n.default.distance(t.x,t.y,e.x,e.y)},t.crossProductZ=function(t,e,r){var n=e.x,o=e.y;return(r.x-n)*(t.y-o)-(r.y-o)*(t.x-n)},t}();e.default=i},749:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1069),o=function(){function t(t){void 0===t&&(t=""),this.value=t}return t.prototype.enableDecoding=function(t){return this.encoding=t,this},t.prototype.append=function(t){return"string"===typeof t?this.value+=t.toString():this.encoding?this.value+=n.default.castAsNonUtf8Char(t,this.encoding):this.value+=String.fromCharCode(t),this},t.prototype.length=function(){return this.value.length},t.prototype.charAt=function(t){return this.value.charAt(t)},t.prototype.deleteCharAt=function(t){this.value=this.value.substr(0,t)+this.value.substring(t+1)},t.prototype.setCharAt=function(t,e){this.value=this.value.substr(0,t)+e+this.value.substr(t+1)},t.prototype.substring=function(t,e){return this.value.substring(t,e)},t.prototype.setLengthToZero=function(){this.value=""},t.prototype.toString=function(){return this.value},t.prototype.insert=function(t,e){this.value=this.value.substr(0,t)+e+this.value.substr(t+e.length)},t}();e.default=o},752:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.arraycopy=function(t,e,r,n,o){for(;o--;)r[n++]=t[e++]},t.currentTimeMillis=function(){return Date.now()},t}();e.default=n},763:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){void 0===e&&(e=void 0);var r=t.call(this,e)||this;return r.message=e,r}return n(e,t),e}(r(1264).CustomError);e.default=o},764:function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.OTHER=0]="OTHER",t[t.PURE_BARCODE=1]="PURE_BARCODE",t[t.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",t[t.TRY_HARDER=3]="TRY_HARDER",t[t.CHARACTER_SET=4]="CHARACTER_SET",t[t.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",t[t.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",t[t.ASSUME_GS1=7]="ASSUME_GS1",t[t.RETURN_CODABAR_START_END=8]="RETURN_CODABAR_START_END",t[t.NEED_RESULT_POINT_CALLBACK=9]="NEED_RESULT_POINT_CALLBACK",t[t.ALLOWED_EAN_EXTENSIONS=10]="ALLOWED_EAN_EXTENSIONS"}(n||(n={})),e.default=n},791:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(752),o=function(){function t(t,e,r,o,i,a){void 0===r&&(r=null==e?0:8*e.length),void 0===a&&(a=n.default.currentTimeMillis()),this.text=t,this.rawBytes=e,this.numBits=r,this.resultPoints=o,this.format=i,this.timestamp=a,this.text=t,this.rawBytes=e,this.numBits=void 0===r||null===r?null===e||void 0===e?0:8*e.length:r,this.resultPoints=o,this.format=i,this.resultMetadata=null,this.timestamp=void 0===a||null===a?n.default.currentTimeMillis():a}return t.prototype.getText=function(){return this.text},t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.getBarcodeFormat=function(){return this.format},t.prototype.getResultMetadata=function(){return this.resultMetadata},t.prototype.putMetadata=function(t,e){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(t,e)},t.prototype.putAllMetadata=function(t){null!==t&&(null===this.resultMetadata?this.resultMetadata=t:this.resultMetadata=new Map(t))},t.prototype.addResultPoints=function(t){var e=this.resultPoints;if(null===e)this.resultPoints=t;else if(null!==t&&t.length>0){var r=new Array(e.length+t.length);n.default.arraycopy(e,0,r,0,e.length),n.default.arraycopy(t,0,r,e.length,t.length),this.resultPoints=r}},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.toString=function(){return this.text},t}();e.default=o},820:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.prototype.MathUtils=function(){},t.round=function(t){return NaN===t?0:t<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:t>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:t+(t<0?-.5:.5)|0},t.distance=function(t,e,r,n){var o=t-r,i=e-n;return Math.sqrt(o*o+i*i)},t.sum=function(t){for(var e=0,r=0,n=t.length;r!==n;r++){e+=t[r]}return e},t}();e.default=n},824:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.getChecksumInstance=function(){return new e},e}(r(763).default);e.default=o},825:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1021),o=r(752),i=r(1022),a=r(749),u=r(654),s=function(){function t(t,e,r,n){if(this.width=t,this.height=e,this.rowSize=r,this.bits=n,void 0!==e&&null!==e||(e=t),this.height=e,t<1||e<1)throw new u.default("Both dimensions must be greater than 0");void 0!==r&&null!==r||(r=Math.floor((t+31)/32)),this.rowSize=r,void 0!==n&&null!==n||(this.bits=new Int32Array(this.rowSize*this.height))}return t.parseFromBooleanArray=function(e){for(var r=e.length,n=e[0].length,o=new t(n,r),i=0;i<r;i++)for(var a=e[i],u=0;u<n;u++)a[u]&&o.set(u,i);return o},t.parseFromString=function(e,r,n){if(null===e)throw new u.default("stringRepresentation cannot be null");for(var o=new Array(e.length),i=0,a=0,s=-1,f=0,l=0;l<e.length;)if("\n"===e.charAt(l)||"\r"===e.charAt(l)){if(i>a){if(-1===s)s=i-a;else if(i-a!==s)throw new u.default("row lengths do not match");a=i,f++}l++}else if(e.substring(l,l+r.length)===r)l+=r.length,o[i]=!0,i++;else{if(e.substring(l,l+n.length)!==n)throw new u.default("illegal character encountered: "+e.substring(l));l+=n.length,o[i]=!1,i++}if(i>a){if(-1===s)s=i-a;else if(i-a!==s)throw new u.default("row lengths do not match");f++}for(var d=new t(s,f),c=0;c<i;c++)o[c]&&d.set(Math.floor(c%s),Math.floor(c/s));return d},t.prototype.get=function(t,e){var r=e*this.rowSize+Math.floor(t/32);return 0!==(this.bits[r]>>>(31&t)&1)},t.prototype.set=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]|=1<<(31&t)&4294967295},t.prototype.unset=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]&=~(1<<(31&t)&4294967295)},t.prototype.flip=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]^=1<<(31&t)&4294967295},t.prototype.xor=function(t){if(this.width!==t.getWidth()||this.height!==t.getHeight()||this.rowSize!==t.getRowSize())throw new u.default("input matrix dimensions do not match");for(var e=new n.default(Math.floor(this.width/32)+1),r=this.rowSize,o=this.bits,i=0,a=this.height;i<a;i++)for(var s=i*r,f=t.getRow(i,e).getBitArray(),l=0;l<r;l++)o[s+l]^=f[l]},t.prototype.clear=function(){for(var t=this.bits,e=t.length,r=0;r<e;r++)t[r]=0},t.prototype.setRegion=function(t,e,r,n){if(e<0||t<0)throw new u.default("Left and top must be nonnegative");if(n<1||r<1)throw new u.default("Height and width must be at least 1");var o=t+r,i=e+n;if(i>this.height||o>this.width)throw new u.default("The region must fit inside the matrix");for(var a=this.rowSize,s=this.bits,f=e;f<i;f++)for(var l=f*a,d=t;d<o;d++)s[l+Math.floor(d/32)]|=1<<(31&d)&4294967295},t.prototype.getRow=function(t,e){null===e||void 0===e||e.getSize()<this.width?e=new n.default(this.width):e.clear();for(var r=this.rowSize,o=this.bits,i=t*r,a=0;a<r;a++)e.setBulk(32*a,o[i+a]);return e},t.prototype.setRow=function(t,e){o.default.arraycopy(e.getBitArray(),0,this.bits,t*this.rowSize,this.rowSize)},t.prototype.rotate180=function(){for(var t=this.getWidth(),e=this.getHeight(),r=new n.default(t),o=new n.default(t),i=0,a=Math.floor((e+1)/2);i<a;i++)r=this.getRow(i,r),o=this.getRow(e-1-i,o),r.reverse(),o.reverse(),this.setRow(i,o),this.setRow(e-1-i,r)},t.prototype.getEnclosingRectangle=function(){for(var t=this.width,e=this.height,r=this.rowSize,n=this.bits,o=t,i=e,a=-1,u=-1,s=0;s<e;s++)for(var f=0;f<r;f++){var l=n[s*r+f];if(0!==l){if(s<i&&(i=s),s>u&&(u=s),32*f<o){for(var d=0;0===(l<<31-d&4294967295);)d++;32*f+d<o&&(o=32*f+d)}if(32*f+31>a){for(d=31;l>>>d===0;)d--;32*f+d>a&&(a=32*f+d)}}}return a<o||u<i?null:Int32Array.from([o,i,a-o+1,u-i+1])},t.prototype.getTopLeftOnBit=function(){for(var t=this.rowSize,e=this.bits,r=0;r<e.length&&0===e[r];)r++;if(r===e.length)return null;for(var n=r/t,o=r%t*32,i=e[r],a=0;0===(i<<31-a&4294967295);)a++;return o+=a,Int32Array.from([o,n])},t.prototype.getBottomRightOnBit=function(){for(var t=this.rowSize,e=this.bits,r=e.length-1;r>=0&&0===e[r];)r--;if(r<0)return null;for(var n=Math.floor(r/t),o=32*Math.floor(r%t),i=e[r],a=31;i>>>a===0;)a--;return o+=a,Int32Array.from([o,n])},t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.getRowSize=function(){return this.rowSize},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.width===r.width&&this.height===r.height&&this.rowSize===r.rowSize&&i.default.equals(this.bits,r.bits)},t.prototype.hashCode=function(){var t=this.width;return t=31*(t=31*(t=31*(t=31*t+this.width)+this.height)+this.rowSize)+i.default.hashCode(this.bits)},t.prototype.toString=function(t,e,r){return void 0===t&&(t="X "),void 0===e&&(e="  "),void 0===r&&(r="\n"),this.buildToString(t,e,r)},t.prototype.buildToString=function(t,e,r){for(var n=new a.default,o=0,i=this.height;o<i;o++){for(var u=0,s=this.width;u<s;u++)n.append(this.get(u,o)?t:e);n.append(r)}return n.toString()},t.prototype.clone=function(){return new t(this.width,this.height,this.rowSize,this.bits.slice())},t}();e.default=s},898:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.numberOfTrailingZeros=function(t){var e;if(0===t)return 32;var r=31;return 0!==(e=t<<16)&&(r-=16,t=e),0!==(e=t<<8)&&(r-=8,t=e),0!==(e=t<<4)&&(r-=4,t=e),0!==(e=t<<2)&&(r-=2,t=e),r-(t<<1>>>31)},t.numberOfLeadingZeros=function(t){if(0===t)return 32;var e=1;return t>>>16===0&&(e+=16,t<<=16),t>>>24===0&&(e+=8,t<<=8),t>>>28===0&&(e+=4,t<<=4),t>>>30===0&&(e+=2,t<<=2),e-=t>>>31},t.toHexString=function(t){return t.toString(16)},t.toBinaryString=function(t){return String(parseInt(String(t),2))},t.bitCount=function(t){return t=(t=(858993459&(t-=t>>>1&1431655765))+(t>>>2&858993459))+(t>>>4)&252645135,t+=t>>>8,63&(t+=t>>>16)},t.truncDivision=function(t,e){return Math.trunc(t/e)},t.parseInt=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t.MIN_VALUE_32_BITS=-2147483648,t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}();e.default=n},899:function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.OTHER=0]="OTHER",t[t.ORIENTATION=1]="ORIENTATION",t[t.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",t[t.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",t[t.ISSUE_NUMBER=4]="ISSUE_NUMBER",t[t.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",t[t.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",t[t.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",t[t.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",t[t.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",t[t.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"}(n||(n={})),e.default=n},900:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1021),o=r(764),i=r(899),a=r(736),u=r(642),s=function(){function t(){}return t.prototype.decode=function(t,e){try{return this.doDecode(t,e)}catch(h){if(e&&!0===e.get(o.default.TRY_HARDER)&&t.isRotateSupported()){var r=t.rotateCounterClockwise(),n=this.doDecode(r,e),s=n.getResultMetadata(),f=270;null!==s&&!0===s.get(i.default.ORIENTATION)&&(f+=s.get(i.default.ORIENTATION)%360),n.putMetadata(i.default.ORIENTATION,f);var l=n.getResultPoints();if(null!==l)for(var d=r.getHeight(),c=0;c<l.length;c++)l[c]=new a.default(d-l[c].getY()-1,l[c].getX());return n}throw new u.default}},t.prototype.reset=function(){},t.prototype.doDecode=function(t,e){var r,s=t.getWidth(),f=t.getHeight(),l=new n.default(s),d=e&&!0===e.get(o.default.TRY_HARDER),c=Math.max(1,f>>(d?8:5));r=d?f:15;for(var h=Math.trunc(f/2),p=0;p<r;p++){var _=Math.trunc((p+1)/2),g=h+c*(0===(1&p)?_:-_);if(g<0||g>=f)break;try{l=t.getBlackRow(g,l)}catch(A){continue}for(var v=function(t){if(1===t&&(l.reverse(),e&&!0===e.get(o.default.NEED_RESULT_POINT_CALLBACK))){var r=new Map;e.forEach((function(t,e){return r.set(e,t)})),r.delete(o.default.NEED_RESULT_POINT_CALLBACK),e=r}try{var n=w.decodeRow(g,l,e);if(1===t){n.putMetadata(i.default.ORIENTATION,180);var u=n.getResultPoints();null!==u&&(u[0]=new a.default(s-u[0].getX()-1,u[0].getY()),u[1]=new a.default(s-u[1].getX()-1,u[1].getY()))}return{value:n}}catch(f){}},w=this,y=0;y<2;y++){var E=v(y);if("object"===typeof E)return E.value}}throw new u.default},t.recordPattern=function(t,e,r){for(var n=r.length,o=0;o<n;o++)r[o]=0;var i=t.getSize();if(e>=i)throw new u.default;for(var a=!t.get(e),s=0,f=e;f<i;){if(t.get(f)!==a)r[s]++;else{if(++s===n)break;r[s]=1,a=!a}f++}if(s!==n&&(s!==n-1||f!==i))throw new u.default},t.recordPatternInReverse=function(e,r,n){for(var o=n.length,i=e.get(r);r>0&&o>=0;)e.get(--r)!==i&&(o--,i=!i);if(o>=0)throw new u.default;t.recordPattern(e,r+1,n)},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return Number.POSITIVE_INFINITY;var u=o/i;r*=u;for(var s=0,f=0;f<n;f++){var l=t[f],d=e[f]*u,c=l>d?l-d:d-l;if(c>r)return Number.POSITIVE_INFINITY;s+=c}return s/o},t}();e.default=s},904:function(t,e,r){"use strict";var n=this&&this.__extends||function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(r(763).default);e.default=o}}]);
//# sourceMappingURL=16.e8af8afd.chunk.js.map