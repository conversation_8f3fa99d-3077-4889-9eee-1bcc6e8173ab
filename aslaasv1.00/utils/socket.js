const socketio = require('socket.io');
const activeUsers = new Set();
const admin = require("firebase-admin");

const UserModel = require('../models/user');
const LogModel = require('../models/log');
const DeviceModel = require('../models/device');
const user = require('../models/user');
const DeviceMessageLogModel = require('../models/deviceMessageLog');
const logger = require('./logger');



const sendMessageToChannel = async (deviceNumber, msg, from, isCommandResponse = false) => {
  let message = null;

  if (!msg || typeof msg.payload !== 'string') {
    logger.debug('Invalid message format:', msg);
    return;
  }

  try {
    // Sanitize the payload to handle potential encoding issues
    const sanitizedPayload = msg.payload
      .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
      .trim();
    
    try {
      // Try to parse as JSON, but handle errors gracefully
      message = JSON.parse(sanitizedPayload);
    } catch (parseError) {
      console.log(`Cannot parse message as JSON for device ${deviceNumber}, using raw payload`);
      // If parsing fails, use the raw payload instead
      message = { rawPayload: sanitizedPayload };
    }
  } catch (error) {
    console.error(`Error processing message for device ${deviceNumber}:`, error);
    message = { error: "Message processing failed" };
  }

  // Message formatting
  if (Array.isArray(msg.payload)) {
      message = msg.payload[0]
          .replace(" N,", ",")
          .replace(" E,", ",")
          .replace(" N", "")
          .replace(" E", "")
          .replace(/\s,\s,/g, ",")
          .replace(/\s,,/g, ",")
          .replace("(", "{")
          .replace("\x1B)", "}")
          .replace(" ,,", ",");
  } else {
      message = msg.payload
          .replace(" N,", ",")
          .replace(" E,", ",")
          .replace(" N", "")
          .replace(" E", "")
          .replace(/\s,\s,/g, ",")
          .replace(/\s,,/g, ",")
          .replace("(", "{")
          .replace("\x1B)", "}")
          .replace(" ,,", ",");
  }

  try {
      // Vehicle processing logic removed
  } catch (err) {
      console.log(err);
  }

  // Notifying active users
  activeUsers.forEach(async (user) => {
      if (user.device && user.device.deviceNumber === deviceNumber) {
          const user_ = await UserModel.findOne({ phoneNumber: user.phoneNumber });

          if (user.socket.connected) {
            const data = { payload: message, from, from_client_id: deviceNumber };
            user.socket.emit("data-received", { ...data });

              // Only log for regular messages, not command responses
              if (!isCommandResponse) {
                logger.debug("Device message log is saved");
              }
          }
      }
  });

  // Sending FCM notification
  const device = await DeviceModel.findOne({ deviceNumber });
  if (device && device.fmctoken) {
      const payload = typeof msg.payload === 'string' ? JSON.parse(msg.payload) : msg.payload;
      const { sta, motion, Speed } = payload;
      let title = '';
      let body = '';
      // Notification conditions
      // (Same as before)
  } else {
      console.log("No FCM token found for device", deviceNumber);
  }
};


let io = null;

const initialize = async(server) => {

    io = socketio(server, {
        cors: {
            origin: "*",
            methods: ["GET", "POST"]
        },
        transports: ['polling', 'websocket']
    });
    io.on("connection", (client) => {
            client.on("logined", data => {
            data.socket = client;
            let find = false;
            activeUsers.forEach(user => {
                if (user.phoneNumber === data.phoneNumber) {
                    user.socket = client;
                    find = true;
                }
            });
            if (!find)
                activeUsers.add(data);
        });
        client.on("disconnect", (reason) => {
            console.log("disonnect ", reason);
            activeUsers.forEach((user) => {
                if (user.socket != null && (user.socket.id == client.id)) {
                    console.log("delete user");
                    activeUsers.delete(user);
                }
            })

        })
    });
};

const getSocketIO = () => {
    return io;
};

const emitToAdmins = (event, data) => {
    if (io) {
        // Emit to all connected admin clients
        io.emit(event, data);
        logger.debug(`Socket.IO: Emitted ${event} to all clients`);
    }
};

module.exports = {
    initialize,
    sendMessageToChannel,
    getSocketIO,
    emitToAdmins
}
