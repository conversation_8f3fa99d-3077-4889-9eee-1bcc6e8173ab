const express = require("express");
const mongoose = require("mongoose");
const bodyParser = require("body-parser");
const passport = require("passport");
const cors = require("cors");
const path = require("path");
const fs = require('fs');
require("dotenv").config();
const { initialize } = require('./utils/socket');
const firebaseAdmin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');
const logger = require('./utils/logger');

try {
    firebaseAdmin.initializeApp({
        credential: firebaseAdmin.credential.cert(serviceAccount),
        //databaseURL: 'https://<DATABASE_NAME>.firebaseio.com'
    });
    logger.info('Firebase Admin SDK initialized successfully!');
} catch (error) {
    logger.error('Firebase Admin SDK initialization failed:', error);
}

const { createServer } = require('http');
// api routers
const auth = require("./routes/api/auth");
const license = require('./routes/api/license');
const device = require("./routes/api/device");

const mtqq = require('./routes/api/mtqq');
const log = require('./routes/api/log');
const admin = require('./routes/api/admin');
const download = require('./routes/api/download');
const qpay = require('./routes/api/PaymentHookRoute');
const sslroot = require('./routes/api/ssl');
const twoFactor = require('./routes/api/twoFactor');
const { initializeMqttClient, parseJsonFromPayload } = require("./utils/mqtt");
const scheduledCommand = require('./routes/api/scheduledCommand');
const app = express();
// Bodyparser middleware
app.use(cors())
app.use(
    bodyParser.urlencoded({
        extended: false
    })
);
app.use(bodyParser.json());
// DB Config
// const DATABASE_CONNECTION = process.env.DATABASE_URL;
//  const DATABASE_CONNECTION = process.env.DATABASE_ATLAS_URL;
const DATABASE_CONNECTION = process.env.DATABASE_LOCAL_URL;

// Connect to MongoDB
const option = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    family: 4,
    //ssl:true,
}

mongoose
    .connect(
        DATABASE_CONNECTION,
        option
    )
    .then(() => {
        logger.info("MongoDB successfully connected");
        initializeMqttClient();
    })
    .catch(err => logger.error("MongoDB connection failed:", err));

const assetFolder = path.resolve(__dirname, './build/');
app.use('/uploads', express.static('uploads'));
app.use('/images', express.static('uploads/images'));
app.use('/app', express.static('uploads/app'));
app.use('/direct-app-download', express.static('uploads/app'));

// Passport middleware
app.use(passport.initialize());
// Passport config
require("./config/passport")(passport);
// Routes
app.get('/test-file-exists', (req, res) => {
    const filePath = path.resolve(__dirname, './uploads/app/android-app.apk');
    const exists = fs.existsSync(filePath);
    res.json({
        fileExists: exists,
        filePath: filePath,
        directoryContents: exists ? null : fs.readdirSync(path.dirname(filePath))
    });
});

app.get('/test-download', (req, res) => {
    try {
        const filePath = path.resolve(__dirname, './uploads/app/android-app.apk');
        if (fs.existsSync(filePath)) {
            // Set appropriate headers
            res.setHeader('Content-Type', 'application/vnd.android.package-archive');
            res.setHeader('Content-Disposition', 'attachment; filename=android-app.apk');
            
            // Stream the file instead of using res.download
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        } else {
            res.status(404).send(`File not found at: ${filePath}`);
        }
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.use("/api/admin", admin);
app.use("/api/auth", auth);
app.use("/api/2fa", twoFactor);
app.use("/api/device", device);
app.use('/api/license', license);
app.use('/api/hook/payment', qpay);
app.use("/.well-known/", sslroot);

app.use("/mqtt", mtqq);
app.use("/api/log", log);
app.use("/api/scheduledCommand", scheduledCommand);
app.use("/api/download", download);

// Static file serving should come after API routes
app.use(express.static(assetFolder));

// Catch-all route for React app should be last
app.get("*", (req, res) => {
    // Only serve index.html for non-API routes
    if (!req.path.startsWith('/api/')) {
        res.sendFile(path.join(assetFolder, 'index.html'));
    } else {
        res.status(404).json({ error: "API endpoint not found" });
    }
});

const { scheduleDailyLicenseExpiry } = require('./utils/licenseExpiryScheduler');
scheduleDailyLicenseExpiry();
const port = process.env.PORT || 5000; // process.env.port is Heroku's port if you choose to deploy the app there
const host = process.env.HOST || '0.0.0.0'; // Bind to all interfaces to accept external connections
const server = app.listen(port, host, () => {
    logger.info(`Server up and running on ${host}:${port}!`);
    logger.info(`Server accessible from external devices at: http://<your-ip>:${port}`);
});


initialize(server);
